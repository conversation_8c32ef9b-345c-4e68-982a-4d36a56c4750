!function(t){var n={};function o(e){if(n[e])return n[e].exports;var s=n[e]={i:e,l:!1,exports:{}};return t[e].call(s.exports,s,s.exports,o),s.l=!0,s.exports}o.m=t,o.c=n,o.d=function(t,n,e){o.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:e})},o.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},o.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(n,"a",n),n},o.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},o.p="",o(o.s=2)}([function(t,n){function o(t){32!=(t.keyCode?t.keyCode:t.charCode)?/[a-zA-Z]/.test(t.key)||t.preventDefault():0==t.target.value.length&&t.preventDefault()}$("#contact-form").length&&$("#contact-form").validate({submitHandler:function(t){var n=$(t).find('button[type="submit"]');$("#form-result").remove(),n.before('<div id="form-result" class="alert alert-success" role="alert" style="display: none;"></div>');var o=n.html();n.html(n.prop("disabled",!0).data("loading-text")),$(t).ajaxSubmit({dataType:"json",success:function(e){"true"==e.status&&$(t).find(".form-control").val(""),n.prop("disabled",!1).html(o),$("#form-result").html(e.message).fadeIn("slow"),setTimeout(function(){$("#form-result").fadeOut("slow")},6e3)}})}}),document.addEventListener("DOMContentLoaded",function(){document.querySelector("#nam").addEventListener("keydown",function(t){o(t)}),document.querySelector("#ph_no").addEventListener("keypress",function(t){!function(t){/^\+?\d*$/.test(t.target.value+t.key)||t.preventDefault()}(t)}),document.querySelector("#subject").addEventListener("keydown",function(t){o(t)})})},function(t,n){!function(t){"use strict";var n=0;function o(n,o){return t.map(n,function(t){return function(t,n){return t+".touchspin_"+n}(t,o)})}t.fn.TouchSpin=function(e){if("destroy"!==e){var s={min:0,max:100,initval:"",replacementval:"",step:1,decimals:0,stepinterval:100,forcestepdivisibility:"round",stepintervaldelay:500,verticalbuttons:!1,verticalupclass:"glyphicon glyphicon-chevron-up",verticaldownclass:"glyphicon glyphicon-chevron-down",prefix:"",postfix:"",prefix_extraclass:"",postfix_extraclass:"",booster:!0,boostat:10,maxboostedstep:!1,mousewheel:!0,buttondown_class:"btn btn-default",buttonup_class:"btn btn-default",buttondown_txt:"-",buttonup_txt:"+"},a={min:"min",max:"max",initval:"init-val",replacementval:"replacement-val",step:"step",decimals:"decimals",stepinterval:"step-interval",verticalbuttons:"vertical-buttons",verticalupclass:"vertical-up-class",verticaldownclass:"vertical-down-class",forcestepdivisibility:"force-step-divisibility",stepintervaldelay:"step-interval-delay",prefix:"prefix",postfix:"postfix",prefix_extraclass:"prefix-extra-class",postfix_extraclass:"postfix-extra-class",booster:"booster",boostat:"boostat",maxboostedstep:"max-boosted-step",mousewheel:"mouse-wheel",buttondown_class:"button-down-class",buttonup_class:"button-up-class",buttondown_txt:"button-down-txt",buttonup_txt:"button-up-txt"};return this.each(function(){var i,u,p,r,c,l,d,f,v=t(this),b=v.data(),h=0,m=!1;function x(){var t,n,o;""!==(t=v.val())?i.decimals>0&&"."===t||(n=parseFloat(t),isNaN(n)&&(n=""!==i.replacementval?i.replacementval:0),o=n,n.toString()!==t&&(o=n),n<i.min&&(o=i.min),n>i.max&&(o=i.max),o=function(t){switch(i.forcestepdivisibility){case"round":return(Math.round(t/i.step)*i.step).toFixed(i.decimals);case"floor":return(Math.floor(t/i.step)*i.step).toFixed(i.decimals);case"ceil":return(Math.ceil(t/i.step)*i.step).toFixed(i.decimals);default:return t}}(o),Number(t).toString()!==o.toString()&&(v.val(o),v.trigger("change"))):""!==i.replacementval&&(v.val(i.replacementval),v.trigger("change"))}function g(){if(i.booster){var t=Math.pow(2,Math.floor(h/i.boostat))*i.step;return i.maxboostedstep&&t>i.maxboostedstep&&(t=i.maxboostedstep,r=Math.round(r/t)*t),Math.max(i.step,t)}return i.step}function w(){x(),r=parseFloat(p.input.val()),isNaN(r)&&(r=0);var t=r,n=g();(r+=n)>i.max&&(r=i.max,v.trigger("touchspin.on.max"),C()),p.input.val(Number(r).toFixed(i.decimals)),t!==r&&v.trigger("change")}function y(){x(),r=parseFloat(p.input.val()),isNaN(r)&&(r=0);var t=r,n=g();(r-=n)<i.min&&(r=i.min,v.trigger("touchspin.on.min"),C()),p.input.val(r.toFixed(i.decimals)),t!==r&&v.trigger("change")}function _(){C(),h=0,m="down",v.trigger("touchspin.on.startspin"),v.trigger("touchspin.on.startdownspin"),d=setTimeout(function(){c=setInterval(function(){h++,y()},i.stepinterval)},i.stepintervaldelay)}function k(){C(),h=0,m="up",v.trigger("touchspin.on.startspin"),v.trigger("touchspin.on.startupspin"),f=setTimeout(function(){l=setInterval(function(){h++,w()},i.stepinterval)},i.stepintervaldelay)}function C(){switch(clearTimeout(d),clearTimeout(f),clearInterval(c),clearInterval(l),m){case"up":v.trigger("touchspin.on.stopupspin"),v.trigger("touchspin.on.stopspin");break;case"down":v.trigger("touchspin.on.stopdownspin"),v.trigger("touchspin.on.stopspin")}h=0,m=!1}!function(){if(v.data("alreadyinitialized"))return;if(v.data("alreadyinitialized",!0),n+=1,v.data("spinnerid",n),!v.is("input"))return void console.log("Must be an input.");i=t.extend({},s,b,(r={},t.each(a,function(t,n){var o="bts-"+n;v.is("[data-"+o+"]")&&(r[t]=v.data(o))}),r),e),""!==i.initval&&""===v.val()&&v.val(i.initval),x(),function(){var n=v.val(),o=v.parent();""!==n&&(n=Number(n).toFixed(i.decimals));v.data("initvalue",n).val(n),v.addClass("form-control"),o.hasClass("input-group")?function(n){n.addClass("bootstrap-touchspin");var o,e,s=v.prev(),a=v.next(),p='<span class="input-group-addon bootstrap-touchspin-prefix">'+i.prefix+"</span>",r='<span class="input-group-addon bootstrap-touchspin-postfix">'+i.postfix+"</span>";s.hasClass("input-group-btn")?(o='<button class="'+i.buttondown_class+' bootstrap-touchspin-down" type="button">'+i.buttondown_txt+"</button>",s.append(o)):(o='<span class="input-group-btn"><button class="'+i.buttondown_class+' bootstrap-touchspin-down" type="button">'+i.buttondown_txt+"</button></span>",t(o).insertBefore(v));a.hasClass("input-group-btn")?(e='<button class="'+i.buttonup_class+' bootstrap-touchspin-up" type="button">'+i.buttonup_txt+"</button>",a.prepend(e)):(e='<span class="input-group-btn"><button class="'+i.buttonup_class+' bootstrap-touchspin-up" type="button">'+i.buttonup_txt+"</button></span>",t(e).insertAfter(v));t(p).insertBefore(v),t(r).insertAfter(v),u=n}(o):function(){var n;n=i.verticalbuttons?'<div class="input-group bootstrap-touchspin"><span class="input-group-addon bootstrap-touchspin-prefix">'+i.prefix+'</span><span class="input-group-addon bootstrap-touchspin-postfix">'+i.postfix+'</span><span class="input-group-btn-vertical"><button class="'+i.buttondown_class+' bootstrap-touchspin-up" type="button"><i class="'+i.verticalupclass+'"></i></button><button class="'+i.buttonup_class+' bootstrap-touchspin-down" type="button"><i class="'+i.verticaldownclass+'"></i></button></span></div>':'<div class="input-group bootstrap-touchspin"><span class="input-group-btn"><button class="'+i.buttondown_class+' bootstrap-touchspin-down" type="button">'+i.buttondown_txt+'</button></span><span class="input-group-addon bootstrap-touchspin-prefix">'+i.prefix+'</span><span class="input-group-addon bootstrap-touchspin-postfix">'+i.postfix+'</span><span class="input-group-btn"><button class="'+i.buttonup_class+' bootstrap-touchspin-up" type="button">'+i.buttonup_txt+"</button></span></div>";u=t(n).insertBefore(v),t(".bootstrap-touchspin-prefix",u).after(v),v.hasClass("input-sm")?u.addClass("input-group-sm"):v.hasClass("input-lg")&&u.addClass("input-group-lg")}()}(),p={down:t(".bootstrap-touchspin-down",u),up:t(".bootstrap-touchspin-up",u),input:t("input",u),prefix:t(".bootstrap-touchspin-prefix",u).addClass(i.prefix_extraclass),postfix:t(".bootstrap-touchspin-postfix",u).addClass(i.postfix_extraclass)},function(){""===i.prefix&&p.prefix.hide();""===i.postfix&&p.postfix.hide()}(),v.on("keydown",function(t){var n=t.keyCode||t.which;38===n?("up"!==m&&(w(),k()),t.preventDefault()):40===n&&("down"!==m&&(y(),_()),t.preventDefault())}),v.on("keyup",function(t){var n=t.keyCode||t.which;38===n?C():40===n&&C()}),v.on("blur",function(){x()}),p.down.on("keydown",function(t){var n=t.keyCode||t.which;32!==n&&13!==n||("down"!==m&&(y(),_()),t.preventDefault())}),p.down.on("keyup",function(t){var n=t.keyCode||t.which;32!==n&&13!==n||C()}),p.up.on("keydown",function(t){var n=t.keyCode||t.which;32!==n&&13!==n||("up"!==m&&(w(),k()),t.preventDefault())}),p.up.on("keyup",function(t){var n=t.keyCode||t.which;32!==n&&13!==n||C()}),p.down.on("mousedown.touchspin",function(t){p.down.off("touchstart.touchspin"),v.is(":disabled")||(y(),_(),t.preventDefault(),t.stopPropagation())}),p.down.on("touchstart.touchspin",function(t){p.down.off("mousedown.touchspin"),v.is(":disabled")||(y(),_(),t.preventDefault(),t.stopPropagation())}),p.up.on("mousedown.touchspin",function(t){p.up.off("touchstart.touchspin"),v.is(":disabled")||(w(),k(),t.preventDefault(),t.stopPropagation())}),p.up.on("touchstart.touchspin",function(t){p.up.off("mousedown.touchspin"),v.is(":disabled")||(w(),k(),t.preventDefault(),t.stopPropagation())}),p.up.on("mouseout touchleave touchend touchcancel",function(t){m&&(t.stopPropagation(),C())}),p.down.on("mouseout touchleave touchend touchcancel",function(t){m&&(t.stopPropagation(),C())}),p.down.on("mousemove touchmove",function(t){m&&(t.stopPropagation(),t.preventDefault())}),p.up.on("mousemove touchmove",function(t){m&&(t.stopPropagation(),t.preventDefault())}),t(document).on(o(["mouseup","touchend","touchcancel"],n).join(" "),function(t){m&&(t.preventDefault(),C())}),t(document).on(o(["mousemove","touchmove","scroll","scrollstart"],n).join(" "),function(t){m&&(t.preventDefault(),C())}),v.on("mousewheel DOMMouseScroll",function(t){if(i.mousewheel&&v.is(":focus")){var n=t.originalEvent.wheelDelta||-t.originalEvent.deltaY||-t.originalEvent.detail;t.stopPropagation(),t.preventDefault(),n<0?y():w()}}),v.on("touchspin.uponce",function(){C(),w()}),v.on("touchspin.downonce",function(){C(),y()}),v.on("touchspin.startupspin",function(){k()}),v.on("touchspin.startdownspin",function(){_()}),v.on("touchspin.stopspin",function(){C()}),v.on("touchspin.updatesettings",function(n,o){!function(n){(function(n){i=t.extend({},i,n)})(n),x();var o=p.input.val();""!==o&&(o=Number(p.input.val()),p.input.val(o.toFixed(i.decimals)))}(o)}),p.input.css("display","block");var r}()})}this.each(function(){var n=t(this).data();t(document).off(o(["mouseup","touchend","touchcancel","mousemove","touchmove","scroll","scrollstart"],n.spinnerid).join(" "))})}}(jQuery)},function(t,n,o){o(1),o(0)}]);