!function(t){var e={};function i(n){if(e[n])return e[n].exports;var s=e[n]={i:n,l:!1,exports:{}};return t[n].call(s.exports,s,s.exports,i),s.l=!0,s.exports}i.m=t,i.c=e,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},i.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=8)}([function(t,e,i){var n,s,o;s="undefined"!=typeof window?window:this,o=function(i,s){var o=[],r=i.document,a=o.slice,l=o.concat,h=o.push,c=o.indexOf,u={},d=u.toString,p=u.hasOwnProperty,f={},m=function(t,e){return new m.fn.init(t,e)},g=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,v=/^-ms-/,b=/-([\da-z])/gi,y=function(t,e){return e.toUpperCase()};function _(t){var e=!!t&&"length"in t&&t.length,i=m.type(t);return"function"!==i&&!m.isWindow(t)&&("array"===i||0===e||"number"==typeof e&&e>0&&e-1 in t)}m.fn=m.prototype={jquery:"1.12.4",constructor:m,selector:"",length:0,toArray:function(){return a.call(this)},get:function(t){return null!=t?t<0?this[t+this.length]:this[t]:a.call(this)},pushStack:function(t){var e=m.merge(this.constructor(),t);return e.prevObject=this,e.context=this.context,e},each:function(t){return m.each(this,t)},map:function(t){return this.pushStack(m.map(this,function(e,i){return t.call(e,i,e)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(i>=0&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:h,sort:o.sort,splice:o.splice},m.extend=m.fn.extend=function(){var t,e,i,n,s,o,r=arguments[0]||{},a=1,l=arguments.length,h=!1;for("boolean"==typeof r&&(h=r,r=arguments[a]||{},a++),"object"==typeof r||m.isFunction(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(s=arguments[a]))for(n in s)t=r[n],r!==(i=s[n])&&(h&&i&&(m.isPlainObject(i)||(e=m.isArray(i)))?(e?(e=!1,o=t&&m.isArray(t)?t:[]):o=t&&m.isPlainObject(t)?t:{},r[n]=m.extend(h,o,i)):void 0!==i&&(r[n]=i));return r},m.extend({expando:"jQuery"+("1.12.4"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isFunction:function(t){return"function"===m.type(t)},isArray:Array.isArray||function(t){return"array"===m.type(t)},isWindow:function(t){return null!=t&&t==t.window},isNumeric:function(t){var e=t&&t.toString();return!m.isArray(t)&&e-parseFloat(e)+1>=0},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},isPlainObject:function(t){var e;if(!t||"object"!==m.type(t)||t.nodeType||m.isWindow(t))return!1;try{if(t.constructor&&!p.call(t,"constructor")&&!p.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}if(!f.ownFirst)for(e in t)return p.call(t,e);for(e in t);return void 0===e||p.call(t,e)},type:function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?u[d.call(t)]||"object":typeof t},globalEval:function(t){t&&m.trim(t)&&(i.execScript||function(t){i.eval.call(i,t)})(t)},camelCase:function(t){return t.replace(v,"ms-").replace(b,y)},nodeName:function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},each:function(t,e){var i,n=0;if(_(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},trim:function(t){return null==t?"":(t+"").replace(g,"")},makeArray:function(t,e){var i=e||[];return null!=t&&(_(Object(t))?m.merge(i,"string"==typeof t?[t]:t):h.call(i,t)),i},inArray:function(t,e,i){var n;if(e){if(c)return c.call(e,t,i);for(n=e.length,i=i?i<0?Math.max(0,n+i):i:0;i<n;i++)if(i in e&&e[i]===t)return i}return-1},merge:function(t,e){for(var i=+e.length,n=0,s=t.length;n<i;)t[s++]=e[n++];if(i!=i)for(;void 0!==e[n];)t[s++]=e[n++];return t.length=s,t},grep:function(t,e,i){for(var n=[],s=0,o=t.length,r=!i;s<o;s++)!e(t[s],s)!==r&&n.push(t[s]);return n},map:function(t,e,i){var n,s,o=0,r=[];if(_(t))for(n=t.length;o<n;o++)null!=(s=e(t[o],o,i))&&r.push(s);else for(o in t)null!=(s=e(t[o],o,i))&&r.push(s);return l.apply([],r)},guid:1,proxy:function(t,e){var i,n,s;if("string"==typeof e&&(s=t[e],e=t,t=s),m.isFunction(t))return i=a.call(arguments,2),(n=function(){return t.apply(e||this,i.concat(a.call(arguments)))}).guid=t.guid=t.guid||m.guid++,n},now:function(){return+new Date},support:f}),"function"==typeof Symbol&&(m.fn[Symbol.iterator]=o[Symbol.iterator]),m.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){u["[object "+e+"]"]=e.toLowerCase()});var w=function(t){var e,i,n,s,o,r,a,l,h,c,u,d,p,f,m,g,v,b,y,_="sizzle"+1*new Date,w=t.document,x=0,C=0,k=ot(),D=ot(),T=ot(),S=function(t,e){return t===e&&(u=!0),0},E=1<<31,I={}.hasOwnProperty,N=[],A=N.pop,M=N.push,P=N.push,H=N.slice,O=function(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1},z="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",W="[\\x20\\t\\r\\n\\f]",L="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",$="\\["+W+"*("+L+")(?:"+W+"*([*^$|!~]?=)"+W+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+L+"))|)"+W+"*\\]",F=":("+L+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+$+")*)|.*)\\)|)",R=new RegExp(W+"+","g"),j=new RegExp("^"+W+"+|((?:^|[^\\\\])(?:\\\\.)*)"+W+"+$","g"),q=new RegExp("^"+W+"*,"+W+"*"),B=new RegExp("^"+W+"*([>+~]|"+W+")"+W+"*"),Y=new RegExp("="+W+"*([^\\]'\"]*?)"+W+"*\\]","g"),U=new RegExp(F),K=new RegExp("^"+L+"$"),V={ID:new RegExp("^#("+L+")"),CLASS:new RegExp("^\\.("+L+")"),TAG:new RegExp("^("+L+"|[*])"),ATTR:new RegExp("^"+$),PSEUDO:new RegExp("^"+F),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+W+"*(even|odd|(([+-]|)(\\d*)n|)"+W+"*(?:([+-]|)"+W+"*(\\d+)|))"+W+"*\\)|)","i"),bool:new RegExp("^(?:"+z+")$","i"),needsContext:new RegExp("^"+W+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+W+"*((?:-\\d)?\\d*)"+W+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,G=/^h\d$/i,Q=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Z=/[+~]/,tt=/'|\\/g,et=new RegExp("\\\\([\\da-f]{1,6}"+W+"?|("+W+")|.)","ig"),it=function(t,e,i){var n="0x"+e-65536;return n!=n||i?e:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)},nt=function(){d()};try{P.apply(N=H.call(w.childNodes),w.childNodes),N[w.childNodes.length].nodeType}catch(t){P={apply:N.length?function(t,e){M.apply(t,H.call(e))}:function(t,e){for(var i=t.length,n=0;t[i++]=e[n++];);t.length=i-1}}}function st(t,e,n,s){var o,a,h,c,u,f,v,b,x=e&&e.ownerDocument,C=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==C&&9!==C&&11!==C)return n;if(!s&&((e?e.ownerDocument||e:w)!==p&&d(e),e=e||p,m)){if(11!==C&&(f=J.exec(t)))if(o=f[1]){if(9===C){if(!(h=e.getElementById(o)))return n;if(h.id===o)return n.push(h),n}else if(x&&(h=x.getElementById(o))&&y(e,h)&&h.id===o)return n.push(h),n}else{if(f[2])return P.apply(n,e.getElementsByTagName(t)),n;if((o=f[3])&&i.getElementsByClassName&&e.getElementsByClassName)return P.apply(n,e.getElementsByClassName(o)),n}if(i.qsa&&!T[t+" "]&&(!g||!g.test(t))){if(1!==C)x=e,b=t;else if("object"!==e.nodeName.toLowerCase()){for((c=e.getAttribute("id"))?c=c.replace(tt,"\\$&"):e.setAttribute("id",c=_),a=(v=r(t)).length,u=K.test(c)?"#"+c:"[id='"+c+"']";a--;)v[a]=u+" "+mt(v[a]);b=v.join(","),x=Z.test(t)&&pt(e.parentNode)||e}if(b)try{return P.apply(n,x.querySelectorAll(b)),n}catch(t){}finally{c===_&&e.removeAttribute("id")}}}return l(t.replace(j,"$1"),e,n,s)}function ot(){var t=[];return function e(i,s){return t.push(i+" ")>n.cacheLength&&delete e[t.shift()],e[i+" "]=s}}function rt(t){return t[_]=!0,t}function at(t){var e=p.createElement("div");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function lt(t,e){for(var i=t.split("|"),s=i.length;s--;)n.attrHandle[i[s]]=e}function ht(t,e){var i=e&&t,n=i&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||E)-(~t.sourceIndex||E);if(n)return n;if(i)for(;i=i.nextSibling;)if(i===e)return-1;return t?1:-1}function ct(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function ut(t){return function(e){var i=e.nodeName.toLowerCase();return("input"===i||"button"===i)&&e.type===t}}function dt(t){return rt(function(e){return e=+e,rt(function(i,n){for(var s,o=t([],i.length,e),r=o.length;r--;)i[s=o[r]]&&(i[s]=!(n[s]=i[s]))})})}function pt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in i=st.support={},o=st.isXML=function(t){var e=t&&(t.ownerDocument||t).documentElement;return!!e&&"HTML"!==e.nodeName},d=st.setDocument=function(t){var e,s,r=t?t.ownerDocument||t:w;return r!==p&&9===r.nodeType&&r.documentElement?(f=(p=r).documentElement,m=!o(p),(s=p.defaultView)&&s.top!==s&&(s.addEventListener?s.addEventListener("unload",nt,!1):s.attachEvent&&s.attachEvent("onunload",nt)),i.attributes=at(function(t){return t.className="i",!t.getAttribute("className")}),i.getElementsByTagName=at(function(t){return t.appendChild(p.createComment("")),!t.getElementsByTagName("*").length}),i.getElementsByClassName=Q.test(p.getElementsByClassName),i.getById=at(function(t){return f.appendChild(t).id=_,!p.getElementsByName||!p.getElementsByName(_).length}),i.getById?(n.find.ID=function(t,e){if(void 0!==e.getElementById&&m){var i=e.getElementById(t);return i?[i]:[]}},n.filter.ID=function(t){var e=t.replace(et,it);return function(t){return t.getAttribute("id")===e}}):(delete n.find.ID,n.filter.ID=function(t){var e=t.replace(et,it);return function(t){var i=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}}),n.find.TAG=i.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):i.qsa?e.querySelectorAll(t):void 0}:function(t,e){var i,n=[],s=0,o=e.getElementsByTagName(t);if("*"===t){for(;i=o[s++];)1===i.nodeType&&n.push(i);return n}return o},n.find.CLASS=i.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&m)return e.getElementsByClassName(t)},v=[],g=[],(i.qsa=Q.test(p.querySelectorAll))&&(at(function(t){f.appendChild(t).innerHTML="<a id='"+_+"'></a><select id='"+_+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&g.push("[*^$]="+W+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||g.push("\\["+W+"*(?:value|"+z+")"),t.querySelectorAll("[id~="+_+"-]").length||g.push("~="),t.querySelectorAll(":checked").length||g.push(":checked"),t.querySelectorAll("a#"+_+"+*").length||g.push(".#.+[+~]")}),at(function(t){var e=p.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&g.push("name"+W+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||g.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),g.push(",.*:")})),(i.matchesSelector=Q.test(b=f.matches||f.webkitMatchesSelector||f.mozMatchesSelector||f.oMatchesSelector||f.msMatchesSelector))&&at(function(t){i.disconnectedMatch=b.call(t,"div"),b.call(t,"[s!='']:x"),v.push("!=",F)}),g=g.length&&new RegExp(g.join("|")),v=v.length&&new RegExp(v.join("|")),e=Q.test(f.compareDocumentPosition),y=e||Q.test(f.contains)?function(t,e){var i=9===t.nodeType?t.documentElement:t,n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(i.contains?i.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},S=e?function(t,e){if(t===e)return u=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!i.sortDetached&&e.compareDocumentPosition(t)===n?t===p||t.ownerDocument===w&&y(w,t)?-1:e===p||e.ownerDocument===w&&y(w,e)?1:c?O(c,t)-O(c,e):0:4&n?-1:1)}:function(t,e){if(t===e)return u=!0,0;var i,n=0,s=t.parentNode,o=e.parentNode,r=[t],a=[e];if(!s||!o)return t===p?-1:e===p?1:s?-1:o?1:c?O(c,t)-O(c,e):0;if(s===o)return ht(t,e);for(i=t;i=i.parentNode;)r.unshift(i);for(i=e;i=i.parentNode;)a.unshift(i);for(;r[n]===a[n];)n++;return n?ht(r[n],a[n]):r[n]===w?-1:a[n]===w?1:0},p):p},st.matches=function(t,e){return st(t,null,null,e)},st.matchesSelector=function(t,e){if((t.ownerDocument||t)!==p&&d(t),e=e.replace(Y,"='$1']"),i.matchesSelector&&m&&!T[e+" "]&&(!v||!v.test(e))&&(!g||!g.test(e)))try{var n=b.call(t,e);if(n||i.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){}return st(e,p,null,[t]).length>0},st.contains=function(t,e){return(t.ownerDocument||t)!==p&&d(t),y(t,e)},st.attr=function(t,e){(t.ownerDocument||t)!==p&&d(t);var s=n.attrHandle[e.toLowerCase()],o=s&&I.call(n.attrHandle,e.toLowerCase())?s(t,e,!m):void 0;return void 0!==o?o:i.attributes||!m?t.getAttribute(e):(o=t.getAttributeNode(e))&&o.specified?o.value:null},st.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},st.uniqueSort=function(t){var e,n=[],s=0,o=0;if(u=!i.detectDuplicates,c=!i.sortStable&&t.slice(0),t.sort(S),u){for(;e=t[o++];)e===t[o]&&(s=n.push(o));for(;s--;)t.splice(n[s],1)}return c=null,t},s=st.getText=function(t){var e,i="",n=0,o=t.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)i+=s(t)}else if(3===o||4===o)return t.nodeValue}else for(;e=t[n++];)i+=s(e);return i},(n=st.selectors={cacheLength:50,createPseudo:rt,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(et,it),t[3]=(t[3]||t[4]||t[5]||"").replace(et,it),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||st.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&st.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return V.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&U.test(i)&&(e=r(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(et,it).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=k[t+" "];return e||(e=new RegExp("(^|"+W+")"+t+"("+W+"|$)"))&&k(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,i){return function(n){var s=st.attr(n,t);return null==s?"!="===e:!e||(s+="","="===e?s===i:"!="===e?s!==i:"^="===e?i&&0===s.indexOf(i):"*="===e?i&&s.indexOf(i)>-1:"$="===e?i&&s.slice(-i.length)===i:"~="===e?(" "+s.replace(R," ")+" ").indexOf(i)>-1:"|="===e&&(s===i||s.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,i,n,s){var o="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===n&&0===s?function(t){return!!t.parentNode}:function(e,i,l){var h,c,u,d,p,f,m=o!==r?"nextSibling":"previousSibling",g=e.parentNode,v=a&&e.nodeName.toLowerCase(),b=!l&&!a,y=!1;if(g){if(o){for(;m;){for(d=e;d=d[m];)if(a?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;f=m="only"===t&&!f&&"nextSibling"}return!0}if(f=[r?g.firstChild:g.lastChild],r&&b){for(y=(p=(h=(c=(u=(d=g)[_]||(d[_]={}))[d.uniqueID]||(u[d.uniqueID]={}))[t]||[])[0]===x&&h[1])&&h[2],d=p&&g.childNodes[p];d=++p&&d&&d[m]||(y=p=0)||f.pop();)if(1===d.nodeType&&++y&&d===e){c[t]=[x,p,y];break}}else if(b&&(y=p=(h=(c=(u=(d=e)[_]||(d[_]={}))[d.uniqueID]||(u[d.uniqueID]={}))[t]||[])[0]===x&&h[1]),!1===y)for(;(d=++p&&d&&d[m]||(y=p=0)||f.pop())&&((a?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++y||(b&&((c=(u=d[_]||(d[_]={}))[d.uniqueID]||(u[d.uniqueID]={}))[t]=[x,y]),d!==e)););return(y-=s)===n||y%n==0&&y/n>=0}}},PSEUDO:function(t,e){var i,s=n.pseudos[t]||n.setFilters[t.toLowerCase()]||st.error("unsupported pseudo: "+t);return s[_]?s(e):s.length>1?(i=[t,t,"",e],n.setFilters.hasOwnProperty(t.toLowerCase())?rt(function(t,i){for(var n,o=s(t,e),r=o.length;r--;)t[n=O(t,o[r])]=!(i[n]=o[r])}):function(t){return s(t,0,i)}):s}},pseudos:{not:rt(function(t){var e=[],i=[],n=a(t.replace(j,"$1"));return n[_]?rt(function(t,e,i,s){for(var o,r=n(t,null,s,[]),a=t.length;a--;)(o=r[a])&&(t[a]=!(e[a]=o))}):function(t,s,o){return e[0]=t,n(e,null,o,i),e[0]=null,!i.pop()}}),has:rt(function(t){return function(e){return st(t,e).length>0}}),contains:rt(function(t){return t=t.replace(et,it),function(e){return(e.textContent||e.innerText||s(e)).indexOf(t)>-1}}),lang:rt(function(t){return K.test(t||"")||st.error("unsupported lang: "+t),t=t.replace(et,it).toLowerCase(),function(e){var i;do{if(i=m?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===f},focus:function(t){return t===p.activeElement&&(!p.hasFocus||p.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:function(t){return!1===t.disabled},disabled:function(t){return!0===t.disabled},checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!n.pseudos.empty(t)},header:function(t){return G.test(t.nodeName)},input:function(t){return X.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:dt(function(){return[0]}),last:dt(function(t,e){return[e-1]}),eq:dt(function(t,e,i){return[i<0?i+e:i]}),even:dt(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:dt(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:dt(function(t,e,i){for(var n=i<0?i+e:i;--n>=0;)t.push(n);return t}),gt:dt(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=n.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})n.pseudos[e]=ct(e);for(e in{submit:!0,reset:!0})n.pseudos[e]=ut(e);function ft(){}function mt(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function gt(t,e,i){var n=e.dir,s=i&&"parentNode"===n,o=C++;return e.first?function(e,i,o){for(;e=e[n];)if(1===e.nodeType||s)return t(e,i,o)}:function(e,i,r){var a,l,h,c=[x,o];if(r){for(;e=e[n];)if((1===e.nodeType||s)&&t(e,i,r))return!0}else for(;e=e[n];)if(1===e.nodeType||s){if((a=(l=(h=e[_]||(e[_]={}))[e.uniqueID]||(h[e.uniqueID]={}))[n])&&a[0]===x&&a[1]===o)return c[2]=a[2];if(l[n]=c,c[2]=t(e,i,r))return!0}}}function vt(t){return t.length>1?function(e,i,n){for(var s=t.length;s--;)if(!t[s](e,i,n))return!1;return!0}:t[0]}function bt(t,e,i,n,s){for(var o,r=[],a=0,l=t.length,h=null!=e;a<l;a++)(o=t[a])&&(i&&!i(o,n,s)||(r.push(o),h&&e.push(a)));return r}function yt(t,e,i,n,s,o){return n&&!n[_]&&(n=yt(n)),s&&!s[_]&&(s=yt(s,o)),rt(function(o,r,a,l){var h,c,u,d=[],p=[],f=r.length,m=o||function(t,e,i){for(var n=0,s=e.length;n<s;n++)st(t,e[n],i);return i}(e||"*",a.nodeType?[a]:a,[]),g=!t||!o&&e?m:bt(m,d,t,a,l),v=i?s||(o?t:f||n)?[]:r:g;if(i&&i(g,v,a,l),n)for(h=bt(v,p),n(h,[],a,l),c=h.length;c--;)(u=h[c])&&(v[p[c]]=!(g[p[c]]=u));if(o){if(s||t){if(s){for(h=[],c=v.length;c--;)(u=v[c])&&h.push(g[c]=u);s(null,v=[],h,l)}for(c=v.length;c--;)(u=v[c])&&(h=s?O(o,u):d[c])>-1&&(o[h]=!(r[h]=u))}}else v=bt(v===r?v.splice(f,v.length):v),s?s(null,r,v,l):P.apply(r,v)})}function _t(t){for(var e,i,s,o=t.length,r=n.relative[t[0].type],a=r||n.relative[" "],l=r?1:0,c=gt(function(t){return t===e},a,!0),u=gt(function(t){return O(e,t)>-1},a,!0),d=[function(t,i,n){var s=!r&&(n||i!==h)||((e=i).nodeType?c(t,i,n):u(t,i,n));return e=null,s}];l<o;l++)if(i=n.relative[t[l].type])d=[gt(vt(d),i)];else{if((i=n.filter[t[l].type].apply(null,t[l].matches))[_]){for(s=++l;s<o&&!n.relative[t[s].type];s++);return yt(l>1&&vt(d),l>1&&mt(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(j,"$1"),i,l<s&&_t(t.slice(l,s)),s<o&&_t(t=t.slice(s)),s<o&&mt(t))}d.push(i)}return vt(d)}return ft.prototype=n.filters=n.pseudos,n.setFilters=new ft,r=st.tokenize=function(t,e){var i,s,o,r,a,l,h,c=D[t+" "];if(c)return e?0:c.slice(0);for(a=t,l=[],h=n.preFilter;a;){for(r in i&&!(s=q.exec(a))||(s&&(a=a.slice(s[0].length)||a),l.push(o=[])),i=!1,(s=B.exec(a))&&(i=s.shift(),o.push({value:i,type:s[0].replace(j," ")}),a=a.slice(i.length)),n.filter)!(s=V[r].exec(a))||h[r]&&!(s=h[r](s))||(i=s.shift(),o.push({value:i,type:r,matches:s}),a=a.slice(i.length));if(!i)break}return e?a.length:a?st.error(t):D(t,l).slice(0)},a=st.compile=function(t,e){var i,s,o,a,l,c,u=[],f=[],g=T[t+" "];if(!g){for(e||(e=r(t)),i=e.length;i--;)(g=_t(e[i]))[_]?u.push(g):f.push(g);(g=T(t,(s=f,o=u,a=o.length>0,l=s.length>0,c=function(t,e,i,r,c){var u,f,g,v=0,b="0",y=t&&[],_=[],w=h,C=t||l&&n.find.TAG("*",c),k=x+=null==w?1:Math.random()||.1,D=C.length;for(c&&(h=e===p||e||c);b!==D&&null!=(u=C[b]);b++){if(l&&u){for(f=0,e||u.ownerDocument===p||(d(u),i=!m);g=s[f++];)if(g(u,e||p,i)){r.push(u);break}c&&(x=k)}a&&((u=!g&&u)&&v--,t&&y.push(u))}if(v+=b,a&&b!==v){for(f=0;g=o[f++];)g(y,_,e,i);if(t){if(v>0)for(;b--;)y[b]||_[b]||(_[b]=A.call(r));_=bt(_)}P.apply(r,_),c&&!t&&_.length>0&&v+o.length>1&&st.uniqueSort(r)}return c&&(x=k,h=w),y},a?rt(c):c))).selector=t}return g},l=st.select=function(t,e,s,o){var l,h,c,u,d,p="function"==typeof t&&t,f=!o&&r(t=p.selector||t);if(s=s||[],1===f.length){if((h=f[0]=f[0].slice(0)).length>2&&"ID"===(c=h[0]).type&&i.getById&&9===e.nodeType&&m&&n.relative[h[1].type]){if(!(e=(n.find.ID(c.matches[0].replace(et,it),e)||[])[0]))return s;p&&(e=e.parentNode),t=t.slice(h.shift().value.length)}for(l=V.needsContext.test(t)?0:h.length;l--&&(c=h[l],!n.relative[u=c.type]);)if((d=n.find[u])&&(o=d(c.matches[0].replace(et,it),Z.test(h[0].type)&&pt(e.parentNode)||e))){if(h.splice(l,1),!(t=o.length&&mt(h)))return P.apply(s,o),s;break}}return(p||a(t,f))(o,e,!m,s,!e||Z.test(t)&&pt(e.parentNode)||e),s},i.sortStable=_.split("").sort(S).join("")===_,i.detectDuplicates=!!u,d(),i.sortDetached=at(function(t){return 1&t.compareDocumentPosition(p.createElement("div"))}),at(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||lt("type|href|height|width",function(t,e,i){if(!i)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),i.attributes&&at(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||lt("value",function(t,e,i){if(!i&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),at(function(t){return null==t.getAttribute("disabled")})||lt(z,function(t,e,i){var n;if(!i)return!0===t[e]?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null}),st}(i);m.find=w,m.expr=w.selectors,m.expr[":"]=m.expr.pseudos,m.uniqueSort=m.unique=w.uniqueSort,m.text=w.getText,m.isXMLDoc=w.isXML,m.contains=w.contains;var x=function(t,e,i){for(var n=[],s=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(s&&m(t).is(i))break;n.push(t)}return n},C=function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i},k=m.expr.match.needsContext,D=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,T=/^.[^:#\[\.,]*$/;function S(t,e,i){if(m.isFunction(e))return m.grep(t,function(t,n){return!!e.call(t,n,t)!==i});if(e.nodeType)return m.grep(t,function(t){return t===e!==i});if("string"==typeof e){if(T.test(e))return m.filter(e,t,i);e=m.filter(e,t)}return m.grep(t,function(t){return m.inArray(t,e)>-1!==i})}m.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?m.find.matchesSelector(n,t)?[n]:[]:m.find.matches(t,m.grep(e,function(t){return 1===t.nodeType}))},m.fn.extend({find:function(t){var e,i=[],n=this,s=n.length;if("string"!=typeof t)return this.pushStack(m(t).filter(function(){for(e=0;e<s;e++)if(m.contains(n[e],this))return!0}));for(e=0;e<s;e++)m.find(t,n[e],i);return(i=this.pushStack(s>1?m.unique(i):i)).selector=this.selector?this.selector+" "+t:t,i},filter:function(t){return this.pushStack(S(this,t||[],!1))},not:function(t){return this.pushStack(S(this,t||[],!0))},is:function(t){return!!S(this,"string"==typeof t&&k.test(t)?m(t):t||[],!1).length}});var E,I=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(m.fn.init=function(t,e,i){var n,s;if(!t)return this;if(i=i||E,"string"==typeof t){if(!(n="<"===t.charAt(0)&&">"===t.charAt(t.length-1)&&t.length>=3?[null,t,null]:I.exec(t))||!n[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof m?e[0]:e,m.merge(this,m.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:r,!0)),D.test(n[1])&&m.isPlainObject(e))for(n in e)m.isFunction(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}if((s=r.getElementById(n[2]))&&s.parentNode){if(s.id!==n[2])return E.find(t);this.length=1,this[0]=s}return this.context=r,this.selector=t,this}return t.nodeType?(this.context=this[0]=t,this.length=1,this):m.isFunction(t)?void 0!==i.ready?i.ready(t):t(m):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),m.makeArray(t,this))}).prototype=m.fn,E=m(r);var N=/^(?:parents|prev(?:Until|All))/,A={children:!0,contents:!0,next:!0,prev:!0};function M(t,e){do{t=t[e]}while(t&&1!==t.nodeType);return t}m.fn.extend({has:function(t){var e,i=m(t,this),n=i.length;return this.filter(function(){for(e=0;e<n;e++)if(m.contains(this,i[e]))return!0})},closest:function(t,e){for(var i,n=0,s=this.length,o=[],r=k.test(t)||"string"!=typeof t?m(t,e||this.context):0;n<s;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?r.index(i)>-1:1===i.nodeType&&m.find.matchesSelector(i,t))){o.push(i);break}return this.pushStack(o.length>1?m.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?m.inArray(this[0],m(t)):m.inArray(t.jquery?t[0]:t,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(m.uniqueSort(m.merge(this.get(),m(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),m.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return x(t,"parentNode")},parentsUntil:function(t,e,i){return x(t,"parentNode",i)},next:function(t){return M(t,"nextSibling")},prev:function(t){return M(t,"previousSibling")},nextAll:function(t){return x(t,"nextSibling")},prevAll:function(t){return x(t,"previousSibling")},nextUntil:function(t,e,i){return x(t,"nextSibling",i)},prevUntil:function(t,e,i){return x(t,"previousSibling",i)},siblings:function(t){return C((t.parentNode||{}).firstChild,t)},children:function(t){return C(t.firstChild)},contents:function(t){return m.nodeName(t,"iframe")?t.contentDocument||t.contentWindow.document:m.merge([],t.childNodes)}},function(t,e){m.fn[t]=function(i,n){var s=m.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(s=m.filter(n,s)),this.length>1&&(A[t]||(s=m.uniqueSort(s)),N.test(t)&&(s=s.reverse())),this.pushStack(s)}});var P,H,O=/\S+/g;function z(){r.addEventListener?(r.removeEventListener("DOMContentLoaded",W),i.removeEventListener("load",W)):(r.detachEvent("onreadystatechange",W),i.detachEvent("onload",W))}function W(){(r.addEventListener||"load"===i.event.type||"complete"===r.readyState)&&(z(),m.ready())}for(H in m.Callbacks=function(t){var e,i;t="string"==typeof t?(e=t,i={},m.each(e.match(O)||[],function(t,e){i[e]=!0}),i):m.extend({},t);var n,s,o,r,a=[],l=[],h=-1,c=function(){for(r=t.once,o=n=!0;l.length;h=-1)for(s=l.shift();++h<a.length;)!1===a[h].apply(s[0],s[1])&&t.stopOnFalse&&(h=a.length,s=!1);t.memory||(s=!1),n=!1,r&&(a=s?[]:"")},u={add:function(){return a&&(s&&!n&&(h=a.length-1,l.push(s)),function e(i){m.each(i,function(i,n){m.isFunction(n)?t.unique&&u.has(n)||a.push(n):n&&n.length&&"string"!==m.type(n)&&e(n)})}(arguments),s&&!n&&c()),this},remove:function(){return m.each(arguments,function(t,e){for(var i;(i=m.inArray(e,a,i))>-1;)a.splice(i,1),i<=h&&h--}),this},has:function(t){return t?m.inArray(t,a)>-1:a.length>0},empty:function(){return a&&(a=[]),this},disable:function(){return r=l=[],a=s="",this},disabled:function(){return!a},lock:function(){return r=!0,s||u.disable(),this},locked:function(){return!!r},fireWith:function(t,e){return r||(e=[t,(e=e||[]).slice?e.slice():e],l.push(e),n||c()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!o}};return u},m.extend({Deferred:function(t){var e=[["resolve","done",m.Callbacks("once memory"),"resolved"],["reject","fail",m.Callbacks("once memory"),"rejected"],["notify","progress",m.Callbacks("memory")]],i="pending",n={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},then:function(){var t=arguments;return m.Deferred(function(i){m.each(e,function(e,o){var r=m.isFunction(t[e])&&t[e];s[o[1]](function(){var t=r&&r.apply(this,arguments);t&&m.isFunction(t.promise)?t.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[o[0]+"With"](this===n?i.promise():this,r?[t]:arguments)})}),t=null}).promise()},promise:function(t){return null!=t?m.extend(t,n):n}},s={};return n.pipe=n.then,m.each(e,function(t,o){var r=o[2],a=o[3];n[o[1]]=r.add,a&&r.add(function(){i=a},e[1^t][2].disable,e[2][2].lock),s[o[0]]=function(){return s[o[0]+"With"](this===s?n:this,arguments),this},s[o[0]+"With"]=r.fireWith}),n.promise(s),t&&t.call(s,s),s},when:function(t){var e,i,n,s=0,o=a.call(arguments),r=o.length,l=1!==r||t&&m.isFunction(t.promise)?r:0,h=1===l?t:m.Deferred(),c=function(t,i,n){return function(s){i[t]=this,n[t]=arguments.length>1?a.call(arguments):s,n===e?h.notifyWith(i,n):--l||h.resolveWith(i,n)}};if(r>1)for(e=new Array(r),i=new Array(r),n=new Array(r);s<r;s++)o[s]&&m.isFunction(o[s].promise)?o[s].promise().progress(c(s,i,e)).done(c(s,n,o)).fail(h.reject):--l;return l||h.resolveWith(n,o),h.promise()}}),m.fn.ready=function(t){return m.ready.promise().done(t),this},m.extend({isReady:!1,readyWait:1,holdReady:function(t){t?m.readyWait++:m.ready(!0)},ready:function(t){(!0===t?--m.readyWait:m.isReady)||(m.isReady=!0,!0!==t&&--m.readyWait>0||(P.resolveWith(r,[m]),m.fn.triggerHandler&&(m(r).triggerHandler("ready"),m(r).off("ready"))))}}),m.ready.promise=function(t){if(!P)if(P=m.Deferred(),"complete"===r.readyState||"loading"!==r.readyState&&!r.documentElement.doScroll)i.setTimeout(m.ready);else if(r.addEventListener)r.addEventListener("DOMContentLoaded",W),i.addEventListener("load",W);else{r.attachEvent("onreadystatechange",W),i.attachEvent("onload",W);var e=!1;try{e=null==i.frameElement&&r.documentElement}catch(t){}e&&e.doScroll&&function t(){if(!m.isReady){try{e.doScroll("left")}catch(e){return i.setTimeout(t,50)}z(),m.ready()}}()}return P.promise(t)},m.ready.promise(),m(f))break;f.ownFirst="0"===H,f.inlineBlockNeedsLayout=!1,m(function(){var t,e,i,n;(i=r.getElementsByTagName("body")[0])&&i.style&&(e=r.createElement("div"),(n=r.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",i.appendChild(n).appendChild(e),void 0!==e.style.zoom&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",f.inlineBlockNeedsLayout=t=3===e.offsetWidth,t&&(i.style.zoom=1)),i.removeChild(n))}),function(){var t=r.createElement("div");f.deleteExpando=!0;try{delete t.test}catch(t){f.deleteExpando=!1}t=null}();var L,$=function(t){var e=m.noData[(t.nodeName+" ").toLowerCase()],i=+t.nodeType||1;return(1===i||9===i)&&(!e||!0!==e&&t.getAttribute("classid")===e)},F=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,R=/([A-Z])/g;function j(t,e,i){if(void 0===i&&1===t.nodeType){var n="data-"+e.replace(R,"-$1").toLowerCase();if("string"==typeof(i=t.getAttribute(n))){try{i="true"===i||"false"!==i&&("null"===i?null:+i+""===i?+i:F.test(i)?m.parseJSON(i):i)}catch(t){}m.data(t,e,i)}else i=void 0}return i}function q(t){var e;for(e in t)if(("data"!==e||!m.isEmptyObject(t[e]))&&"toJSON"!==e)return!1;return!0}function B(t,e,i,n){if($(t)){var s,r,a=m.expando,l=t.nodeType,h=l?m.cache:t,c=l?t[a]:t[a]&&a;if(c&&h[c]&&(n||h[c].data)||void 0!==i||"string"!=typeof e)return c||(c=l?t[a]=o.pop()||m.guid++:a),h[c]||(h[c]=l?{}:{toJSON:m.noop}),"object"!=typeof e&&"function"!=typeof e||(n?h[c]=m.extend(h[c],e):h[c].data=m.extend(h[c].data,e)),r=h[c],n||(r.data||(r.data={}),r=r.data),void 0!==i&&(r[m.camelCase(e)]=i),"string"==typeof e?null==(s=r[e])&&(s=r[m.camelCase(e)]):s=r,s}}function Y(t,e,i){if($(t)){var n,s,o=t.nodeType,r=o?m.cache:t,a=o?t[m.expando]:m.expando;if(r[a]){if(e&&(n=i?r[a]:r[a].data)){s=(e=m.isArray(e)?e.concat(m.map(e,m.camelCase)):e in n?[e]:(e=m.camelCase(e))in n?[e]:e.split(" ")).length;for(;s--;)delete n[e[s]];if(i?!q(n):!m.isEmptyObject(n))return}(i||(delete r[a].data,q(r[a])))&&(o?m.cleanData([t],!0):f.deleteExpando||r!=r.window?delete r[a]:r[a]=void 0)}}}m.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(t){return!!(t=t.nodeType?m.cache[t[m.expando]]:t[m.expando])&&!q(t)},data:function(t,e,i){return B(t,e,i)},removeData:function(t,e){return Y(t,e)},_data:function(t,e,i){return B(t,e,i,!0)},_removeData:function(t,e){return Y(t,e,!0)}}),m.fn.extend({data:function(t,e){var i,n,s,o=this[0],r=o&&o.attributes;if(void 0===t){if(this.length&&(s=m.data(o),1===o.nodeType&&!m._data(o,"parsedAttrs"))){for(i=r.length;i--;)r[i]&&0===(n=r[i].name).indexOf("data-")&&j(o,n=m.camelCase(n.slice(5)),s[n]);m._data(o,"parsedAttrs",!0)}return s}return"object"==typeof t?this.each(function(){m.data(this,t)}):arguments.length>1?this.each(function(){m.data(this,t,e)}):o?j(o,t,m.data(o,t)):void 0},removeData:function(t){return this.each(function(){m.removeData(this,t)})}}),m.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=m._data(t,e),i&&(!n||m.isArray(i)?n=m._data(t,e,m.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=m.queue(t,e),n=i.length,s=i.shift(),o=m._queueHooks(t,e);"inprogress"===s&&(s=i.shift(),n--),s&&("fx"===e&&i.unshift("inprogress"),delete o.stop,s.call(t,function(){m.dequeue(t,e)},o)),!n&&o&&o.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return m._data(t,i)||m._data(t,i,{empty:m.Callbacks("once memory").add(function(){m._removeData(t,e+"queue"),m._removeData(t,i)})})}}),m.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?m.queue(this[0],t):void 0===e?this:this.each(function(){var i=m.queue(this,t,e);m._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&m.dequeue(this,t)})},dequeue:function(t){return this.each(function(){m.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,s=m.Deferred(),o=this,r=this.length,a=function(){--n||s.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(i=m._data(o[r],t+"queueHooks"))&&i.empty&&(n++,i.empty.add(a));return a(),s.promise(e)}}),f.shrinkWrapBlocks=function(){return null!=L?L:(L=!1,(e=r.getElementsByTagName("body")[0])&&e.style?(t=r.createElement("div"),(i=r.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",e.appendChild(i).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(r.createElement("div")).style.width="5px",L=3!==t.offsetWidth),e.removeChild(i),L):void 0);var t,e,i};var U=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,K=new RegExp("^(?:([+-])=|)("+U+")([a-z%]*)$","i"),V=["Top","Right","Bottom","Left"],X=function(t,e){return t=e||t,"none"===m.css(t,"display")||!m.contains(t.ownerDocument,t)};function G(t,e,i,n){var s,o=1,r=20,a=n?function(){return n.cur()}:function(){return m.css(t,e,"")},l=a(),h=i&&i[3]||(m.cssNumber[e]?"":"px"),c=(m.cssNumber[e]||"px"!==h&&+l)&&K.exec(m.css(t,e));if(c&&c[3]!==h){h=h||c[3],i=i||[],c=+l||1;do{c/=o=o||".5",m.style(t,e,c+h)}while(o!==(o=a()/l)&&1!==o&&--r)}return i&&(c=+c||+l||0,s=i[1]?c+(i[1]+1)*i[2]:+i[2],n&&(n.unit=h,n.start=c,n.end=s)),s}var Q,J,Z,tt=function(t,e,i,n,s,o,r){var a=0,l=t.length,h=null==i;if("object"===m.type(i))for(a in s=!0,i)tt(t,e,a,i[a],!0,o,r);else if(void 0!==n&&(s=!0,m.isFunction(n)||(r=!0),h&&(r?(e.call(t,n),e=null):(h=e,e=function(t,e,i){return h.call(m(t),i)})),e))for(;a<l;a++)e(t[a],i,r?n:n.call(t[a],a,e(t[a],i)));return s?t:h?e.call(t):l?e(t[0],i):o},et=/^(?:checkbox|radio)$/i,it=/<([\w:-]+)/,nt=/^$|\/(?:java|ecma)script/i,st=/^\s+/,ot="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";function rt(t){var e=ot.split("|"),i=t.createDocumentFragment();if(i.createElement)for(;e.length;)i.createElement(e.pop());return i}Q=r.createElement("div"),J=r.createDocumentFragment(),Z=r.createElement("input"),Q.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",f.leadingWhitespace=3===Q.firstChild.nodeType,f.tbody=!Q.getElementsByTagName("tbody").length,f.htmlSerialize=!!Q.getElementsByTagName("link").length,f.html5Clone="<:nav></:nav>"!==r.createElement("nav").cloneNode(!0).outerHTML,Z.type="checkbox",Z.checked=!0,J.appendChild(Z),f.appendChecked=Z.checked,Q.innerHTML="<textarea>x</textarea>",f.noCloneChecked=!!Q.cloneNode(!0).lastChild.defaultValue,J.appendChild(Q),(Z=r.createElement("input")).setAttribute("type","radio"),Z.setAttribute("checked","checked"),Z.setAttribute("name","t"),Q.appendChild(Z),f.checkClone=Q.cloneNode(!0).cloneNode(!0).lastChild.checked,f.noCloneEvent=!!Q.addEventListener,Q[m.expando]=1,f.attributes=!Q.getAttribute(m.expando);var at={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:f.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};function lt(t,e){var i,n,s=0,o=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):void 0;if(!o)for(o=[],i=t.childNodes||t;null!=(n=i[s]);s++)!e||m.nodeName(n,e)?o.push(n):m.merge(o,lt(n,e));return void 0===e||e&&m.nodeName(t,e)?m.merge([t],o):o}function ht(t,e){for(var i,n=0;null!=(i=t[n]);n++)m._data(i,"globalEval",!e||m._data(e[n],"globalEval"))}at.optgroup=at.option,at.tbody=at.tfoot=at.colgroup=at.caption=at.thead,at.th=at.td;var ct=/<|&#?\w+;/,ut=/<tbody/i;function dt(t){et.test(t.type)&&(t.defaultChecked=t.checked)}function pt(t,e,i,n,s){for(var o,r,a,l,h,c,u,d=t.length,p=rt(e),g=[],v=0;v<d;v++)if((r=t[v])||0===r)if("object"===m.type(r))m.merge(g,r.nodeType?[r]:r);else if(ct.test(r)){for(l=l||p.appendChild(e.createElement("div")),h=(it.exec(r)||["",""])[1].toLowerCase(),u=at[h]||at._default,l.innerHTML=u[1]+m.htmlPrefilter(r)+u[2],o=u[0];o--;)l=l.lastChild;if(!f.leadingWhitespace&&st.test(r)&&g.push(e.createTextNode(st.exec(r)[0])),!f.tbody)for(o=(r="table"!==h||ut.test(r)?"<table>"!==u[1]||ut.test(r)?0:l:l.firstChild)&&r.childNodes.length;o--;)m.nodeName(c=r.childNodes[o],"tbody")&&!c.childNodes.length&&r.removeChild(c);for(m.merge(g,l.childNodes),l.textContent="";l.firstChild;)l.removeChild(l.firstChild);l=p.lastChild}else g.push(e.createTextNode(r));for(l&&p.removeChild(l),f.appendChecked||m.grep(lt(g,"input"),dt),v=0;r=g[v++];)if(n&&m.inArray(r,n)>-1)s&&s.push(r);else if(a=m.contains(r.ownerDocument,r),l=lt(p.appendChild(r),"script"),a&&ht(l),i)for(o=0;r=l[o++];)nt.test(r.type||"")&&i.push(r);return l=null,p}!function(){var t,e,n=r.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})e="on"+t,(f[t]=e in i)||(n.setAttribute(e,"t"),f[t]=!1===n.attributes[e].expando);n=null}();var ft=/^(?:input|select|textarea)$/i,mt=/^key/,gt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,vt=/^(?:focusinfocus|focusoutblur)$/,bt=/^([^.]*)(?:\.(.+)|)/;function yt(){return!0}function _t(){return!1}function wt(){try{return r.activeElement}catch(t){}}function xt(t,e,i,n,s,o){var r,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)xt(t,a,i,n,e[a],o);return t}if(null==n&&null==s?(s=i,n=i=void 0):null==s&&("string"==typeof i?(s=n,n=void 0):(s=n,n=i,i=void 0)),!1===s)s=_t;else if(!s)return t;return 1===o&&(r=s,(s=function(t){return m().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=m.guid++)),t.each(function(){m.event.add(this,e,s,n,i)})}m.event={global:{},add:function(t,e,i,n,s){var o,r,a,l,h,c,u,d,p,f,g,v=m._data(t);if(v){for(i.handler&&(i=(l=i).handler,s=l.selector),i.guid||(i.guid=m.guid++),(r=v.events)||(r=v.events={}),(c=v.handle)||((c=v.handle=function(t){return void 0===m||t&&m.event.triggered===t.type?void 0:m.event.dispatch.apply(c.elem,arguments)}).elem=t),a=(e=(e||"").match(O)||[""]).length;a--;)p=g=(o=bt.exec(e[a])||[])[1],f=(o[2]||"").split(".").sort(),p&&(h=m.event.special[p]||{},p=(s?h.delegateType:h.bindType)||p,h=m.event.special[p]||{},u=m.extend({type:p,origType:g,data:n,handler:i,guid:i.guid,selector:s,needsContext:s&&m.expr.match.needsContext.test(s),namespace:f.join(".")},l),(d=r[p])||((d=r[p]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(t,n,f,c)||(t.addEventListener?t.addEventListener(p,c,!1):t.attachEvent&&t.attachEvent("on"+p,c))),h.add&&(h.add.call(t,u),u.handler.guid||(u.handler.guid=i.guid)),s?d.splice(d.delegateCount++,0,u):d.push(u),m.event.global[p]=!0);t=null}},remove:function(t,e,i,n,s){var o,r,a,l,h,c,u,d,p,f,g,v=m.hasData(t)&&m._data(t);if(v&&(c=v.events)){for(h=(e=(e||"").match(O)||[""]).length;h--;)if(p=g=(a=bt.exec(e[h])||[])[1],f=(a[2]||"").split(".").sort(),p){for(u=m.event.special[p]||{},d=c[p=(n?u.delegateType:u.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=o=d.length;o--;)r=d[o],!s&&g!==r.origType||i&&i.guid!==r.guid||a&&!a.test(r.namespace)||n&&n!==r.selector&&("**"!==n||!r.selector)||(d.splice(o,1),r.selector&&d.delegateCount--,u.remove&&u.remove.call(t,r));l&&!d.length&&(u.teardown&&!1!==u.teardown.call(t,f,v.handle)||m.removeEvent(t,p,v.handle),delete c[p])}else for(p in c)m.event.remove(t,p+e[h],i,n,!0);m.isEmptyObject(c)&&(delete v.handle,m._removeData(t,"events"))}},trigger:function(t,e,n,s){var o,a,l,h,c,u,d,f=[n||r],g=p.call(t,"type")?t.type:t,v=p.call(t,"namespace")?t.namespace.split("."):[];if(l=u=n=n||r,3!==n.nodeType&&8!==n.nodeType&&!vt.test(g+m.event.triggered)&&(g.indexOf(".")>-1&&(g=(v=g.split(".")).shift(),v.sort()),a=g.indexOf(":")<0&&"on"+g,(t=t[m.expando]?t:new m.Event(g,"object"==typeof t&&t)).isTrigger=s?2:3,t.namespace=v.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:m.makeArray(e,[t]),c=m.event.special[g]||{},s||!c.trigger||!1!==c.trigger.apply(n,e))){if(!s&&!c.noBubble&&!m.isWindow(n)){for(h=c.delegateType||g,vt.test(h+g)||(l=l.parentNode);l;l=l.parentNode)f.push(l),u=l;u===(n.ownerDocument||r)&&f.push(u.defaultView||u.parentWindow||i)}for(d=0;(l=f[d++])&&!t.isPropagationStopped();)t.type=d>1?h:c.bindType||g,(o=(m._data(l,"events")||{})[t.type]&&m._data(l,"handle"))&&o.apply(l,e),(o=a&&l[a])&&o.apply&&$(l)&&(t.result=o.apply(l,e),!1===t.result&&t.preventDefault());if(t.type=g,!s&&!t.isDefaultPrevented()&&(!c._default||!1===c._default.apply(f.pop(),e))&&$(n)&&a&&n[g]&&!m.isWindow(n)){(u=n[a])&&(n[a]=null),m.event.triggered=g;try{n[g]()}catch(t){}m.event.triggered=void 0,u&&(n[a]=u)}return t.result}},dispatch:function(t){t=m.event.fix(t);var e,i,n,s,o,r,l=a.call(arguments),h=(m._data(this,"events")||{})[t.type]||[],c=m.event.special[t.type]||{};if(l[0]=t,t.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,t)){for(r=m.event.handlers.call(this,t,h),e=0;(s=r[e++])&&!t.isPropagationStopped();)for(t.currentTarget=s.elem,i=0;(o=s.handlers[i++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!t.rnamespace.test(o.namespace)||(t.handleObj=o,t.data=o.data,void 0!==(n=((m.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,l))&&!1===(t.result=n)&&(t.preventDefault(),t.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,t),t.result}},handlers:function(t,e){var i,n,s,o,r=[],a=e.delegateCount,l=t.target;if(a&&l.nodeType&&("click"!==t.type||isNaN(t.button)||t.button<1))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(!0!==l.disabled||"click"!==t.type)){for(n=[],i=0;i<a;i++)void 0===n[s=(o=e[i]).selector+" "]&&(n[s]=o.needsContext?m(s,this).index(l)>-1:m.find(s,this,null,[l]).length),n[s]&&n.push(o);n.length&&r.push({elem:l,handlers:n})}return a<e.length&&r.push({elem:this,handlers:e.slice(a)}),r},fix:function(t){if(t[m.expando])return t;var e,i,n,s=t.type,o=t,a=this.fixHooks[s];for(a||(this.fixHooks[s]=a=gt.test(s)?this.mouseHooks:mt.test(s)?this.keyHooks:{}),n=a.props?this.props.concat(a.props):this.props,t=new m.Event(o),e=n.length;e--;)t[i=n[e]]=o[i];return t.target||(t.target=o.srcElement||r),3===t.target.nodeType&&(t.target=t.target.parentNode),t.metaKey=!!t.metaKey,a.filter?a.filter(t,o):t},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(t,e){var i,n,s,o=e.button,a=e.fromElement;return null==t.pageX&&null!=e.clientX&&(s=(n=t.target.ownerDocument||r).documentElement,i=n.body,t.pageX=e.clientX+(s&&s.scrollLeft||i&&i.scrollLeft||0)-(s&&s.clientLeft||i&&i.clientLeft||0),t.pageY=e.clientY+(s&&s.scrollTop||i&&i.scrollTop||0)-(s&&s.clientTop||i&&i.clientTop||0)),!t.relatedTarget&&a&&(t.relatedTarget=a===t.target?e.toElement:a),t.which||void 0===o||(t.which=1&o?1:2&o?3:4&o?2:0),t}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==wt()&&this.focus)try{return this.focus(),!1}catch(t){}},delegateType:"focusin"},blur:{trigger:function(){if(this===wt()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(m.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(t){return m.nodeName(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}},simulate:function(t,e,i){var n=m.extend(new m.Event,i,{type:t,isSimulated:!0});m.event.trigger(n,null,e),n.isDefaultPrevented()&&i.preventDefault()}},m.removeEvent=r.removeEventListener?function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)}:function(t,e,i){var n="on"+e;t.detachEvent&&(void 0===t[n]&&(t[n]=null),t.detachEvent(n,i))},m.Event=function(t,e){if(!(this instanceof m.Event))return new m.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?yt:_t):this.type=t,e&&m.extend(this,e),this.timeStamp=t&&t.timeStamp||m.now(),this[m.expando]=!0},m.Event.prototype={constructor:m.Event,isDefaultPrevented:_t,isPropagationStopped:_t,isImmediatePropagationStopped:_t,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=yt,t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=yt,t&&!this.isSimulated&&(t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0)},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=yt,t&&t.stopImmediatePropagation&&t.stopImmediatePropagation(),this.stopPropagation()}},m.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){m.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,s=t.handleObj;return n&&(n===this||m.contains(this,n))||(t.type=s.origType,i=s.handler.apply(this,arguments),t.type=e),i}}}),f.submit||(m.event.special.submit={setup:function(){if(m.nodeName(this,"form"))return!1;m.event.add(this,"click._submit keypress._submit",function(t){var e=t.target,i=m.nodeName(e,"input")||m.nodeName(e,"button")?m.prop(e,"form"):void 0;i&&!m._data(i,"submit")&&(m.event.add(i,"submit._submit",function(t){t._submitBubble=!0}),m._data(i,"submit",!0))})},postDispatch:function(t){t._submitBubble&&(delete t._submitBubble,this.parentNode&&!t.isTrigger&&m.event.simulate("submit",this.parentNode,t))},teardown:function(){if(m.nodeName(this,"form"))return!1;m.event.remove(this,"._submit")}}),f.change||(m.event.special.change={setup:function(){if(ft.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(m.event.add(this,"propertychange._change",function(t){"checked"===t.originalEvent.propertyName&&(this._justChanged=!0)}),m.event.add(this,"click._change",function(t){this._justChanged&&!t.isTrigger&&(this._justChanged=!1),m.event.simulate("change",this,t)})),!1;m.event.add(this,"beforeactivate._change",function(t){var e=t.target;ft.test(e.nodeName)&&!m._data(e,"change")&&(m.event.add(e,"change._change",function(t){!this.parentNode||t.isSimulated||t.isTrigger||m.event.simulate("change",this.parentNode,t)}),m._data(e,"change",!0))})},handle:function(t){var e=t.target;if(this!==e||t.isSimulated||t.isTrigger||"radio"!==e.type&&"checkbox"!==e.type)return t.handleObj.handler.apply(this,arguments)},teardown:function(){return m.event.remove(this,"._change"),!ft.test(this.nodeName)}}),f.focusin||m.each({focus:"focusin",blur:"focusout"},function(t,e){var i=function(t){m.event.simulate(e,t.target,m.event.fix(t))};m.event.special[e]={setup:function(){var n=this.ownerDocument||this,s=m._data(n,e);s||n.addEventListener(t,i,!0),m._data(n,e,(s||0)+1)},teardown:function(){var n=this.ownerDocument||this,s=m._data(n,e)-1;s?m._data(n,e,s):(n.removeEventListener(t,i,!0),m._removeData(n,e))}}}),m.fn.extend({on:function(t,e,i,n){return xt(this,t,e,i,n)},one:function(t,e,i,n){return xt(this,t,e,i,n,1)},off:function(t,e,i){var n,s;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,m(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(s in t)this.off(s,e,t[s]);return this}return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=_t),this.each(function(){m.event.remove(this,t,i,e)})},trigger:function(t,e){return this.each(function(){m.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return m.event.trigger(t,e,i,!0)}});var Ct=/ jQuery\d+="(?:null|\d+)"/g,kt=new RegExp("<(?:"+ot+")[\\s/>]","i"),Dt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,Tt=/<script|<style|<link/i,St=/checked\s*(?:[^=]|=\s*.checked.)/i,Et=/^true\/(.*)/,It=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Nt=rt(r).appendChild(r.createElement("div"));function At(t,e){return m.nodeName(t,"table")&&m.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function Mt(t){return t.type=(null!==m.find.attr(t,"type"))+"/"+t.type,t}function Pt(t){var e=Et.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function Ht(t,e){if(1===e.nodeType&&m.hasData(t)){var i,n,s,o=m._data(t),r=m._data(e,o),a=o.events;if(a)for(i in delete r.handle,r.events={},a)for(n=0,s=a[i].length;n<s;n++)m.event.add(e,i,a[i][n]);r.data&&(r.data=m.extend({},r.data))}}function Ot(t,e){var i,n,s;if(1===e.nodeType){if(i=e.nodeName.toLowerCase(),!f.noCloneEvent&&e[m.expando]){for(n in(s=m._data(e)).events)m.removeEvent(e,n,s.handle);e.removeAttribute(m.expando)}"script"===i&&e.text!==t.text?(Mt(e).text=t.text,Pt(e)):"object"===i?(e.parentNode&&(e.outerHTML=t.outerHTML),f.html5Clone&&t.innerHTML&&!m.trim(e.innerHTML)&&(e.innerHTML=t.innerHTML)):"input"===i&&et.test(t.type)?(e.defaultChecked=e.checked=t.checked,e.value!==t.value&&(e.value=t.value)):"option"===i?e.defaultSelected=e.selected=t.defaultSelected:"input"!==i&&"textarea"!==i||(e.defaultValue=t.defaultValue)}}function zt(t,e,i,n){e=l.apply([],e);var s,o,r,a,h,c,u=0,d=t.length,p=d-1,g=e[0],v=m.isFunction(g);if(v||d>1&&"string"==typeof g&&!f.checkClone&&St.test(g))return t.each(function(s){var o=t.eq(s);v&&(e[0]=g.call(this,s,o.html())),zt(o,e,i,n)});if(d&&(s=(c=pt(e,t[0].ownerDocument,!1,t,n)).firstChild,1===c.childNodes.length&&(c=s),s||n)){for(r=(a=m.map(lt(c,"script"),Mt)).length;u<d;u++)o=c,u!==p&&(o=m.clone(o,!0,!0),r&&m.merge(a,lt(o,"script"))),i.call(t[u],o,u);if(r)for(h=a[a.length-1].ownerDocument,m.map(a,Pt),u=0;u<r;u++)o=a[u],nt.test(o.type||"")&&!m._data(o,"globalEval")&&m.contains(h,o)&&(o.src?m._evalUrl&&m._evalUrl(o.src):m.globalEval((o.text||o.textContent||o.innerHTML||"").replace(It,"")));c=s=null}return t}function Wt(t,e,i){for(var n,s=e?m.filter(e,t):t,o=0;null!=(n=s[o]);o++)i||1!==n.nodeType||m.cleanData(lt(n)),n.parentNode&&(i&&m.contains(n.ownerDocument,n)&&ht(lt(n,"script")),n.parentNode.removeChild(n));return t}m.extend({htmlPrefilter:function(t){return t.replace(Dt,"<$1></$2>")},clone:function(t,e,i){var n,s,o,r,a,l=m.contains(t.ownerDocument,t);if(f.html5Clone||m.isXMLDoc(t)||!kt.test("<"+t.nodeName+">")?o=t.cloneNode(!0):(Nt.innerHTML=t.outerHTML,Nt.removeChild(o=Nt.firstChild)),!(f.noCloneEvent&&f.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||m.isXMLDoc(t)))for(n=lt(o),a=lt(t),r=0;null!=(s=a[r]);++r)n[r]&&Ot(s,n[r]);if(e)if(i)for(a=a||lt(t),n=n||lt(o),r=0;null!=(s=a[r]);r++)Ht(s,n[r]);else Ht(t,o);return(n=lt(o,"script")).length>0&&ht(n,!l&&lt(t,"script")),n=a=s=null,o},cleanData:function(t,e){for(var i,n,s,r,a=0,l=m.expando,h=m.cache,c=f.attributes,u=m.event.special;null!=(i=t[a]);a++)if((e||$(i))&&(r=(s=i[l])&&h[s])){if(r.events)for(n in r.events)u[n]?m.event.remove(i,n):m.removeEvent(i,n,r.handle);h[s]&&(delete h[s],c||void 0===i.removeAttribute?i[l]=void 0:i.removeAttribute(l),o.push(s))}}}),m.fn.extend({domManip:zt,detach:function(t){return Wt(this,t,!0)},remove:function(t){return Wt(this,t)},text:function(t){return tt(this,function(t){return void 0===t?m.text(this):this.empty().append((this[0]&&this[0].ownerDocument||r).createTextNode(t))},null,t,arguments.length)},append:function(){return zt(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||At(this,t).appendChild(t)})},prepend:function(){return zt(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=At(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return zt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return zt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++){for(1===t.nodeType&&m.cleanData(lt(t,!1));t.firstChild;)t.removeChild(t.firstChild);t.options&&m.nodeName(t,"select")&&(t.options.length=0)}return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return m.clone(this,t,e)})},html:function(t){return tt(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t)return 1===e.nodeType?e.innerHTML.replace(Ct,""):void 0;if("string"==typeof t&&!Tt.test(t)&&(f.htmlSerialize||!kt.test(t))&&(f.leadingWhitespace||!st.test(t))&&!at[(it.exec(t)||["",""])[1].toLowerCase()]){t=m.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(m.cleanData(lt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return zt(this,arguments,function(e){var i=this.parentNode;m.inArray(this,t)<0&&(m.cleanData(lt(this)),i&&i.replaceChild(e,this))},t)}}),m.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){m.fn[t]=function(t){for(var i,n=0,s=[],o=m(t),r=o.length-1;n<=r;n++)i=n===r?this:this.clone(!0),m(o[n])[e](i),h.apply(s,i.get());return this.pushStack(s)}});var Lt,$t={HTML:"block",BODY:"block"};function Ft(t,e){var i=m(e.createElement(t)).appendTo(e.body),n=m.css(i[0],"display");return i.detach(),n}function Rt(t){var e=r,i=$t[t];return i||("none"!==(i=Ft(t,e))&&i||((e=((Lt=(Lt||m("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement))[0].contentWindow||Lt[0].contentDocument).document).write(),e.close(),i=Ft(t,e),Lt.detach()),$t[t]=i),i}var jt=/^margin/,qt=new RegExp("^("+U+")(?!px)[a-z%]+$","i"),Bt=function(t,e,i,n){var s,o,r={};for(o in e)r[o]=t.style[o],t.style[o]=e[o];for(o in s=i.apply(t,n||[]),e)t.style[o]=r[o];return s},Yt=r.documentElement;!function(){var t,e,n,s,o,a,l=r.createElement("div"),h=r.createElement("div");function c(){var c,u,d=r.documentElement;d.appendChild(l),h.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",t=n=a=!1,e=o=!0,i.getComputedStyle&&(u=i.getComputedStyle(h),t="1%"!==(u||{}).top,a="2px"===(u||{}).marginLeft,n="4px"===(u||{width:"4px"}).width,h.style.marginRight="50%",e="4px"===(u||{marginRight:"4px"}).marginRight,(c=h.appendChild(r.createElement("div"))).style.cssText=h.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",c.style.marginRight=c.style.width="0",h.style.width="1px",o=!parseFloat((i.getComputedStyle(c)||{}).marginRight),h.removeChild(c)),h.style.display="none",(s=0===h.getClientRects().length)&&(h.style.display="",h.innerHTML="<table><tr><td></td><td>t</td></tr></table>",h.childNodes[0].style.borderCollapse="separate",(c=h.getElementsByTagName("td"))[0].style.cssText="margin:0;border:0;padding:0;display:none",(s=0===c[0].offsetHeight)&&(c[0].style.display="",c[1].style.display="none",s=0===c[0].offsetHeight)),d.removeChild(l)}h.style&&(h.style.cssText="float:left;opacity:.5",f.opacity="0.5"===h.style.opacity,f.cssFloat=!!h.style.cssFloat,h.style.backgroundClip="content-box",h.cloneNode(!0).style.backgroundClip="",f.clearCloneStyle="content-box"===h.style.backgroundClip,(l=r.createElement("div")).style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",h.innerHTML="",l.appendChild(h),f.boxSizing=""===h.style.boxSizing||""===h.style.MozBoxSizing||""===h.style.WebkitBoxSizing,m.extend(f,{reliableHiddenOffsets:function(){return null==t&&c(),s},boxSizingReliable:function(){return null==t&&c(),n},pixelMarginRight:function(){return null==t&&c(),e},pixelPosition:function(){return null==t&&c(),t},reliableMarginRight:function(){return null==t&&c(),o},reliableMarginLeft:function(){return null==t&&c(),a}}))}();var Ut,Kt,Vt=/^(top|right|bottom|left)$/;function Xt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}i.getComputedStyle?(Ut=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=i),e.getComputedStyle(t)},Kt=function(t,e,i){var n,s,o,r,a=t.style;return""!==(r=(i=i||Ut(t))?i.getPropertyValue(e)||i[e]:void 0)&&void 0!==r||m.contains(t.ownerDocument,t)||(r=m.style(t,e)),i&&!f.pixelMarginRight()&&qt.test(r)&&jt.test(e)&&(n=a.width,s=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=r,r=i.width,a.width=n,a.minWidth=s,a.maxWidth=o),void 0===r?r:r+""}):Yt.currentStyle&&(Ut=function(t){return t.currentStyle},Kt=function(t,e,i){var n,s,o,r,a=t.style;return null==(r=(i=i||Ut(t))?i[e]:void 0)&&a&&a[e]&&(r=a[e]),qt.test(r)&&!Vt.test(e)&&(n=a.left,(o=(s=t.runtimeStyle)&&s.left)&&(s.left=t.currentStyle.left),a.left="fontSize"===e?"1em":r,r=a.pixelLeft+"px",a.left=n,o&&(s.left=o)),void 0===r?r:r+""||"auto"});var Gt=/alpha\([^)]*\)/i,Qt=/opacity\s*=\s*([^)]*)/i,Jt=/^(none|table(?!-c[ea]).+)/,Zt=new RegExp("^("+U+")(.*)$","i"),te={position:"absolute",visibility:"hidden",display:"block"},ee={letterSpacing:"0",fontWeight:"400"},ie=["Webkit","O","Moz","ms"],ne=r.createElement("div").style;function se(t){if(t in ne)return t;for(var e=t.charAt(0).toUpperCase()+t.slice(1),i=ie.length;i--;)if((t=ie[i]+e)in ne)return t}function oe(t,e){for(var i,n,s,o=[],r=0,a=t.length;r<a;r++)(n=t[r]).style&&(o[r]=m._data(n,"olddisplay"),i=n.style.display,e?(o[r]||"none"!==i||(n.style.display=""),""===n.style.display&&X(n)&&(o[r]=m._data(n,"olddisplay",Rt(n.nodeName)))):(s=X(n),(i&&"none"!==i||!s)&&m._data(n,"olddisplay",s?i:m.css(n,"display"))));for(r=0;r<a;r++)(n=t[r]).style&&(e&&"none"!==n.style.display&&""!==n.style.display||(n.style.display=e?o[r]||"":"none"));return t}function re(t,e,i){var n=Zt.exec(e);return n?Math.max(0,n[1]-(i||0))+(n[2]||"px"):e}function ae(t,e,i,n,s){for(var o=i===(n?"border":"content")?4:"width"===e?1:0,r=0;o<4;o+=2)"margin"===i&&(r+=m.css(t,i+V[o],!0,s)),n?("content"===i&&(r-=m.css(t,"padding"+V[o],!0,s)),"margin"!==i&&(r-=m.css(t,"border"+V[o]+"Width",!0,s))):(r+=m.css(t,"padding"+V[o],!0,s),"padding"!==i&&(r+=m.css(t,"border"+V[o]+"Width",!0,s)));return r}function le(t,e,i){var n=!0,s="width"===e?t.offsetWidth:t.offsetHeight,o=Ut(t),r=f.boxSizing&&"border-box"===m.css(t,"boxSizing",!1,o);if(s<=0||null==s){if(((s=Kt(t,e,o))<0||null==s)&&(s=t.style[e]),qt.test(s))return s;n=r&&(f.boxSizingReliable()||s===t.style[e]),s=parseFloat(s)||0}return s+ae(t,e,i||(r?"border":"content"),n,o)+"px"}function he(t,e,i,n,s){return new he.prototype.init(t,e,i,n,s)}m.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=Kt(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:f.cssFloat?"cssFloat":"styleFloat"},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,o,r,a=m.camelCase(e),l=t.style;if(e=m.cssProps[a]||(m.cssProps[a]=se(a)||a),r=m.cssHooks[e]||m.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(s=r.get(t,!1,n))?s:l[e];if("string"==(o=typeof i)&&(s=K.exec(i))&&s[1]&&(i=G(t,e,s),o="number"),null!=i&&i==i&&("number"===o&&(i+=s&&s[3]||(m.cssNumber[a]?"":"px")),f.clearCloneStyle||""!==i||0!==e.indexOf("background")||(l[e]="inherit"),!(r&&"set"in r&&void 0===(i=r.set(t,i,n)))))try{l[e]=i}catch(t){}}},css:function(t,e,i,n){var s,o,r,a=m.camelCase(e);return e=m.cssProps[a]||(m.cssProps[a]=se(a)||a),(r=m.cssHooks[e]||m.cssHooks[a])&&"get"in r&&(o=r.get(t,!0,i)),void 0===o&&(o=Kt(t,e,n)),"normal"===o&&e in ee&&(o=ee[e]),""===i||i?(s=parseFloat(o),!0===i||isFinite(s)?s||0:o):o}}),m.each(["height","width"],function(t,e){m.cssHooks[e]={get:function(t,i,n){if(i)return Jt.test(m.css(t,"display"))&&0===t.offsetWidth?Bt(t,te,function(){return le(t,e,n)}):le(t,e,n)},set:function(t,i,n){var s=n&&Ut(t);return re(0,i,n?ae(t,e,n,f.boxSizing&&"border-box"===m.css(t,"boxSizing",!1,s),s):0)}}}),f.opacity||(m.cssHooks.opacity={get:function(t,e){return Qt.test((e&&t.currentStyle?t.currentStyle.filter:t.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":e?"1":""},set:function(t,e){var i=t.style,n=t.currentStyle,s=m.isNumeric(e)?"alpha(opacity="+100*e+")":"",o=n&&n.filter||i.filter||"";i.zoom=1,(e>=1||""===e)&&""===m.trim(o.replace(Gt,""))&&i.removeAttribute&&(i.removeAttribute("filter"),""===e||n&&!n.filter)||(i.filter=Gt.test(o)?o.replace(Gt,s):o+" "+s)}}),m.cssHooks.marginRight=Xt(f.reliableMarginRight,function(t,e){if(e)return Bt(t,{display:"inline-block"},Kt,[t,"marginRight"])}),m.cssHooks.marginLeft=Xt(f.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Kt(t,"marginLeft"))||(m.contains(t.ownerDocument,t)?t.getBoundingClientRect().left-Bt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}):0))+"px"}),m.each({margin:"",padding:"",border:"Width"},function(t,e){m.cssHooks[t+e]={expand:function(i){for(var n=0,s={},o="string"==typeof i?i.split(" "):[i];n<4;n++)s[t+V[n]+e]=o[n]||o[n-2]||o[0];return s}},jt.test(t)||(m.cssHooks[t+e].set=re)}),m.fn.extend({css:function(t,e){return tt(this,function(t,e,i){var n,s,o={},r=0;if(m.isArray(e)){for(n=Ut(t),s=e.length;r<s;r++)o[e[r]]=m.css(t,e[r],!1,n);return o}return void 0!==i?m.style(t,e,i):m.css(t,e)},t,e,arguments.length>1)},show:function(){return oe(this,!0)},hide:function(){return oe(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){X(this)?m(this).show():m(this).hide()})}}),m.Tween=he,he.prototype={constructor:he,init:function(t,e,i,n,s,o){this.elem=t,this.prop=i,this.easing=s||m.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=o||(m.cssNumber[i]?"":"px")},cur:function(){var t=he.propHooks[this.prop];return t&&t.get?t.get(this):he.propHooks._default.get(this)},run:function(t){var e,i=he.propHooks[this.prop];return this.options.duration?this.pos=e=m.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):he.propHooks._default.set(this),this}},he.prototype.init.prototype=he.prototype,he.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=m.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){m.fx.step[t.prop]?m.fx.step[t.prop](t):1!==t.elem.nodeType||null==t.elem.style[m.cssProps[t.prop]]&&!m.cssHooks[t.prop]?t.elem[t.prop]=t.now:m.style(t.elem,t.prop,t.now+t.unit)}}},he.propHooks.scrollTop=he.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},m.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},m.fx=he.prototype.init,m.fx.step={};var ce,ue,de,pe,fe,me,ge,ve=/^(?:toggle|show|hide)$/,be=/queueHooks$/;function ye(){return i.setTimeout(function(){ce=void 0}),ce=m.now()}function _e(t,e){var i,n={height:t},s=0;for(e=e?1:0;s<4;s+=2-e)n["margin"+(i=V[s])]=n["padding"+i]=t;return e&&(n.opacity=n.width=t),n}function we(t,e,i){for(var n,s=(xe.tweeners[e]||[]).concat(xe.tweeners["*"]),o=0,r=s.length;o<r;o++)if(n=s[o].call(i,e,t))return n}function xe(t,e,i){var n,s,o=0,r=xe.prefilters.length,a=m.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=ce||ye(),i=Math.max(0,h.startTime+h.duration-e),n=1-(i/h.duration||0),o=0,r=h.tweens.length;o<r;o++)h.tweens[o].run(n);return a.notifyWith(t,[h,n,i]),n<1&&r?i:(a.resolveWith(t,[h]),!1)},h=a.promise({elem:t,props:m.extend({},e),opts:m.extend(!0,{specialEasing:{},easing:m.easing._default},i),originalProperties:e,originalOptions:i,startTime:ce||ye(),duration:i.duration,tweens:[],createTween:function(e,i){var n=m.Tween(t,h.opts,e,i,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(n),n},stop:function(e){var i=0,n=e?h.tweens.length:0;if(s)return this;for(s=!0;i<n;i++)h.tweens[i].run(1);return e?(a.notifyWith(t,[h,1,0]),a.resolveWith(t,[h,e])):a.rejectWith(t,[h,e]),this}}),c=h.props;for(function(t,e){var i,n,s,o,r;for(i in t)if(s=e[n=m.camelCase(i)],o=t[i],m.isArray(o)&&(s=o[1],o=t[i]=o[0]),i!==n&&(t[n]=o,delete t[i]),(r=m.cssHooks[n])&&"expand"in r)for(i in o=r.expand(o),delete t[n],o)i in t||(t[i]=o[i],e[i]=s);else e[n]=s}(c,h.opts.specialEasing);o<r;o++)if(n=xe.prefilters[o].call(h,t,c,h.opts))return m.isFunction(n.stop)&&(m._queueHooks(h.elem,h.opts.queue).stop=m.proxy(n.stop,n)),n;return m.map(c,we,h),m.isFunction(h.opts.start)&&h.opts.start.call(t,h),m.fx.timer(m.extend(l,{elem:t,anim:h,queue:h.opts.queue})),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always)}m.Animation=m.extend(xe,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return G(i.elem,t,K.exec(e),i),i}]},tweener:function(t,e){m.isFunction(t)?(e=t,t=["*"]):t=t.match(O);for(var i,n=0,s=t.length;n<s;n++)i=t[n],xe.tweeners[i]=xe.tweeners[i]||[],xe.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,s,o,r,a,l,h,c=this,u={},d=t.style,p=t.nodeType&&X(t),g=m._data(t,"fxshow");for(n in i.queue||(null==(a=m._queueHooks(t,"fx")).unqueued&&(a.unqueued=0,l=a.empty.fire,a.empty.fire=function(){a.unqueued||l()}),a.unqueued++,c.always(function(){c.always(function(){a.unqueued--,m.queue(t,"fx").length||a.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(i.overflow=[d.overflow,d.overflowX,d.overflowY],"inline"===("none"===(h=m.css(t,"display"))?m._data(t,"olddisplay")||Rt(t.nodeName):h)&&"none"===m.css(t,"float")&&(f.inlineBlockNeedsLayout&&"inline"!==Rt(t.nodeName)?d.zoom=1:d.display="inline-block")),i.overflow&&(d.overflow="hidden",f.shrinkWrapBlocks()||c.always(function(){d.overflow=i.overflow[0],d.overflowX=i.overflow[1],d.overflowY=i.overflow[2]})),e)if(s=e[n],ve.exec(s)){if(delete e[n],o=o||"toggle"===s,s===(p?"hide":"show")){if("show"!==s||!g||void 0===g[n])continue;p=!0}u[n]=g&&g[n]||m.style(t,n)}else h=void 0;if(m.isEmptyObject(u))"inline"===("none"===h?Rt(t.nodeName):h)&&(d.display=h);else for(n in g?"hidden"in g&&(p=g.hidden):g=m._data(t,"fxshow",{}),o&&(g.hidden=!p),p?m(t).show():c.done(function(){m(t).hide()}),c.done(function(){var e;for(e in m._removeData(t,"fxshow"),u)m.style(t,e,u[e])}),u)r=we(p?g[n]:0,n,c),n in g||(g[n]=r.start,p&&(r.end=r.start,r.start="width"===n||"height"===n?1:0))}],prefilter:function(t,e){e?xe.prefilters.unshift(t):xe.prefilters.push(t)}}),m.speed=function(t,e,i){var n=t&&"object"==typeof t?m.extend({},t):{complete:i||!i&&e||m.isFunction(t)&&t,duration:t,easing:i&&e||e&&!m.isFunction(e)&&e};return n.duration=m.fx.off?0:"number"==typeof n.duration?n.duration:n.duration in m.fx.speeds?m.fx.speeds[n.duration]:m.fx.speeds._default,null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){m.isFunction(n.old)&&n.old.call(this),n.queue&&m.dequeue(this,n.queue)},n},m.fn.extend({fadeTo:function(t,e,i,n){return this.filter(X).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var s=m.isEmptyObject(t),o=m.speed(e,i,n),r=function(){var e=xe(this,m.extend({},t),o);(s||m._data(this,"finish"))&&e.stop(!0)};return r.finish=r,s||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&!1!==t&&this.queue(t||"fx",[]),this.each(function(){var e=!0,s=null!=t&&t+"queueHooks",o=m.timers,r=m._data(this);if(s)r[s]&&r[s].stop&&n(r[s]);else for(s in r)r[s]&&r[s].stop&&be.test(s)&&n(r[s]);for(s=o.length;s--;)o[s].elem!==this||null!=t&&o[s].queue!==t||(o[s].anim.stop(i),e=!1,o.splice(s,1));!e&&i||m.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,i=m._data(this),n=i[t+"queue"],s=i[t+"queueHooks"],o=m.timers,r=n?n.length:0;for(i.finish=!0,m.queue(this,t,[]),s&&s.stop&&s.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish})}}),m.each(["toggle","show","hide"],function(t,e){var i=m.fn[e];m.fn[e]=function(t,n,s){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(_e(e,!0),t,n,s)}}),m.each({slideDown:_e("show"),slideUp:_e("hide"),slideToggle:_e("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){m.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}}),m.timers=[],m.fx.tick=function(){var t,e=m.timers,i=0;for(ce=m.now();i<e.length;i++)(t=e[i])()||e[i]!==t||e.splice(i--,1);e.length||m.fx.stop(),ce=void 0},m.fx.timer=function(t){m.timers.push(t),t()?m.fx.start():m.timers.pop()},m.fx.interval=13,m.fx.start=function(){ue||(ue=i.setInterval(m.fx.tick,m.fx.interval))},m.fx.stop=function(){i.clearInterval(ue),ue=null},m.fx.speeds={slow:600,fast:200,_default:400},m.fn.delay=function(t,e){return t=m.fx&&m.fx.speeds[t]||t,e=e||"fx",this.queue(e,function(e,n){var s=i.setTimeout(e,t);n.stop=function(){i.clearTimeout(s)}})},pe=r.createElement("input"),fe=r.createElement("div"),me=r.createElement("select"),ge=me.appendChild(r.createElement("option")),(fe=r.createElement("div")).setAttribute("className","t"),fe.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",de=fe.getElementsByTagName("a")[0],pe.setAttribute("type","checkbox"),fe.appendChild(pe),(de=fe.getElementsByTagName("a")[0]).style.cssText="top:1px",f.getSetAttribute="t"!==fe.className,f.style=/top/.test(de.getAttribute("style")),f.hrefNormalized="/a"===de.getAttribute("href"),f.checkOn=!!pe.value,f.optSelected=ge.selected,f.enctype=!!r.createElement("form").enctype,me.disabled=!0,f.optDisabled=!ge.disabled,(pe=r.createElement("input")).setAttribute("value",""),f.input=""===pe.getAttribute("value"),pe.value="t",pe.setAttribute("type","radio"),f.radioValue="t"===pe.value;var Ce=/\r/g,ke=/[\x20\t\r\n\f]+/g;m.fn.extend({val:function(t){var e,i,n,s=this[0];return arguments.length?(n=m.isFunction(t),this.each(function(i){var s;1===this.nodeType&&(null==(s=n?t.call(this,i,m(this).val()):t)?s="":"number"==typeof s?s+="":m.isArray(s)&&(s=m.map(s,function(t){return null==t?"":t+""})),(e=m.valHooks[this.type]||m.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,s,"value")||(this.value=s))})):s?(e=m.valHooks[s.type]||m.valHooks[s.nodeName.toLowerCase()])&&"get"in e&&void 0!==(i=e.get(s,"value"))?i:"string"==typeof(i=s.value)?i.replace(Ce,""):null==i?"":i:void 0}}),m.extend({valHooks:{option:{get:function(t){var e=m.find.attr(t,"value");return null!=e?e:m.trim(m.text(t)).replace(ke," ")}},select:{get:function(t){for(var e,i,n=t.options,s=t.selectedIndex,o="select-one"===t.type||s<0,r=o?null:[],a=o?s+1:n.length,l=s<0?a:o?s:0;l<a;l++)if(((i=n[l]).selected||l===s)&&(f.optDisabled?!i.disabled:null===i.getAttribute("disabled"))&&(!i.parentNode.disabled||!m.nodeName(i.parentNode,"optgroup"))){if(e=m(i).val(),o)return e;r.push(e)}return r},set:function(t,e){for(var i,n,s=t.options,o=m.makeArray(e),r=s.length;r--;)if(n=s[r],m.inArray(m.valHooks.option.get(n),o)>-1)try{n.selected=i=!0}catch(t){n.scrollHeight}else n.selected=!1;return i||(t.selectedIndex=-1),s}}}}),m.each(["radio","checkbox"],function(){m.valHooks[this]={set:function(t,e){if(m.isArray(e))return t.checked=m.inArray(m(t).val(),e)>-1}},f.checkOn||(m.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var De,Te,Se=m.expr.attrHandle,Ee=/^(?:checked|selected)$/i,Ie=f.getSetAttribute,Ne=f.input;m.fn.extend({attr:function(t,e){return tt(this,m.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){m.removeAttr(this,t)})}}),m.extend({attr:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?m.prop(t,e,i):(1===o&&m.isXMLDoc(t)||(e=e.toLowerCase(),s=m.attrHooks[e]||(m.expr.match.bool.test(e)?Te:De)),void 0!==i?null===i?void m.removeAttr(t,e):s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:(t.setAttribute(e,i+""),i):s&&"get"in s&&null!==(n=s.get(t,e))?n:null==(n=m.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!f.radioValue&&"radio"===e&&m.nodeName(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n,s=0,o=e&&e.match(O);if(o&&1===t.nodeType)for(;i=o[s++];)n=m.propFix[i]||i,m.expr.match.bool.test(i)?Ne&&Ie||!Ee.test(i)?t[n]=!1:t[m.camelCase("default-"+i)]=t[n]=!1:m.attr(t,i,""),t.removeAttribute(Ie?i:n)}}),Te={set:function(t,e,i){return!1===e?m.removeAttr(t,i):Ne&&Ie||!Ee.test(i)?t.setAttribute(!Ie&&m.propFix[i]||i,i):t[m.camelCase("default-"+i)]=t[i]=!0,i}},m.each(m.expr.match.bool.source.match(/\w+/g),function(t,e){var i=Se[e]||m.find.attr;Ne&&Ie||!Ee.test(e)?Se[e]=function(t,e,n){var s,o;return n||(o=Se[e],Se[e]=s,s=null!=i(t,e,n)?e.toLowerCase():null,Se[e]=o),s}:Se[e]=function(t,e,i){if(!i)return t[m.camelCase("default-"+e)]?e.toLowerCase():null}}),Ne&&Ie||(m.attrHooks.value={set:function(t,e,i){if(!m.nodeName(t,"input"))return De&&De.set(t,e,i);t.defaultValue=e}}),Ie||(De={set:function(t,e,i){var n=t.getAttributeNode(i);if(n||t.setAttributeNode(n=t.ownerDocument.createAttribute(i)),n.value=e+="","value"===i||e===t.getAttribute(i))return e}},Se.id=Se.name=Se.coords=function(t,e,i){var n;if(!i)return(n=t.getAttributeNode(e))&&""!==n.value?n.value:null},m.valHooks.button={get:function(t,e){var i=t.getAttributeNode(e);if(i&&i.specified)return i.value},set:De.set},m.attrHooks.contenteditable={set:function(t,e,i){De.set(t,""!==e&&e,i)}},m.each(["width","height"],function(t,e){m.attrHooks[e]={set:function(t,i){if(""===i)return t.setAttribute(e,"auto"),i}}})),f.style||(m.attrHooks.style={get:function(t){return t.style.cssText||void 0},set:function(t,e){return t.style.cssText=e+""}});var Ae=/^(?:input|select|textarea|button|object)$/i,Me=/^(?:a|area)$/i;m.fn.extend({prop:function(t,e){return tt(this,m.prop,t,e,arguments.length>1)},removeProp:function(t){return t=m.propFix[t]||t,this.each(function(){try{this[t]=void 0,delete this[t]}catch(t){}})}}),m.extend({prop:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&m.isXMLDoc(t)||(e=m.propFix[e]||e,s=m.propHooks[e]),void 0!==i?s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:t[e]=i:s&&"get"in s&&null!==(n=s.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=m.find.attr(t,"tabindex");return e?parseInt(e,10):Ae.test(t.nodeName)||Me.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),f.hrefNormalized||m.each(["href","src"],function(t,e){m.propHooks[e]={get:function(t){return t.getAttribute(e,4)}}}),f.optSelected||(m.propHooks.selected={get:function(t){var e=t.parentNode;return e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex),null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),m.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){m.propFix[this.toLowerCase()]=this}),f.enctype||(m.propFix.enctype="encoding");var Pe=/[\t\r\n\f]/g;function He(t){return m.attr(t,"class")||""}m.fn.extend({addClass:function(t){var e,i,n,s,o,r,a,l=0;if(m.isFunction(t))return this.each(function(e){m(this).addClass(t.call(this,e,He(this)))});if("string"==typeof t&&t)for(e=t.match(O)||[];i=this[l++];)if(s=He(i),n=1===i.nodeType&&(" "+s+" ").replace(Pe," ")){for(r=0;o=e[r++];)n.indexOf(" "+o+" ")<0&&(n+=o+" ");s!==(a=m.trim(n))&&m.attr(i,"class",a)}return this},removeClass:function(t){var e,i,n,s,o,r,a,l=0;if(m.isFunction(t))return this.each(function(e){m(this).removeClass(t.call(this,e,He(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof t&&t)for(e=t.match(O)||[];i=this[l++];)if(s=He(i),n=1===i.nodeType&&(" "+s+" ").replace(Pe," ")){for(r=0;o=e[r++];)for(;n.indexOf(" "+o+" ")>-1;)n=n.replace(" "+o+" "," ");s!==(a=m.trim(n))&&m.attr(i,"class",a)}return this},toggleClass:function(t,e){var i=typeof t;return"boolean"==typeof e&&"string"===i?e?this.addClass(t):this.removeClass(t):m.isFunction(t)?this.each(function(i){m(this).toggleClass(t.call(this,i,He(this),e),e)}):this.each(function(){var e,n,s,o;if("string"===i)for(n=0,s=m(this),o=t.match(O)||[];e=o[n++];)s.hasClass(e)?s.removeClass(e):s.addClass(e);else void 0!==t&&"boolean"!==i||((e=He(this))&&m._data(this,"__className__",e),m.attr(this,"class",e||!1===t?"":m._data(this,"__className__")||""))})},hasClass:function(t){var e,i,n=0;for(e=" "+t+" ";i=this[n++];)if(1===i.nodeType&&(" "+He(i)+" ").replace(Pe," ").indexOf(e)>-1)return!0;return!1}}),m.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(t,e){m.fn[e]=function(t,i){return arguments.length>0?this.on(e,null,t,i):this.trigger(e)}}),m.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}});var Oe=i.location,ze=m.now(),We=/\?/,Le=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;m.parseJSON=function(t){if(i.JSON&&i.JSON.parse)return i.JSON.parse(t+"");var e,n=null,s=m.trim(t+"");return s&&!m.trim(s.replace(Le,function(t,i,s,o){return e&&i&&(n=0),0===n?t:(e=s||i,n+=!o-!s,"")}))?Function("return "+s)():m.error("Invalid JSON: "+t)},m.parseXML=function(t){var e;if(!t||"string"!=typeof t)return null;try{i.DOMParser?e=(new i.DOMParser).parseFromString(t,"text/xml"):((e=new i.ActiveXObject("Microsoft.XMLDOM")).async="false",e.loadXML(t))}catch(t){e=void 0}return e&&e.documentElement&&!e.getElementsByTagName("parsererror").length||m.error("Invalid XML: "+t),e};var $e=/#.*$/,Fe=/([?&])_=[^&]*/,Re=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,je=/^(?:GET|HEAD)$/,qe=/^\/\//,Be=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ye={},Ue={},Ke="*/".concat("*"),Ve=Oe.href,Xe=Be.exec(Ve.toLowerCase())||[];function Ge(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,s=0,o=e.toLowerCase().match(O)||[];if(m.isFunction(i))for(;n=o[s++];)"+"===n.charAt(0)?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function Qe(t,e,i,n){var s={},o=t===Ue;function r(a){var l;return s[a]=!0,m.each(t[a]||[],function(t,a){var h=a(e,i,n);return"string"!=typeof h||o||s[h]?o?!(l=h):void 0:(e.dataTypes.unshift(h),r(h),!1)}),l}return r(e.dataTypes[0])||!s["*"]&&r("*")}function Je(t,e){var i,n,s=m.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((s[n]?t:i||(i={}))[n]=e[n]);return i&&m.extend(!0,t,i),t}m.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ve,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Xe[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ke,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":m.parseJSON,"text xml":m.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Je(Je(t,m.ajaxSettings),e):Je(m.ajaxSettings,t)},ajaxPrefilter:Ge(Ye),ajaxTransport:Ge(Ue),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,s,o,r,a,l,h,c,u=m.ajaxSetup({},e),d=u.context||u,p=u.context&&(d.nodeType||d.jquery)?m(d):m.event,f=m.Deferred(),g=m.Callbacks("once memory"),v=u.statusCode||{},b={},y={},_=0,w="canceled",x={readyState:0,getResponseHeader:function(t){var e;if(2===_){if(!c)for(c={};e=Re.exec(r);)c[e[1].toLowerCase()]=e[2];e=c[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return 2===_?r:null},setRequestHeader:function(t,e){var i=t.toLowerCase();return _||(t=y[i]=y[i]||t,b[t]=e),this},overrideMimeType:function(t){return _||(u.mimeType=t),this},statusCode:function(t){var e;if(t)if(_<2)for(e in t)v[e]=[v[e],t[e]];else x.always(t[x.status]);return this},abort:function(t){var e=t||w;return h&&h.abort(e),C(0,e),this}};if(f.promise(x).complete=g.add,x.success=x.done,x.error=x.fail,u.url=((t||u.url||Ve)+"").replace($e,"").replace(qe,Xe[1]+"//"),u.type=e.method||e.type||u.method||u.type,u.dataTypes=m.trim(u.dataType||"*").toLowerCase().match(O)||[""],null==u.crossDomain&&(n=Be.exec(u.url.toLowerCase()),u.crossDomain=!(!n||n[1]===Xe[1]&&n[2]===Xe[2]&&(n[3]||("http:"===n[1]?"80":"443"))===(Xe[3]||("http:"===Xe[1]?"80":"443")))),u.data&&u.processData&&"string"!=typeof u.data&&(u.data=m.param(u.data,u.traditional)),Qe(Ye,u,e,x),2===_)return x;for(s in(l=m.event&&u.global)&&0==m.active++&&m.event.trigger("ajaxStart"),u.type=u.type.toUpperCase(),u.hasContent=!je.test(u.type),o=u.url,u.hasContent||(u.data&&(o=u.url+=(We.test(o)?"&":"?")+u.data,delete u.data),!1===u.cache&&(u.url=Fe.test(o)?o.replace(Fe,"$1_="+ze++):o+(We.test(o)?"&":"?")+"_="+ze++)),u.ifModified&&(m.lastModified[o]&&x.setRequestHeader("If-Modified-Since",m.lastModified[o]),m.etag[o]&&x.setRequestHeader("If-None-Match",m.etag[o])),(u.data&&u.hasContent&&!1!==u.contentType||e.contentType)&&x.setRequestHeader("Content-Type",u.contentType),x.setRequestHeader("Accept",u.dataTypes[0]&&u.accepts[u.dataTypes[0]]?u.accepts[u.dataTypes[0]]+("*"!==u.dataTypes[0]?", "+Ke+"; q=0.01":""):u.accepts["*"]),u.headers)x.setRequestHeader(s,u.headers[s]);if(u.beforeSend&&(!1===u.beforeSend.call(d,x,u)||2===_))return x.abort();for(s in w="abort",{success:1,error:1,complete:1})x[s](u[s]);if(h=Qe(Ue,u,e,x)){if(x.readyState=1,l&&p.trigger("ajaxSend",[x,u]),2===_)return x;u.async&&u.timeout>0&&(a=i.setTimeout(function(){x.abort("timeout")},u.timeout));try{_=1,h.send(b,C)}catch(t){if(!(_<2))throw t;C(-1,t)}}else C(-1,"No Transport");function C(t,e,n,s){var c,b,y,w,C,k=e;2!==_&&(_=2,a&&i.clearTimeout(a),h=void 0,r=s||"",x.readyState=t>0?4:0,c=t>=200&&t<300||304===t,n&&(w=function(t,e,i){for(var n,s,o,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===s&&(s=t.mimeType||e.getResponseHeader("Content-Type"));if(s)for(r in a)if(a[r]&&a[r].test(s)){l.unshift(r);break}if(l[0]in i)o=l[0];else{for(r in i){if(!l[0]||t.converters[r+" "+l[0]]){o=r;break}n||(n=r)}o=o||n}if(o)return o!==l[0]&&l.unshift(o),i[o]}(u,x,n)),w=function(t,e,i,n){var s,o,r,a,l,h={},c=t.dataTypes.slice();if(c[1])for(r in t.converters)h[r.toLowerCase()]=t.converters[r];for(o=c.shift();o;)if(t.responseFields[o]&&(i[t.responseFields[o]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(r=h[l+" "+o]||h["* "+o]))for(s in h)if((a=s.split(" "))[1]===o&&(r=h[l+" "+a[0]]||h["* "+a[0]])){!0===r?r=h[s]:!0!==h[s]&&(o=a[0],c.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(u,w,x,c),c?(u.ifModified&&((C=x.getResponseHeader("Last-Modified"))&&(m.lastModified[o]=C),(C=x.getResponseHeader("etag"))&&(m.etag[o]=C)),204===t||"HEAD"===u.type?k="nocontent":304===t?k="notmodified":(k=w.state,b=w.data,c=!(y=w.error))):(y=k,!t&&k||(k="error",t<0&&(t=0))),x.status=t,x.statusText=(e||k)+"",c?f.resolveWith(d,[b,k,x]):f.rejectWith(d,[x,k,y]),x.statusCode(v),v=void 0,l&&p.trigger(c?"ajaxSuccess":"ajaxError",[x,u,c?b:y]),g.fireWith(d,[x,k]),l&&(p.trigger("ajaxComplete",[x,u]),--m.active||m.event.trigger("ajaxStop")))}return x},getJSON:function(t,e,i){return m.get(t,e,i,"json")},getScript:function(t,e){return m.get(t,void 0,e,"script")}}),m.each(["get","post"],function(t,e){m[e]=function(t,i,n,s){return m.isFunction(i)&&(s=s||n,n=i,i=void 0),m.ajax(m.extend({url:t,type:e,dataType:s,data:i,success:n},m.isPlainObject(t)&&t))}}),m._evalUrl=function(t){return m.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},m.fn.extend({wrapAll:function(t){if(m.isFunction(t))return this.each(function(e){m(this).wrapAll(t.call(this,e))});if(this[0]){var e=m(t,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstChild&&1===t.firstChild.nodeType;)t=t.firstChild;return t}).append(this)}return this},wrapInner:function(t){return m.isFunction(t)?this.each(function(e){m(this).wrapInner(t.call(this,e))}):this.each(function(){var e=m(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)})},wrap:function(t){var e=m.isFunction(t);return this.each(function(i){m(this).wrapAll(e?t.call(this,i):t)})},unwrap:function(){return this.parent().each(function(){m.nodeName(this,"body")||m(this).replaceWith(this.childNodes)}).end()}}),m.expr.filters.hidden=function(t){return f.reliableHiddenOffsets()?t.offsetWidth<=0&&t.offsetHeight<=0&&!t.getClientRects().length:function(t){if(!m.contains(t.ownerDocument||r,t))return!0;for(;t&&1===t.nodeType;){if("none"===((e=t).style&&e.style.display||m.css(e,"display"))||"hidden"===t.type)return!0;t=t.parentNode}var e;return!1}(t)},m.expr.filters.visible=function(t){return!m.expr.filters.hidden(t)};var Ze=/%20/g,ti=/\[\]$/,ei=/\r?\n/g,ii=/^(?:submit|button|image|reset|file)$/i,ni=/^(?:input|select|textarea|keygen)/i;function si(t,e,i,n){var s;if(m.isArray(e))m.each(e,function(e,s){i||ti.test(t)?n(t,s):si(t+"["+("object"==typeof s&&null!=s?e:"")+"]",s,i,n)});else if(i||"object"!==m.type(e))n(t,e);else for(s in e)si(t+"["+s+"]",e[s],i,n)}m.param=function(t,e){var i,n=[],s=function(t,e){e=m.isFunction(e)?e():null==e?"":e,n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)};if(void 0===e&&(e=m.ajaxSettings&&m.ajaxSettings.traditional),m.isArray(t)||t.jquery&&!m.isPlainObject(t))m.each(t,function(){s(this.name,this.value)});else for(i in t)si(i,t[i],e,s);return n.join("&").replace(Ze,"+")},m.fn.extend({serialize:function(){return m.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=m.prop(this,"elements");return t?m.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!m(this).is(":disabled")&&ni.test(this.nodeName)&&!ii.test(t)&&(this.checked||!et.test(t))}).map(function(t,e){var i=m(this).val();return null==i?null:m.isArray(i)?m.map(i,function(t){return{name:e.name,value:t.replace(ei,"\r\n")}}):{name:e.name,value:i.replace(ei,"\r\n")}}).get()}}),m.ajaxSettings.xhr=void 0!==i.ActiveXObject?function(){return this.isLocal?hi():r.documentMode>8?li():/^(get|post|head|put|delete|options)$/i.test(this.type)&&li()||hi()}:li;var oi=0,ri={},ai=m.ajaxSettings.xhr();function li(){try{return new i.XMLHttpRequest}catch(t){}}function hi(){try{return new i.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}i.attachEvent&&i.attachEvent("onunload",function(){for(var t in ri)ri[t](void 0,!0)}),f.cors=!!ai&&"withCredentials"in ai,(ai=f.ajax=!!ai)&&m.ajaxTransport(function(t){var e;if(!t.crossDomain||f.cors)return{send:function(n,s){var o,r=t.xhr(),a=++oi;if(r.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)r[o]=t.xhrFields[o];for(o in t.mimeType&&r.overrideMimeType&&r.overrideMimeType(t.mimeType),t.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest"),n)void 0!==n[o]&&r.setRequestHeader(o,n[o]+"");r.send(t.hasContent&&t.data||null),e=function(i,n){var o,l,h;if(e&&(n||4===r.readyState))if(delete ri[a],e=void 0,r.onreadystatechange=m.noop,n)4!==r.readyState&&r.abort();else{h={},o=r.status,"string"==typeof r.responseText&&(h.text=r.responseText);try{l=r.statusText}catch(t){l=""}o||!t.isLocal||t.crossDomain?1223===o&&(o=204):o=h.text?200:404}h&&s(o,l,h,r.getAllResponseHeaders())},t.async?4===r.readyState?i.setTimeout(e):r.onreadystatechange=ri[a]=e:e()},abort:function(){e&&e(void 0,!0)}}}),m.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return m.globalEval(t),t}}}),m.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET",t.global=!1)}),m.ajaxTransport("script",function(t){if(t.crossDomain){var e,i=r.head||m("head")[0]||r.documentElement;return{send:function(n,s){(e=r.createElement("script")).async=!0,t.scriptCharset&&(e.charset=t.scriptCharset),e.src=t.url,e.onload=e.onreadystatechange=function(t,i){(i||!e.readyState||/loaded|complete/.test(e.readyState))&&(e.onload=e.onreadystatechange=null,e.parentNode&&e.parentNode.removeChild(e),e=null,i||s(200,"success"))},i.insertBefore(e,i.firstChild)},abort:function(){e&&e.onload(void 0,!0)}}}});var ci=[],ui=/(=)\?(?=&|$)|\?\?/;m.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=ci.pop()||m.expando+"_"+ze++;return this[t]=!0,t}}),m.ajaxPrefilter("json jsonp",function(t,e,n){var s,o,r,a=!1!==t.jsonp&&(ui.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&ui.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return s=t.jsonpCallback=m.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(ui,"$1"+s):!1!==t.jsonp&&(t.url+=(We.test(t.url)?"&":"?")+t.jsonp+"="+s),t.converters["script json"]=function(){return r||m.error(s+" was not called"),r[0]},t.dataTypes[0]="json",o=i[s],i[s]=function(){r=arguments},n.always(function(){void 0===o?m(i).removeProp(s):i[s]=o,t[s]&&(t.jsonpCallback=e.jsonpCallback,ci.push(s)),r&&m.isFunction(o)&&o(r[0]),r=o=void 0}),"script"}),m.parseHTML=function(t,e,i){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(i=e,e=!1),e=e||r;var n=D.exec(t),s=!i&&[];return n?[e.createElement(n[1])]:(n=pt([t],e,s),s&&s.length&&m(s).remove(),m.merge([],n.childNodes))};var di=m.fn.load;function pi(t){return m.isWindow(t)?t:9===t.nodeType&&(t.defaultView||t.parentWindow)}m.fn.load=function(t,e,i){if("string"!=typeof t&&di)return di.apply(this,arguments);var n,s,o,r=this,a=t.indexOf(" ");return a>-1&&(n=m.trim(t.slice(a,t.length)),t=t.slice(0,a)),m.isFunction(e)?(i=e,e=void 0):e&&"object"==typeof e&&(s="POST"),r.length>0&&m.ajax({url:t,type:s||"GET",dataType:"html",data:e}).done(function(t){o=arguments,r.html(n?m("<div>").append(m.parseHTML(t)).find(n):t)}).always(i&&function(t,e){r.each(function(){i.apply(this,o||[t.responseText,e,t])})}),this},m.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){m.fn[e]=function(t){return this.on(e,t)}}),m.expr.filters.animated=function(t){return m.grep(m.timers,function(e){return t===e.elem}).length},m.offset={setOffset:function(t,e,i){var n,s,o,r,a,l,h=m.css(t,"position"),c=m(t),u={};"static"===h&&(t.style.position="relative"),a=c.offset(),o=m.css(t,"top"),l=m.css(t,"left"),("absolute"===h||"fixed"===h)&&m.inArray("auto",[o,l])>-1?(r=(n=c.position()).top,s=n.left):(r=parseFloat(o)||0,s=parseFloat(l)||0),m.isFunction(e)&&(e=e.call(t,i,m.extend({},a))),null!=e.top&&(u.top=e.top-a.top+r),null!=e.left&&(u.left=e.left-a.left+s),"using"in e?e.using.call(t,u):c.css(u)}},m.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){m.offset.setOffset(this,t,e)});var e,i,n={top:0,left:0},s=this[0],o=s&&s.ownerDocument;return o?(e=o.documentElement,m.contains(e,s)?(void 0!==s.getBoundingClientRect&&(n=s.getBoundingClientRect()),i=pi(o),{top:n.top+(i.pageYOffset||e.scrollTop)-(e.clientTop||0),left:n.left+(i.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):n):void 0},position:function(){if(this[0]){var t,e,i={top:0,left:0},n=this[0];return"fixed"===m.css(n,"position")?e=n.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),m.nodeName(t[0],"html")||(i=t.offset()),i.top+=m.css(t[0],"borderTopWidth",!0),i.left+=m.css(t[0],"borderLeftWidth",!0)),{top:e.top-i.top-m.css(n,"marginTop",!0),left:e.left-i.left-m.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&!m.nodeName(t,"html")&&"static"===m.css(t,"position");)t=t.offsetParent;return t||Yt})}}),m.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var i=/Y/.test(e);m.fn[t]=function(n){return tt(this,function(t,n,s){var o=pi(t);if(void 0===s)return o?e in o?o[e]:o.document.documentElement[n]:t[n];o?o.scrollTo(i?m(o).scrollLeft():s,i?s:m(o).scrollTop()):t[n]=s},t,n,arguments.length,null)}}),m.each(["top","left"],function(t,e){m.cssHooks[e]=Xt(f.pixelPosition,function(t,i){if(i)return i=Kt(t,e),qt.test(i)?m(t).position()[e]+"px":i})}),m.each({Height:"height",Width:"width"},function(t,e){m.each({padding:"inner"+t,content:e,"":"outer"+t},function(i,n){m.fn[n]=function(n,s){var o=arguments.length&&(i||"boolean"!=typeof n),r=i||(!0===n||!0===s?"margin":"border");return tt(this,function(e,i,n){var s;return m.isWindow(e)?e.document.documentElement["client"+t]:9===e.nodeType?(s=e.documentElement,Math.max(e.body["scroll"+t],s["scroll"+t],e.body["offset"+t],s["offset"+t],s["client"+t])):void 0===n?m.css(e,i,r):m.style(e,i,n,r)},e,o?n:void 0,o,null)}})}),m.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)}}),m.fn.size=function(){return this.length},m.fn.andSelf=m.fn.addBack,void 0===(n=function(){return m}.apply(e,[]))||(t.exports=n);var fi=i.jQuery,mi=i.$;return m.noConflict=function(t){return i.$===m&&(i.$=mi),t&&i.jQuery===m&&(i.jQuery=fi),m},s||(i.jQuery=i.$=m),m},"object"==typeof t&&"object"==typeof t.exports?t.exports=s.document?o(s,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return o(t)}:o(s)},function(t,e,i){var n;!function(){var s,o,r,a={frameRate:150,animationTime:400,stepSize:100,pulseAlgorithm:!0,pulseScale:4,pulseNormalize:1,accelerationDelta:50,accelerationMax:3,keyboardSupport:!0,arrowScroll:50,touchpadSupport:!1,fixedBackground:!0,excluded:""},l=a,h=!1,c=!1,u={x:0,y:0},d=!1,p=document.documentElement,f=[],m=/^Mac/.test(navigator.platform),g={left:37,up:38,right:39,down:40,spacebar:32,pageup:33,pagedown:34,end:35,home:36},v={37:1,38:1,39:1,40:1};function b(){if(!d&&document.body){d=!0;var t=document.body,e=document.documentElement,i=window.innerHeight,n=t.scrollHeight;if(p=document.compatMode.indexOf("CSS")>=0?e:t,s=t,l.keyboardSupport&&W("keydown",k),top!=self)c=!0;else if(n>i&&(t.offsetHeight<=i||e.offsetHeight<=i)){var a,u=document.createElement("div");if(u.style.cssText="position:absolute; z-index:-10000; top:0; left:0; right:0; height:"+p.scrollHeight+"px",document.body.appendChild(u),r=function(){a||(a=setTimeout(function(){h||(u.style.height="0",u.style.height=p.scrollHeight+"px",a=null)},500))},setTimeout(r,10),W("resize",r),(o=new B(r)).observe(t,{attributes:!0,childList:!0,characterData:!1}),p.offsetHeight<=i){var f=document.createElement("div");f.style.clear="both",t.appendChild(f)}}l.fixedBackground||h||(t.style.backgroundAttachment="scroll",e.style.backgroundAttachment="scroll")}}var y=[],_=!1,w=Date.now();function x(t,e,i){var n,s;if(n=(n=e)>0?1:-1,s=(s=i)>0?1:-1,(u.x!==n||u.y!==s)&&(u.x=n,u.y=s,y=[],w=0),1!=l.accelerationMax){var o=Date.now()-w;if(o<l.accelerationDelta){var r=(1+50/o)/2;r>1&&(r=Math.min(r,l.accelerationMax),e*=r,i*=r)}w=Date.now()}if(y.push({x:e,y:i,lastX:e<0?.99:-.99,lastY:i<0?.99:-.99,start:Date.now()}),!_){var a=t===document.body,h=function(n){for(var s=Date.now(),o=0,r=0,c=0;c<y.length;c++){var u=y[c],d=s-u.start,p=d>=l.animationTime,f=p?1:d/l.animationTime;l.pulseAlgorithm&&(f=(v=f)>=1?1:v<=0?0:(1==l.pulseNormalize&&(l.pulseNormalize/=U(1)),U(v)));var m=u.x*f-u.lastX>>0,g=u.y*f-u.lastY>>0;o+=m,r+=g,u.lastX+=m,u.lastY+=g,p&&(y.splice(c,1),c--)}var v;a?window.scrollBy(o,r):(o&&(t.scrollLeft+=o),r&&(t.scrollTop+=r)),e||i||(y=[]),y.length?q(h,t,1e3/l.frameRate+1):_=!1};q(h,t,0),_=!0}}function C(t){d||b();var e=t.target,i=P(e);if(!i||t.defaultPrevented||t.ctrlKey)return!0;if($(s,"embed")||$(e,"embed")&&/\.pdf/i.test(e.src)||$(s,"object")||e.shadowRoot)return!0;var n=-t.wheelDeltaX||t.deltaX||0,o=-t.wheelDeltaY||t.deltaY||0;if(m&&(t.wheelDeltaX&&F(t.wheelDeltaX,120)&&(n=t.wheelDeltaX/Math.abs(t.wheelDeltaX)*-120),t.wheelDeltaY&&F(t.wheelDeltaY,120)&&(o=t.wheelDeltaY/Math.abs(t.wheelDeltaY)*-120)),n||o||(o=-t.wheelDelta||0),1===t.deltaMode&&(n*=40,o*=40),!l.touchpadSupport&&function(t){if(t)return f.length||(f=[t,t,t]),t=Math.abs(t),f.push(t),f.shift(),clearTimeout(E),E=setTimeout(function(){window.localStorage&&(localStorage.SS_deltaBuffer=f.join(","))},1e3),!R(120)&&!R(100)}(o))return!0;Math.abs(n)>1.2&&(n*=l.stepSize/120),Math.abs(o)>1.2&&(o*=l.stepSize/120),x(i,n,o),t.preventDefault(),A()}function k(t){var e=t.target,i=t.ctrlKey||t.altKey||t.metaKey||t.shiftKey&&t.keyCode!==g.spacebar;document.body.contains(s)||(s=document.activeElement);var n=/^(button|submit|radio|checkbox|file|color|image)$/i;if(t.defaultPrevented||/^(textarea|select|embed|object)$/i.test(e.nodeName)||$(e,"input")&&!n.test(e.type)||$(s,"video")||function(e){var i=t.target,n=!1;if(-1!=document.URL.indexOf("www.youtube.com/watch"))do{if(n=i.classList&&i.classList.contains("html5-video-controls"))break}while(i=i.parentNode);return n}()||e.isContentEditable||i)return!0;if(($(e,"button")||$(e,"input")&&n.test(e.type))&&t.keyCode===g.spacebar)return!0;if($(e,"input")&&"radio"==e.type&&v[t.keyCode])return!0;var o=0,r=0,a=P(s),h=a.clientHeight;switch(a==document.body&&(h=window.innerHeight),t.keyCode){case g.up:r=-l.arrowScroll;break;case g.down:r=l.arrowScroll;break;case g.spacebar:r=-(t.shiftKey?1:-1)*h*.9;break;case g.pageup:r=.9*-h;break;case g.pagedown:r=.9*h;break;case g.home:r=-a.scrollTop;break;case g.end:var c=a.scrollHeight-a.scrollTop-h;r=c>0?c+10:0;break;case g.left:o=-l.arrowScroll;break;case g.right:o=l.arrowScroll;break;default:return!0}x(a,o,r),t.preventDefault(),A()}function D(t){s=t.target}var T,S,E,I=(T=0,function(t){return t.uniqueID||(t.uniqueID=T++)}),N={};function A(){clearTimeout(S),S=setInterval(function(){N={}},1e3)}function M(t,e){for(var i=t.length;i--;)N[I(t[i])]=e;return e}function P(t){var e=[],i=document.body,n=p.scrollHeight;do{var s=N[I(t)];if(s)return M(e,s);if(e.push(t),n===t.scrollHeight){var o=O(p)&&O(i)||z(p);if(c&&H(p)||!c&&o)return M(e,Y())}else if(H(t)&&z(t))return M(e,t)}while(t=t.parentElement)}function H(t){return t.clientHeight+10<t.scrollHeight}function O(t){return"hidden"!==getComputedStyle(t,"").getPropertyValue("overflow-y")}function z(t){var e=getComputedStyle(t,"").getPropertyValue("overflow-y");return"scroll"===e||"auto"===e}function W(t,e){window.addEventListener(t,e,!1)}function L(t,e){window.removeEventListener(t,e,!1)}function $(t,e){return(t.nodeName||"").toLowerCase()===e.toLowerCase()}function F(t,e){return Math.floor(t/e)==t/e}function R(t){return F(f[0],t)&&F(f[1],t)&&F(f[2],t)}window.localStorage&&localStorage.SS_deltaBuffer&&(f=localStorage.SS_deltaBuffer.split(","));var j,q=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t,e,i){window.setTimeout(t,i||1e3/60)},B=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,Y=function(){if(!j){var t=document.createElement("div");t.style.cssText="height:10000px;width:1px;",document.body.appendChild(t);var e=document.body.scrollTop;document.documentElement.scrollTop,window.scrollBy(0,3),j=document.body.scrollTop!=e?document.body:document.documentElement,window.scrollBy(0,-3),document.body.removeChild(t)}return j};function U(t){var e,i;return(t*=l.pulseScale)<1?e=t-(1-Math.exp(-t)):(t-=1,e=(i=Math.exp(-1))+(1-Math.exp(-t))*(1-i)),e*l.pulseNormalize}var K,V=window.navigator.userAgent,X=/Edge/.test(V),G=/chrome/i.test(V)&&!X,Q=/safari/i.test(V)&&!X,J=/mobile/i.test(V),Z=/Windows NT 6.1/i.test(V)&&/rv:11/i.test(V),tt=(G||Q||Z)&&!J;function et(t){for(var e in t)a.hasOwnProperty(e)&&(l[e]=t[e])}"onwheel"in document.createElement("div")?K="wheel":"onmousewheel"in document.createElement("div")&&(K="mousewheel"),K&&tt&&(W(K,C),W("mousedown",D),W("load",b)),et.destroy=function(){o&&o.disconnect(),L(K,C),L("mousedown",D),L("keydown",k),L("resize",r),L("load",b)},window.SmoothScrollOptions&&et(window.SmoothScrollOptions),void 0===(n=function(){return et}.call(e,i,e,t))||(t.exports=n)}()},function(t,e){t.exports=function(){throw new Error("define cannot be used indirect")}},function(t,e,i){var n,s,o;o=jQuery,jQuery.fn.menuzord=function(t){var e;o.extend(e={showSpeed:300,hideSpeed:300,trigger:"hover",showDelay:0,hideDelay:0,effect:"fade",align:"left",responsive:!0,animation:"none",indentChildren:!0,indicatorFirstLevel:"",indicatorSecondLevel:"",scrollable:!0,scrollableMaxHeight:400},t);var i,n=o(this),s=o(n).children(".menuzord-menu"),r=o(s).find("li"),a=991,l=2e3,h=200;function c(t){"fade"==e.effect?o(t).children(".dropdown, .megamenu").stop(!0,!0).delay(e.showDelay).fadeIn(e.showSpeed).addClass(e.animation):o(t).children(".dropdown, .megamenu").stop(!0,!0).delay(e.showDelay).slideDown(e.showSpeed).addClass(e.animation)}function u(t){"fade"==e.effect?o(t).children(".dropdown, .megamenu").stop(!0,!0).delay(e.hideDelay).fadeOut(e.hideSpeed).removeClass(e.animation):o(t).children(".dropdown, .megamenu").stop(!0,!0).delay(e.hideDelay).slideUp(e.hideSpeed).removeClass(e.animation),o(t).children(".dropdown, .megamenu").find(".dropdown, .megamenu").stop(!0,!0).delay(e.hideDelay).fadeOut(e.hideSpeed)}function d(){o(s).find(".dropdown, .megamenu").hide(0),navigator.userAgent.match(/Mobi/i)||window.navigator.msMaxTouchPoints>0||"click"==e.trigger?(o(".menuzord-menu > li > a, .menuzord-menu ul.dropdown li a").bind("click touchstart",function(t){if(t.stopPropagation(),t.preventDefault(),o(this).parent("li").siblings("li").find(".dropdown, .megamenu").stop(!0,!0).fadeOut(300),"none"==o(this).siblings(".dropdown, .megamenu").css("display"))return c(o(this).parent("li")),!1;u(o(this).parent("li")),window.location.href=o(this).attr("href")}),o(document).bind("click.menu touchstart.menu",function(t){0==o(t.target).closest(".menuzord").length&&o(".menuzord-menu").find(".dropdown, .megamenu").fadeOut(300)})):o(r).bind("mouseenter",function(){c(this)}).bind("mouseleave",function(){u(this)})}function p(){var t=o(s).children("li").children(".dropdown, .megamenu");if(o(window).innerWidth()>a)for(var e=o(n).outerWidth(!0),i=0;i<t.length;i++)o(t[i]).parent("li").position().left+o(t[i]).outerWidth()>e?o(t[i]).css("right",0):((e==o(t[i]).outerWidth()||e-o(t[i]).outerWidth()<20)&&o(t[i]).css("left",0),o(t[i]).parent("li").position().left+o(t[i]).outerWidth()<e&&o(t[i]).css("right","auto"))}function f(){o(n).find("li, a").unbind(),o(document).unbind("click.menu touchstart.menu")}function m(){return window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth}function g(){p(),m()<=a&&l>a&&(f(),e.responsive?(o(s).hide(0),o(i).show(0).click(function(){"none"==o(s).css("display")?o(s).slideDown(e.showSpeed):o(s).slideUp(e.hideSpeed).find(".dropdown, .megamenu").hide(e.hideSpeed)}),o(s).find(".dropdown, .megamenu").hide(0),o(s).find(".indicator").each(function(){o(this).parent("a").siblings(".dropdown, .megamenu").length>0&&o(this).bind("click",function(t){o(s).scrollTo({top:45,left:0},600),"A"==o(this).parent().prop("tagName")&&t.preventDefault(),"none"==o(this).parent("a").siblings(".dropdown, .megamenu").css("display")?(o(this).parent("a").siblings(".dropdown, .megamenu").delay(e.showDelay).slideDown(e.showSpeed),o(this).parent("a").parent("li").siblings("li").find(".dropdown, .megamenu").slideUp(e.hideSpeed)):o(this).parent("a").siblings(".dropdown, .megamenu").slideUp(e.hideSpeed)})})):d()),m()>a&&h<=a&&(f(),o(s).show(0),o(i).hide(0),d()),l=m(),h=m(),function(){function t(t){var e=o(t).find(".menuzord-tabs-nav > li"),i=o(t).find(".menuzord-tabs-content");o(e).bind("click touchstart",function(t){t.stopPropagation(),t.preventDefault(),o(e).removeClass("active"),o(this).addClass("active"),o(i).hide(0),o(i[o(this).index()]).show(0)})}if(o(s).find(".menuzord-tabs").length>0)for(var e=o(s).find(".menuzord-tabs"),i=0;i<e.length;i++)t(e[i])}(),/MSIE (\d+\.\d+);/.test(navigator.userAgent)&&m()<a&&8==new Number(RegExp.$1)&&(o(i).hide(0),o(s).show(0),f(),d())}o(s).children("li").children("a").each(function(){o(this).siblings(".dropdown, .megamenu").length>0&&o(this).append("<span class='indicator'>"+e.indicatorFirstLevel+"</span>")}),o(s).find(".dropdown").children("li").children("a").each(function(){o(this).siblings(".dropdown").length>0&&o(this).append("<span class='indicator'>"+e.indicatorSecondLevel+"</span>")}),"right"==e.align&&o(s).addClass("menuzord-right"),e.indentChildren&&o(s).addClass("menuzord-indented"),e.responsive&&(o(n).addClass("menuzord-responsive").prepend("<a href='javascript:void(0)' class='showhide'><span></span><span></span><span></span><span></span></a>"),i=o(n).children(".showhide")),e.scrollable&&e.responsive&&o(s).css("max-height",e.scrollableMaxHeight).addClass("scrollable").append("<li class='scrollable-fix'></li>"),g(),o(window).resize(function(){g(),p()})},function(o){"use strict";n=[i(0)],void 0===(s=function(t){var e=t.scrollTo=function(e,i,n){return t(window).scrollTo(e,i,n)};function i(e){return t.isFunction(e)||"object"==typeof e?e:{top:e,left:e}}return e.defaults={axis:"xy",duration:parseFloat(t.fn.jquery)>=1.3?0:1,limit:!0},e.window=function(e){return t(window)._scrollable()},t.fn._scrollable=function(){return this.map(function(){var e=this;if(e.nodeName&&-1==t.inArray(e.nodeName.toLowerCase(),["iframe","#document","html","body"]))return e;var i=(e.contentWindow||e).document||e.ownerDocument||e;return/webkit/i.test(navigator.userAgent)||"BackCompat"==i.compatMode?i.body:i.documentElement})},t.fn.scrollTo=function(n,s,o){return"object"==typeof s&&(o=s,s=0),"function"==typeof o&&(o={onAfter:o}),"max"==n&&(n=9e9),o=t.extend({},e.defaults,o),s=s||o.duration,o.queue=o.queue&&o.axis.length>1,o.queue&&(s/=2),o.offset=i(o.offset),o.over=i(o.over),this._scrollable().each(function(){if(null!=n){var r,a=this,l=t(a),h=n,c={},u=l.is("html,body");switch(typeof h){case"number":case"string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(h)){h=i(h);break}if(!(h=u?t(h):t(h,this)).length)return;case"object":(h.is||h.style)&&(r=(h=t(h)).offset())}var d=t.isFunction(o.offset)&&o.offset(a,h)||o.offset;t.each(o.axis.split(""),function(t,i){var n="x"==i?"Left":"Top",s=n.toLowerCase(),f="scroll"+n,m=a[f],g=e.max(a,i);if(r)c[f]=r[s]+(u?0:m-l.offset()[s]),o.margin&&(c[f]-=parseInt(h.css("margin"+n))||0,c[f]-=parseInt(h.css("border"+n+"Width"))||0),c[f]+=d[s]||0,o.over[s]&&(c[f]+=h["x"==i?"width":"height"]()*o.over[s]);else{var v=h[s];c[f]=v.slice&&"%"==v.slice(-1)?parseFloat(v)/100*g:v}o.limit&&/^\d+$/.test(c[f])&&(c[f]=c[f]<=0?0:Math.min(c[f],g)),!t&&o.queue&&(m!=c[f]&&p(o.onAfterFirst),delete c[f])}),p(o.onAfter)}function p(t){l.animate(c,s,o.easing,t&&function(){t.call(this,h,o)})}}).end()},e.max=function(e,i){var n="x"==i?"Width":"Height",s="scroll"+n;if(!t(e).is("html,body"))return e[s]-t(e)[n.toLowerCase()]();var o="client"+n,r=e.ownerDocument.documentElement,a=e.ownerDocument.body;return Math.max(r[s],a[s])-Math.min(r[o],a[o])},e}.apply(e,n))||(t.exports=s)}(i(2))},function(t,e,i){"use strict";$(".wow").length&&new wows.WOW({boxClass:"wow",animateClass:"animated",offset:0,mobile:!1,live:!0}).init(),jQuery(document).ready(function(){jQuery,$(".preloader").length&&$(".preloader").delay(200).fadeOut(500),$(".scroll-top").length&&($(window).scroll(function(){$(this).scrollTop()>200?$(".scroll-top").fadeIn():$(".scroll-top").fadeOut()}),$(".scroll-top").click(function(){return $("html, body").animate({scrollTop:0},1500),!1})),$("#main_menu").length&&$("#main_menu").menuzord({animation:"zoom-out"})}),document.addEventListener("DOMContentLoaded",function(){document.querySelector("#words_count").addEventListener("keypress",function(t){/[0-9]/.test(t.key)||t.preventDefault()}),document.querySelector("#qgenerate").addEventListener("click",function(t){var e=document.querySelector("#service_type").value,i=document.querySelector("#words_count").value;""!=e&&""!=i?$("#qgenmodal").modal("show"):document.querySelector("#service_err").innerHTML="* Service type and words count are required"})}),jQuery(window).scroll(function(){jQuery,function(){$(".stricky").length&&($(window).scrollTop()>100?($(".stricky").removeClass("fadeIn animated"),$(".stricky").addClass("stricky-fixed fadeInDown animated"),$(".scroll-to-top").fadeIn(500)):$(this).scrollTop()<=100&&($(".stricky").removeClass("stricky-fixed fadeInDown animated"),$(".stricky").addClass("slideIn animated"),$(".scroll-to-top").fadeOut(500)))}()}),$("#req_q").on("click",function(t){if(""!==this.hash){t.preventDefault();var e=this.hash;$("html, body").animate({scrollTop:$(e).offset().top},1500,function(){window.location.hash=e})}})},function(t,e){(function(){var t,e,i,n,s,o=function(t,e){return function(){return t.apply(e,arguments)}},r=[].indexOf||function(t){for(var e=0,i=this.length;e<i;e++)if(e in this&&this[e]===t)return e;return-1};e=function(){function t(){}return t.prototype.extend=function(t,e){var i,n;for(i in e)n=e[i],null==t[i]&&(t[i]=n);return t},t.prototype.isMobile=function(t){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)},t.prototype.createEvent=function(t,e,i,n){var s;return null==e&&(e=!1),null==i&&(i=!1),null==n&&(n=null),null!=document.createEvent?(s=document.createEvent("CustomEvent")).initCustomEvent(t,e,i,n):null!=document.createEventObject?(s=document.createEventObject()).eventType=t:s.eventName=t,s},t.prototype.emitEvent=function(t,e){return null!=t.dispatchEvent?t.dispatchEvent(e):e in(null!=t)?t[e]():"on"+e in(null!=t)?t["on"+e]():void 0},t.prototype.addEvent=function(t,e,i){return null!=t.addEventListener?t.addEventListener(e,i,!1):null!=t.attachEvent?t.attachEvent("on"+e,i):t[e]=i},t.prototype.removeEvent=function(t,e,i){return null!=t.removeEventListener?t.removeEventListener(e,i,!1):null!=t.detachEvent?t.detachEvent("on"+e,i):delete t[e]},t.prototype.innerHeight=function(){return"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight},t}(),i=this.WeakMap||this.MozWeakMap||(i=function(){function t(){this.keys=[],this.values=[]}return t.prototype.get=function(t){var e,i,n,s;for(e=i=0,n=(s=this.keys).length;i<n;e=++i)if(s[e]===t)return this.values[e]},t.prototype.set=function(t,e){var i,n,s,o;for(i=n=0,s=(o=this.keys).length;n<s;i=++n)if(o[i]===t)return void(this.values[i]=e);return this.keys.push(t),this.values.push(e)},t}()),t=this.MutationObserver||this.WebkitMutationObserver||this.MozMutationObserver||(t=function(){function t(){"undefined"!=typeof console&&null!==console&&console.warn("MutationObserver is not supported by your browser."),"undefined"!=typeof console&&null!==console&&console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content.")}return t.notSupported=!0,t.prototype.observe=function(){},t}()),n=this.getComputedStyle||function(t,e){return this.getPropertyValue=function(e){var i;return"float"===e&&(e="styleFloat"),s.test(e)&&e.replace(s,function(t,e){return e.toUpperCase()}),(null!=(i=t.currentStyle)?i[e]:void 0)||null},this},s=/(\-([a-z]){1})/g,this.WOW=function(){function s(t){null==t&&(t={}),this.scrollCallback=o(this.scrollCallback,this),this.scrollHandler=o(this.scrollHandler,this),this.resetAnimation=o(this.resetAnimation,this),this.start=o(this.start,this),this.scrolled=!0,this.config=this.util().extend(t,this.defaults),null!=t.scrollContainer&&(this.config.scrollContainer=document.querySelector(t.scrollContainer)),this.animationNameCache=new i,this.wowEvent=this.util().createEvent(this.config.boxClass)}return s.prototype.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null},s.prototype.init=function(){var t;return this.element=window.document.documentElement,"interactive"===(t=document.readyState)||"complete"===t?this.start():this.util().addEvent(document,"DOMContentLoaded",this.start),this.finished=[]},s.prototype.start=function(){var e,i,n,s,o;if(this.stopped=!1,this.boxes=function(){var t,i,n,s;for(s=[],t=0,i=(n=this.element.querySelectorAll("."+this.config.boxClass)).length;t<i;t++)e=n[t],s.push(e);return s}.call(this),this.all=function(){var t,i,n,s;for(s=[],t=0,i=(n=this.boxes).length;t<i;t++)e=n[t],s.push(e);return s}.call(this),this.boxes.length)if(this.disabled())this.resetStyle();else for(i=0,n=(s=this.boxes).length;i<n;i++)e=s[i],this.applyStyle(e,!0);if(this.disabled()||(this.util().addEvent(this.config.scrollContainer||window,"scroll",this.scrollHandler),this.util().addEvent(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live)return new t((o=this,function(t){var e,i,n,s,r;for(r=[],e=0,i=t.length;e<i;e++)s=t[e],r.push(function(){var t,e,i,o;for(o=[],t=0,e=(i=s.addedNodes||[]).length;t<e;t++)n=i[t],o.push(this.doSync(n));return o}.call(o));return r})).observe(document.body,{childList:!0,subtree:!0})},s.prototype.stop=function(){if(this.stopped=!0,this.util().removeEvent(this.config.scrollContainer||window,"scroll",this.scrollHandler),this.util().removeEvent(window,"resize",this.scrollHandler),null!=this.interval)return clearInterval(this.interval)},s.prototype.sync=function(e){if(t.notSupported)return this.doSync(this.element)},s.prototype.doSync=function(t){var e,i,n,s,o;if(null==t&&(t=this.element),1===t.nodeType){for(o=[],i=0,n=(s=(t=t.parentNode||t).querySelectorAll("."+this.config.boxClass)).length;i<n;i++)e=s[i],r.call(this.all,e)<0?(this.boxes.push(e),this.all.push(e),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(e,!0),o.push(this.scrolled=!0)):o.push(void 0);return o}},s.prototype.show=function(t){return this.applyStyle(t),t.className=t.className+" "+this.config.animateClass,null!=this.config.callback&&this.config.callback(t),this.util().emitEvent(t,this.wowEvent),this.util().addEvent(t,"animationend",this.resetAnimation),this.util().addEvent(t,"oanimationend",this.resetAnimation),this.util().addEvent(t,"webkitAnimationEnd",this.resetAnimation),this.util().addEvent(t,"MSAnimationEnd",this.resetAnimation),t},s.prototype.applyStyle=function(t,e){var i,n,s,o;return n=t.getAttribute("data-wow-duration"),i=t.getAttribute("data-wow-delay"),s=t.getAttribute("data-wow-iteration"),this.animate((o=this,function(){return o.customStyle(t,e,n,i,s)}))},s.prototype.animate="requestAnimationFrame"in window?function(t){return window.requestAnimationFrame(t)}:function(t){return t()},s.prototype.resetStyle=function(){var t,e,i,n,s;for(s=[],e=0,i=(n=this.boxes).length;e<i;e++)t=n[e],s.push(t.style.visibility="visible");return s},s.prototype.resetAnimation=function(t){var e;if(t.type.toLowerCase().indexOf("animationend")>=0)return(e=t.target||t.srcElement).className=e.className.replace(this.config.animateClass,"").trim()},s.prototype.customStyle=function(t,e,i,n,s){return e&&this.cacheAnimationName(t),t.style.visibility=e?"hidden":"visible",i&&this.vendorSet(t.style,{animationDuration:i}),n&&this.vendorSet(t.style,{animationDelay:n}),s&&this.vendorSet(t.style,{animationIterationCount:s}),this.vendorSet(t.style,{animationName:e?"none":this.cachedAnimationName(t)}),t},s.prototype.vendors=["moz","webkit"],s.prototype.vendorSet=function(t,e){var i,n,s,o;for(i in n=[],e)s=e[i],t[""+i]=s,n.push(function(){var e,n,r,a;for(a=[],e=0,n=(r=this.vendors).length;e<n;e++)o=r[e],a.push(t[""+o+i.charAt(0).toUpperCase()+i.substr(1)]=s);return a}.call(this));return n},s.prototype.vendorCSS=function(t,e){var i,s,o,r,a,l;for(r=(a=n(t)).getPropertyCSSValue(e),i=0,s=(o=this.vendors).length;i<s;i++)l=o[i],r=r||a.getPropertyCSSValue("-"+l+"-"+e);return r},s.prototype.animationName=function(t){var e;try{e=this.vendorCSS(t,"animation-name").cssText}catch(i){e=n(t).getPropertyValue("animation-name")}return"none"===e?"":e},s.prototype.cacheAnimationName=function(t){return this.animationNameCache.set(t,this.animationName(t))},s.prototype.cachedAnimationName=function(t){return this.animationNameCache.get(t)},s.prototype.scrollHandler=function(){return this.scrolled=!0},s.prototype.scrollCallback=function(){var t;if(this.scrolled&&(this.scrolled=!1,this.boxes=function(){var e,i,n,s;for(s=[],e=0,i=(n=this.boxes).length;e<i;e++)(t=n[e])&&(this.isVisible(t)?this.show(t):s.push(t));return s}.call(this),!this.boxes.length&&!this.config.live))return this.stop()},s.prototype.offsetTop=function(t){for(var e;void 0===t.offsetTop;)t=t.parentNode;for(e=t.offsetTop;t=t.offsetParent;)e+=t.offsetTop;return e},s.prototype.isVisible=function(t){var e,i,n,s,o;return i=t.getAttribute("data-wow-offset")||this.config.offset,s=(o=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset)+Math.min(this.element.clientHeight,this.util().innerHeight())-i,e=(n=this.offsetTop(t))+t.clientHeight,n<=s&&e>=o},s.prototype.util=function(){return null!=this._util?this._util:this._util=new e},s.prototype.disabled=function(){return!this.config.mobile&&this.util().isMobile(navigator.userAgent)},s}()}).call(this)},function(t,e){if("undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(t){"use strict";var e=jQuery.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1==e[0]&&9==e[1]&&e[2]<1||e[0]>2)throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 3")}(),function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var i=!1,n=this;return t(this).one("bsTransitionEnd",function(){i=!0}),setTimeout(function(){i||t(n).trigger(t.support.transition.end)},e),this},t(function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){return t(e.target).is(this)?e.handleObj.handler.apply(this,arguments):void 0}})})}(jQuery),function(t){"use strict";var e='[data-dismiss="alert"]',i=function(i){t(i).on("click",e,this.close)};i.VERSION="3.3.6",i.TRANSITION_DURATION=150,i.prototype.close=function(e){function n(){r.detach().trigger("closed.bs.alert").remove()}var s=t(this),o=s.attr("data-target");o||(o=(o=s.attr("href"))&&o.replace(/.*(?=#[^\s]*$)/,""));var r=t(o);e&&e.preventDefault(),r.length||(r=s.closest(".alert")),r.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one("bsTransitionEnd",n).emulateTransitionEnd(i.TRANSITION_DURATION):n())};var n=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var n=t(this),s=n.data("bs.alert");s||n.data("bs.alert",s=new i(this)),"string"==typeof e&&s[e].call(n)})},t.fn.alert.Constructor=i,t.fn.alert.noConflict=function(){return t.fn.alert=n,this},t(document).on("click.bs.alert.data-api",e,i.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.button"),o="object"==typeof e&&e;s||n.data("bs.button",s=new i(this,o)),"toggle"==e?s.toggle():e&&s.setState(e)})}var i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.isLoading=!1};i.VERSION="3.3.6",i.DEFAULTS={loadingText:"loading..."},i.prototype.setState=function(e){var i="disabled",n=this.$element,s=n.is("input")?"val":"html",o=n.data();e+="Text",null==o.resetText&&n.data("resetText",n[s]()),setTimeout(t.proxy(function(){n[s](null==o[e]?this.options[e]:o[e]),"loadingText"==e?(this.isLoading=!0,n.addClass(i).attr(i,i)):this.isLoading&&(this.isLoading=!1,n.removeClass(i).removeAttr(i))},this),0)},i.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var i=this.$element.find("input");"radio"==i.prop("type")?(i.prop("checked")&&(t=!1),e.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==i.prop("type")&&(i.prop("checked")!==this.$element.hasClass("active")&&(t=!1),this.$element.toggleClass("active")),i.prop("checked",this.$element.hasClass("active")),t&&i.trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")};var n=t.fn.button;t.fn.button=e,t.fn.button.Constructor=i,t.fn.button.noConflict=function(){return t.fn.button=n,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(i){var n=t(i.target);n.hasClass("btn")||(n=n.closest(".btn")),e.call(n,"toggle"),t(i.target).is('input[type="radio"]')||t(i.target).is('input[type="checkbox"]')||i.preventDefault()}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){t(e.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(e.type))})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.carousel"),o=t.extend({},i.DEFAULTS,n.data(),"object"==typeof e&&e),r="string"==typeof e?e:o.slide;s||n.data("bs.carousel",s=new i(this,o)),"number"==typeof e?s.to(e):r?s[r]():o.interval&&s.pause().cycle()})}var i=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",t.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};i.VERSION="3.3.6",i.TRANSITION_DURATION=600,i.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},i.prototype.keydown=function(t){if(!/input|textarea/i.test(t.target.tagName)){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()}},i.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},i.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},i.prototype.getItemForDirection=function(t,e){var i=this.getItemIndex(e);if(("prev"==t&&0===i||"next"==t&&i==this.$items.length-1)&&!this.options.wrap)return e;var n=(i+("prev"==t?-1:1))%this.$items.length;return this.$items.eq(n)},i.prototype.to=function(t){var e=this,i=this.getItemIndex(this.$active=this.$element.find(".item.active"));return t>this.$items.length-1||0>t?void 0:this.sliding?this.$element.one("slid.bs.carousel",function(){e.to(t)}):i==t?this.pause().cycle():this.slide(t>i?"next":"prev",this.$items.eq(t))},i.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},i.prototype.next=function(){return this.sliding?void 0:this.slide("next")},i.prototype.prev=function(){return this.sliding?void 0:this.slide("prev")},i.prototype.slide=function(e,n){var s=this.$element.find(".item.active"),o=n||this.getItemForDirection(e,s),r=this.interval,a="next"==e?"left":"right",l=this;if(o.hasClass("active"))return this.sliding=!1;var h=o[0],c=t.Event("slide.bs.carousel",{relatedTarget:h,direction:a});if(this.$element.trigger(c),!c.isDefaultPrevented()){if(this.sliding=!0,r&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var u=t(this.$indicators.children()[this.getItemIndex(o)]);u&&u.addClass("active")}var d=t.Event("slid.bs.carousel",{relatedTarget:h,direction:a});return t.support.transition&&this.$element.hasClass("slide")?(o.addClass(e),o[0].offsetWidth,s.addClass(a),o.addClass(a),s.one("bsTransitionEnd",function(){o.removeClass([e,a].join(" ")).addClass("active"),s.removeClass(["active",a].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(d)},0)}).emulateTransitionEnd(i.TRANSITION_DURATION)):(s.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger(d)),r&&this.cycle(),this}};var n=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=i,t.fn.carousel.noConflict=function(){return t.fn.carousel=n,this};var s=function(i){var n,s=t(this),o=t(s.attr("data-target")||(n=s.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,""));if(o.hasClass("carousel")){var r=t.extend({},o.data(),s.data()),a=s.attr("data-slide-to");a&&(r.interval=!1),e.call(o,r),a&&o.data("bs.carousel").to(a),i.preventDefault()}};t(document).on("click.bs.carousel.data-api","[data-slide]",s).on("click.bs.carousel.data-api","[data-slide-to]",s),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var i=t(this);e.call(i,i.data())})})}(jQuery),function(t){"use strict";function e(e){var i,n=e.attr("data-target")||(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"");return t(n)}function i(e){return this.each(function(){var i=t(this),s=i.data("bs.collapse"),o=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e);!s&&o.toggle&&/show|hide/.test(e)&&(o.toggle=!1),s||i.data("bs.collapse",s=new n(this,o)),"string"==typeof e&&s[e]()})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.$trigger=t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'),this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};n.VERSION="3.3.6",n.TRANSITION_DURATION=350,n.DEFAULTS={toggle:!0},n.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},n.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var e,s=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(s&&s.length&&(e=s.data("bs.collapse"),e&&e.transitioning))){var o=t.Event("show.bs.collapse");if(this.$element.trigger(o),!o.isDefaultPrevented()){s&&s.length&&(i.call(s,"hide"),e||s.data("bs.collapse",null));var r=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[r](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var a=function(){this.$element.removeClass("collapsing").addClass("collapse in")[r](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return a.call(this);var l=t.camelCase(["scroll",r].join("-"));this.$element.one("bsTransitionEnd",t.proxy(a,this)).emulateTransitionEnd(n.TRANSITION_DURATION)[r](this.$element[0][l])}}}},n.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var i=this.dimension();this.$element[i](this.$element[i]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var s=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};return t.support.transition?void this.$element[i](0).one("bsTransitionEnd",t.proxy(s,this)).emulateTransitionEnd(n.TRANSITION_DURATION):s.call(this)}}},n.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},n.prototype.getParent=function(){return t(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(t.proxy(function(i,n){var s=t(n);this.addAriaAndCollapsedClass(e(s),s)},this)).end()},n.prototype.addAriaAndCollapsedClass=function(t,e){var i=t.hasClass("in");t.attr("aria-expanded",i),e.toggleClass("collapsed",!i).attr("aria-expanded",i)};var s=t.fn.collapse;t.fn.collapse=i,t.fn.collapse.Constructor=n,t.fn.collapse.noConflict=function(){return t.fn.collapse=s,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(n){var s=t(this);s.attr("data-target")||n.preventDefault();var o=e(s),r=o.data("bs.collapse")?"toggle":s.data();i.call(o,r)})}(jQuery),function(t){"use strict";function e(e){var i=e.attr("data-target");i||(i=(i=e.attr("href"))&&/#[A-Za-z]/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var n=i&&t(i);return n&&n.length?n:e.parent()}function i(i){i&&3===i.which||(t(n).remove(),t(s).each(function(){var n=t(this),s=e(n),o={relatedTarget:this};s.hasClass("open")&&(i&&"click"==i.type&&/input|textarea/i.test(i.target.tagName)&&t.contains(s[0],i.target)||(s.trigger(i=t.Event("hide.bs.dropdown",o)),i.isDefaultPrevented()||(n.attr("aria-expanded","false"),s.removeClass("open").trigger(t.Event("hidden.bs.dropdown",o)))))}))}var n=".dropdown-backdrop",s='[data-toggle="dropdown"]',o=function(e){t(e).on("click.bs.dropdown",this.toggle)};o.VERSION="3.3.6",o.prototype.toggle=function(n){var s=t(this);if(!s.is(".disabled, :disabled")){var o=e(s),r=o.hasClass("open");if(i(),!r){"ontouchstart"in document.documentElement&&!o.closest(".navbar-nav").length&&t(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(t(this)).on("click",i);var a={relatedTarget:this};if(o.trigger(n=t.Event("show.bs.dropdown",a)),n.isDefaultPrevented())return;s.trigger("focus").attr("aria-expanded","true"),o.toggleClass("open").trigger(t.Event("shown.bs.dropdown",a))}return!1}},o.prototype.keydown=function(i){if(/(38|40|27|32)/.test(i.which)&&!/input|textarea/i.test(i.target.tagName)){var n=t(this);if(i.preventDefault(),i.stopPropagation(),!n.is(".disabled, :disabled")){var o=e(n),r=o.hasClass("open");if(!r&&27!=i.which||r&&27==i.which)return 27==i.which&&o.find(s).trigger("focus"),n.trigger("click");var a=o.find(".dropdown-menu li:not(.disabled):visible a");if(a.length){var l=a.index(i.target);38==i.which&&l>0&&l--,40==i.which&&l<a.length-1&&l++,~l||(l=0),a.eq(l).trigger("focus")}}}};var r=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var i=t(this),n=i.data("bs.dropdown");n||i.data("bs.dropdown",n=new o(this)),"string"==typeof e&&n[e].call(i)})},t.fn.dropdown.Constructor=o,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=r,this},t(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",s,o.prototype.toggle).on("keydown.bs.dropdown.data-api",s,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,n){return this.each(function(){var s=t(this),o=s.data("bs.modal"),r=t.extend({},i.DEFAULTS,s.data(),"object"==typeof e&&e);o||s.data("bs.modal",o=new i(this,r)),"string"==typeof e?o[e](n):r.show&&o.show(n)})}var i=function(e,i){this.options=i,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};i.VERSION="3.3.6",i.TRANSITION_DURATION=300,i.BACKDROP_TRANSITION_DURATION=150,i.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},i.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},i.prototype.show=function(e){var n=this,s=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(s),this.isShown||s.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){n.$element.one("mouseup.dismiss.bs.modal",function(e){t(e.target).is(n.$element)&&(n.ignoreBackdropClick=!0)})}),this.backdrop(function(){var s=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),n.adjustDialog(),s&&n.$element[0].offsetWidth,n.$element.addClass("in"),n.enforceFocus();var o=t.Event("shown.bs.modal",{relatedTarget:e});s?n.$dialog.one("bsTransitionEnd",function(){n.$element.trigger("focus").trigger(o)}).emulateTransitionEnd(i.TRANSITION_DURATION):n.$element.trigger("focus").trigger(o)}))},i.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(i.TRANSITION_DURATION):this.hideModal())},i.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},i.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},i.prototype.resize=function(){this.isShown?t(window).on("resize.bs.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.bs.modal")},i.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$body.removeClass("modal-open"),t.resetAdjustments(),t.resetScrollbar(),t.$element.trigger("hidden.bs.modal")})},i.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},i.prototype.backdrop=function(e){var n=this,s=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var o=t.support.transition&&s;if(this.$backdrop=t(document.createElement("div")).addClass("modal-backdrop "+s).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){return this.ignoreBackdropClick?void(this.ignoreBackdropClick=!1):void(t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide()))},this)),o&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;o?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var r=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",r).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):r()}else e&&e()},i.prototype.handleUpdate=function(){this.adjustDialog()},i.prototype.adjustDialog=function(){var t=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&t?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!t?this.scrollbarWidth:""})},i.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},i.prototype.checkScrollbar=function(){var t=window.innerWidth;if(!t){var e=document.documentElement.getBoundingClientRect();t=e.right-Math.abs(e.left)}this.bodyIsOverflowing=document.body.clientWidth<t,this.scrollbarWidth=this.measureScrollbar()},i.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"",this.bodyIsOverflowing&&this.$body.css("padding-right",t+this.scrollbarWidth)},i.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad)},i.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var n=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=i,t.fn.modal.noConflict=function(){return t.fn.modal=n,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(i){var n=t(this),s=n.attr("href"),o=t(n.attr("data-target")||s&&s.replace(/.*(?=#[^\s]+$)/,"")),r=o.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(s)&&s},o.data(),n.data());n.is("a")&&i.preventDefault(),o.one("show.bs.modal",function(t){t.isDefaultPrevented()||o.one("hidden.bs.modal",function(){n.is(":visible")&&n.trigger("focus")})}),e.call(o,r,this)})}(jQuery),function(t){"use strict";var e=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};e.VERSION="3.3.6",e.TRANSITION_DURATION=150,e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},e.prototype.init=function(e,i,n){if(this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(n),this.$viewport=this.options.viewport&&t(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var s=this.options.trigger.split(" "),o=s.length;o--;){var r=s[o];if("click"==r)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=r){var a="hover"==r?"mouseenter":"focusin",l="hover"==r?"mouseleave":"focusout";this.$element.on(a+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return(e=t.extend({},this.getDefaults(),this.$element.data(),e)).delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,n){i[t]!=n&&(e[t]=n)}),e},e.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusin"==e.type?"focus":"hover"]=!0),i.tip().hasClass("in")||"in"==i.hoverState?void(i.hoverState="in"):(clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show())},e.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},e.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusout"==e.type?"focus":"hover"]=!1),i.isInStateTrue()?void 0:(clearTimeout(i.timeout),i.hoverState="out",i.options.delay&&i.options.delay.hide?void(i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)):i.hide())},e.prototype.show=function(){var i=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(i);var n=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(i.isDefaultPrevented()||!n)return;var s=this,o=this.tip(),r=this.getUID(this.type);this.setContent(),o.attr("id",r),this.$element.attr("aria-describedby",r),this.options.animation&&o.addClass("fade");var a="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,l=/\s?auto?\s?/i,h=l.test(a);h&&(a=a.replace(l,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(a).data("bs."+this.type,this),this.options.container?o.appendTo(this.options.container):o.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var c=this.getPosition(),u=o[0].offsetWidth,d=o[0].offsetHeight;if(h){var p=a,f=this.getPosition(this.$viewport);a="bottom"==a&&c.bottom+d>f.bottom?"top":"top"==a&&c.top-d<f.top?"bottom":"right"==a&&c.right+u>f.width?"left":"left"==a&&c.left-u<f.left?"right":a,o.removeClass(p).addClass(a)}var m=this.getCalculatedOffset(a,c,u,d);this.applyPlacement(m,a);var g=function(){var t=s.hoverState;s.$element.trigger("shown.bs."+s.type),s.hoverState=null,"out"==t&&s.leave(s)};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",g).emulateTransitionEnd(e.TRANSITION_DURATION):g()}},e.prototype.applyPlacement=function(e,i){var n=this.tip(),s=n[0].offsetWidth,o=n[0].offsetHeight,r=parseInt(n.css("margin-top"),10),a=parseInt(n.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(a)&&(a=0),e.top+=r,e.left+=a,t.offset.setOffset(n[0],t.extend({using:function(t){n.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),n.addClass("in");var l=n[0].offsetWidth,h=n[0].offsetHeight;"top"==i&&h!=o&&(e.top=e.top+o-h);var c=this.getViewportAdjustedDelta(i,e,l,h);c.left?e.left+=c.left:e.top+=c.top;var u=/top|bottom/.test(i),d=u?2*c.left-s+l:2*c.top-o+h,p=u?"offsetWidth":"offsetHeight";n.offset(e),this.replaceArrow(d,n[0][p],u)},e.prototype.replaceArrow=function(t,e,i){this.arrow().css(i?"left":"top",50*(1-t/e)+"%").css(i?"top":"left","")},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();t.find(".tooltip-inner")[this.options.html?"html":"text"](e),t.removeClass("fade in top bottom left right")},e.prototype.hide=function(i){function n(){"in"!=s.hoverState&&o.detach(),s.$element.removeAttr("aria-describedby").trigger("hidden.bs."+s.type),i&&i()}var s=this,o=t(this.$tip),r=t.Event("hide.bs."+this.type);return this.$element.trigger(r),r.isDefaultPrevented()?void 0:(o.removeClass("in"),t.support.transition&&o.hasClass("fade")?o.one("bsTransitionEnd",n).emulateTransitionEnd(e.TRANSITION_DURATION):n(),this.hoverState=null,this)},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(e){var i=(e=e||this.$element)[0],n="BODY"==i.tagName,s=i.getBoundingClientRect();null==s.width&&(s=t.extend({},s,{width:s.right-s.left,height:s.bottom-s.top}));var o=n?{top:0,left:0}:e.offset(),r={scroll:n?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},a=n?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},s,r,a,o)},e.prototype.getCalculatedOffset=function(t,e,i,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-i}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},e.prototype.getViewportAdjustedDelta=function(t,e,i,n){var s={top:0,left:0};if(!this.$viewport)return s;var o=this.options.viewport&&this.options.viewport.padding||0,r=this.getPosition(this.$viewport);if(/right|left/.test(t)){var a=e.top-o-r.scroll,l=e.top+o-r.scroll+n;a<r.top?s.top=r.top-a:l>r.top+r.height&&(s.top=r.top+r.height-l)}else{var h=e.left-o,c=e.left+o+i;h<r.left?s.left=r.left-h:c>r.right&&(s.left=r.left+r.width-c)}return s},e.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},e.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},e.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var i=this;e&&((i=t(e.currentTarget).data("bs."+this.type))||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i))),e?(i.inState.click=!i.inState.click,i.isInStateTrue()?i.enter(i):i.leave(i)):i.tip().hasClass("in")?i.leave(i):i.enter(i)},e.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide(function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null})};var i=t.fn.tooltip;t.fn.tooltip=function(i){return this.each(function(){var n=t(this),s=n.data("bs.tooltip"),o="object"==typeof i&&i;(s||!/destroy|hide/.test(i))&&(s||n.data("bs.tooltip",s=new e(this,o)),"string"==typeof i&&s[i]())})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=i,this}}(jQuery),function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.VERSION="3.3.6",e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),(e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype)).constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),i=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](e),t.find(".popover-content").children().detach().end()[this.options.html?"string"==typeof i?"html":"append":"text"](i),t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var i=t.fn.popover;t.fn.popover=function(i){return this.each(function(){var n=t(this),s=n.data("bs.popover"),o="object"==typeof i&&i;(s||!/destroy|hide/.test(i))&&(s||n.data("bs.popover",s=new e(this,o)),"string"==typeof i&&s[i]())})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=i,this}}(jQuery),function(t){"use strict";function e(i,n){this.$body=t(document.body),this.$scrollElement=t(t(i).is(document.body)?window:i),this.options=t.extend({},e.DEFAULTS,n),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",t.proxy(this.process,this)),this.refresh(),this.process()}function i(i){return this.each(function(){var n=t(this),s=n.data("bs.scrollspy"),o="object"==typeof i&&i;s||n.data("bs.scrollspy",s=new e(this,o)),"string"==typeof i&&s[i]()})}e.VERSION="3.3.6",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e=this,i="offset",n=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),t.isWindow(this.$scrollElement[0])||(i="position",n=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var e=t(this),s=e.data("target")||e.attr("href"),o=/^#./.test(s)&&t(s);return o&&o.length&&o.is(":visible")&&[[o[i]().top+n,s]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){e.offsets.push(this[0]),e.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,i=this.getScrollHeight(),n=this.options.offset+i-this.$scrollElement.height(),s=this.offsets,o=this.targets,r=this.activeTarget;if(this.scrollHeight!=i&&this.refresh(),e>=n)return r!=(t=o[o.length-1])&&this.activate(t);if(r&&e<s[0])return this.activeTarget=null,this.clear();for(t=s.length;t--;)r!=o[t]&&e>=s[t]&&(void 0===s[t+1]||e<s[t+1])&&this.activate(o[t])},e.prototype.activate=function(e){this.activeTarget=e,this.clear();var i=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',n=t(i).parents("li").addClass("active");n.parent(".dropdown-menu").length&&(n=n.closest("li.dropdown").addClass("active")),n.trigger("activate.bs.scrollspy")},e.prototype.clear=function(){t(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var n=t.fn.scrollspy;t.fn.scrollspy=i,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=n,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);i.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.tab");s||n.data("bs.tab",s=new i(this)),"string"==typeof e&&s[e]()})}var i=function(e){this.element=t(e)};i.VERSION="3.3.6",i.TRANSITION_DURATION=150,i.prototype.show=function(){var e=this.element,i=e.closest("ul:not(.dropdown-menu)"),n=e.data("target");if(n||(n=(n=e.attr("href"))&&n.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var s=i.find(".active:last a"),o=t.Event("hide.bs.tab",{relatedTarget:e[0]}),r=t.Event("show.bs.tab",{relatedTarget:s[0]});if(s.trigger(o),e.trigger(r),!r.isDefaultPrevented()&&!o.isDefaultPrevented()){var a=t(n);this.activate(e.closest("li"),i),this.activate(a,a.parent(),function(){s.trigger({type:"hidden.bs.tab",relatedTarget:e[0]}),e.trigger({type:"shown.bs.tab",relatedTarget:s[0]})})}}},i.prototype.activate=function(e,n,s){function o(){r.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),e.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),a?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu").length&&e.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),s&&s()}var r=n.find("> .active"),a=s&&t.support.transition&&(r.length&&r.hasClass("fade")||!!n.find("> .fade").length);r.length&&a?r.one("bsTransitionEnd",o).emulateTransitionEnd(i.TRANSITION_DURATION):o(),r.removeClass("in")};var n=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=n,this};var s=function(i){i.preventDefault(),e.call(t(this),"show")};t(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',s).on("click.bs.tab.data-api",'[data-toggle="pill"]',s)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.affix"),o="object"==typeof e&&e;s||n.data("bs.affix",s=new i(this,o)),"string"==typeof e&&s[e]()})}var i=function(e,n){this.options=t.extend({},i.DEFAULTS,n),this.$target=t(this.options.target).on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};i.VERSION="3.3.6",i.RESET="affix affix-top affix-bottom",i.DEFAULTS={offset:0,target:window},i.prototype.getState=function(t,e,i,n){var s=this.$target.scrollTop(),o=this.$element.offset(),r=this.$target.height();if(null!=i&&"top"==this.affixed)return i>s&&"top";if("bottom"==this.affixed)return null!=i?!(s+this.unpin<=o.top)&&"bottom":!(t-n>=s+r)&&"bottom";var a=null==this.affixed,l=a?s:o.top;return null!=i&&i>=s?"top":null!=n&&l+(a?r:e)>=t-n&&"bottom"},i.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(i.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},i.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},i.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=this.$element.height(),n=this.options.offset,s=n.top,o=n.bottom,r=Math.max(t(document).height(),t(document.body).height());"object"!=typeof n&&(o=s=n),"function"==typeof s&&(s=n.top(this.$element)),"function"==typeof o&&(o=n.bottom(this.$element));var a=this.getState(r,e,s,o);if(this.affixed!=a){null!=this.unpin&&this.$element.css("top","");var l="affix"+(a?"-"+a:""),h=t.Event(l+".bs.affix");if(this.$element.trigger(h),h.isDefaultPrevented())return;this.affixed=a,this.unpin="bottom"==a?this.getPinnedOffset():null,this.$element.removeClass(i.RESET).addClass(l).trigger(l.replace("affix","affixed")+".bs.affix")}"bottom"==a&&this.$element.offset({top:r-e-o})}};var n=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=i,t.fn.affix.noConflict=function(){return t.fn.affix=n,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var i=t(this),n=i.data();n.offset=n.offset||{},null!=n.offsetBottom&&(n.offset.bottom=n.offsetBottom),null!=n.offsetTop&&(n.offset.top=n.offsetTop),e.call(i,n)})})}(jQuery)},function(t,e,i){var n,s,o;s=[i(0)],void 0===(o="function"==typeof(n=function(t){function e(e,n){var s,o,r,a=e.nodeName.toLowerCase();return"area"===a?(o=(s=e.parentNode).name,!(!e.href||!o||"map"!==s.nodeName.toLowerCase())&&!!(r=t("img[usemap='#"+o+"']")[0])&&i(r)):(/^(input|select|textarea|button|object)$/.test(a)?!e.disabled:"a"===a&&e.href||n)&&i(e)}function i(e){return t.expr.filters.visible(e)&&!t(e).parents().addBack().filter(function(){return"hidden"===t.css(this,"visibility")}).length}var n,s,o,r;t.ui=t.ui||{},t.extend(t.ui,{version:"1.11.4",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),t.fn.extend({scrollParent:function(e){var i=this.css("position"),n="absolute"===i,s=e?/(auto|scroll|hidden)/:/(auto|scroll)/,o=this.parents().filter(function(){var e=t(this);return(!n||"static"!==e.css("position"))&&s.test(e.css("overflow")+e.css("overflow-y")+e.css("overflow-x"))}).eq(0);return"fixed"!==i&&o.length?o:t(this[0].ownerDocument||document)},uniqueId:(n=0,function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++n)})}),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&t(this).removeAttr("id")})}}),t.extend(t.expr[":"],{data:t.expr.createPseudo?t.expr.createPseudo(function(e){return function(i){return!!t.data(i,e)}}):function(e,i,n){return!!t.data(e,n[3])},focusable:function(i){return e(i,!isNaN(t.attr(i,"tabindex")))},tabbable:function(i){var n=t.attr(i,"tabindex"),s=isNaN(n);return(s||n>=0)&&e(i,!s)}}),t("<a>").outerWidth(1).jquery||t.each(["Width","Height"],function(e,i){var n="Width"===i?["Left","Right"]:["Top","Bottom"],s=i.toLowerCase(),o={innerWidth:t.fn.innerWidth,innerHeight:t.fn.innerHeight,outerWidth:t.fn.outerWidth,outerHeight:t.fn.outerHeight};function r(e,i,s,o){return t.each(n,function(){i-=parseFloat(t.css(e,"padding"+this))||0,s&&(i-=parseFloat(t.css(e,"border"+this+"Width"))||0),o&&(i-=parseFloat(t.css(e,"margin"+this))||0)}),i}t.fn["inner"+i]=function(e){return void 0===e?o["inner"+i].call(this):this.each(function(){t(this).css(s,r(this,e)+"px")})},t.fn["outer"+i]=function(e,n){return"number"!=typeof e?o["outer"+i].call(this,e):this.each(function(){t(this).css(s,r(this,e,!0,n)+"px")})}}),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(t.fn.removeData=(s=t.fn.removeData,function(e){return arguments.length?s.call(this,t.camelCase(e)):s.call(this)})),t.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),t.fn.extend({focus:(r=t.fn.focus,function(e,i){return"number"==typeof e?this.each(function(){var n=this;setTimeout(function(){t(n).focus(),i&&i.call(n)},e)}):r.apply(this,arguments)}),disableSelection:(o="onselectstart"in document.createElement("div")?"selectstart":"mousedown",function(){return this.bind(o+".ui-disableSelection",function(t){t.preventDefault()})}),enableSelection:function(){return this.unbind(".ui-disableSelection")},zIndex:function(e){if(void 0!==e)return this.css("zIndex",e);if(this.length)for(var i,n,s=t(this[0]);s.length&&s[0]!==document;){if(("absolute"===(i=s.css("position"))||"relative"===i||"fixed"===i)&&(n=parseInt(s.css("zIndex"),10),!isNaN(n)&&0!==n))return n;s=s.parent()}return 0}}),t.ui.plugin={add:function(e,i,n){var s,o=t.ui[e].prototype;for(s in n)o.plugins[s]=o.plugins[s]||[],o.plugins[s].push([i,n[s]])},call:function(t,e,i,n){var s,o=t.plugins[e];if(o&&(n||t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType))for(s=0;s<o.length;s++)t.options[o[s][0]]&&o[s][1].apply(t.element,i)}};var a,l=0,h=Array.prototype.slice;t.cleanData=(a=t.cleanData,function(e){var i,n,s;for(s=0;null!=(n=e[s]);s++)try{(i=t._data(n,"events"))&&i.remove&&t(n).triggerHandler("remove")}catch(t){}a(e)}),t.widget=function(e,i,n){var s,o,r,a,l={},h=e.split(".")[0];return e=e.split(".")[1],s=h+"-"+e,n||(n=i,i=t.Widget),t.expr[":"][s.toLowerCase()]=function(e){return!!t.data(e,s)},t[h]=t[h]||{},o=t[h][e],r=t[h][e]=function(t,e){if(!this._createWidget)return new r(t,e);arguments.length&&this._createWidget(t,e)},t.extend(r,o,{version:n.version,_proto:t.extend({},n),_childConstructors:[]}),(a=new i).options=t.widget.extend({},a.options),t.each(n,function(e,n){var s,o;t.isFunction(n)?l[e]=(s=function(){return i.prototype[e].apply(this,arguments)},o=function(t){return i.prototype[e].apply(this,t)},function(){var t,e=this._super,i=this._superApply;return this._super=s,this._superApply=o,t=n.apply(this,arguments),this._super=e,this._superApply=i,t}):l[e]=n}),r.prototype=t.widget.extend(a,{widgetEventPrefix:o&&a.widgetEventPrefix||e},l,{constructor:r,namespace:h,widgetName:e,widgetFullName:s}),o?(t.each(o._childConstructors,function(e,i){var n=i.prototype;t.widget(n.namespace+"."+n.widgetName,r,i._proto)}),delete o._childConstructors):i._childConstructors.push(r),t.widget.bridge(e,r),r},t.widget.extend=function(e){for(var i,n,s=h.call(arguments,1),o=0,r=s.length;o<r;o++)for(i in s[o])n=s[o][i],s[o].hasOwnProperty(i)&&void 0!==n&&(t.isPlainObject(n)?e[i]=t.isPlainObject(e[i])?t.widget.extend({},e[i],n):t.widget.extend({},n):e[i]=n);return e},t.widget.bridge=function(e,i){var n=i.prototype.widgetFullName||e;t.fn[e]=function(s){var o="string"==typeof s,r=h.call(arguments,1),a=this;return o?this.each(function(){var i,o=t.data(this,n);return"instance"===s?(a=o,!1):o?t.isFunction(o[s])&&"_"!==s.charAt(0)?(i=o[s].apply(o,r))!==o&&void 0!==i?(a=i&&i.jquery?a.pushStack(i.get()):i,!1):void 0:t.error("no such method '"+s+"' for "+e+" widget instance"):t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+s+"'")}):(r.length&&(s=t.widget.extend.apply(null,[s].concat(r))),this.each(function(){var e=t.data(this,n);e?(e.option(s||{}),e._init&&e._init()):t.data(this,n,new i(s,this))})),a}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(e,i){i=t(i||this.defaultElement||this)[0],this.element=t(i),this.uuid=l++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=t(),this.hoverable=t(),this.focusable=t(),i!==this&&(t.data(i,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===i&&this.destroy()}}),this.document=t(i.style?i.ownerDocument:i.document||i),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this.options=t.widget.extend({},this.options,this._getCreateOptions(),e),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:t.noop,_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){this._destroy(),this.element.unbind(this.eventNamespace).removeData(this.widgetFullName).removeData(t.camelCase(this.widgetFullName)),this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled"),this.bindings.unbind(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:t.noop,widget:function(){return this.element},option:function(e,i){var n,s,o,r=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(r={},e=(n=e.split(".")).shift(),n.length){for(s=r[e]=t.widget.extend({},this.options[e]),o=0;o<n.length-1;o++)s[n[o]]=s[n[o]]||{},s=s[n[o]];if(e=n.pop(),1===arguments.length)return void 0===s[e]?null:s[e];s[e]=i}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];r[e]=i}return this._setOptions(r),this},_setOptions:function(t){var e;for(e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return this.options[t]=e,"disabled"===t&&(this.widget().toggleClass(this.widgetFullName+"-disabled",!!e),e&&(this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus"))),this},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_on:function(e,i,n){var s,o=this;"boolean"!=typeof e&&(n=i,i=e,e=!1),n?(i=s=t(i),this.bindings=this.bindings.add(i)):(n=i,i=this.element,s=this.widget()),t.each(n,function(n,r){function a(){if(e||!0!==o.options.disabled&&!t(this).hasClass("ui-state-disabled"))return("string"==typeof r?o[r]:r).apply(o,arguments)}"string"!=typeof r&&(a.guid=r.guid=r.guid||a.guid||t.guid++);var l=n.match(/^([\w:-]*)\s*(.*)$/),h=l[1]+o.eventNamespace,c=l[2];c?s.delegate(c,h,a):i.bind(h,a)})},_off:function(e,i){i=(i||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.unbind(i).undelegate(i),this.bindings=t(this.bindings.not(e).get()),this.focusable=t(this.focusable.not(e).get()),this.hoverable=t(this.hoverable.not(e).get())},_delay:function(t,e){var i=this;return setTimeout(function(){return("string"==typeof t?i[t]:t).apply(i,arguments)},e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){t(e.currentTarget).addClass("ui-state-hover")},mouseleave:function(e){t(e.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){t(e.currentTarget).addClass("ui-state-focus")},focusout:function(e){t(e.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(e,i,n){var s,o,r=this.options[e];if(n=n||{},(i=t.Event(i)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),i.target=this.element[0],o=i.originalEvent)for(s in o)s in i||(i[s]=o[s]);return this.element.trigger(i,n),!(t.isFunction(r)&&!1===r.apply(this.element[0],[i].concat(n))||i.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},function(e,i){t.Widget.prototype["_"+e]=function(n,s,o){"string"==typeof s&&(s={effect:s});var r,a=s?!0===s||"number"==typeof s?i:s.effect||i:e;"number"==typeof(s=s||{})&&(s={duration:s}),r=!t.isEmptyObject(s),s.complete=o,s.delay&&n.delay(s.delay),r&&t.effects&&t.effects.effect[a]?n[e](s):a!==e&&n[a]?n[a](s.duration,s.easing,o):n.queue(function(i){t(this)[e](),o&&o.call(n[0]),i()})}}),t.widget;var c=!1;t(document).mouseup(function(){c=!1}),t.widget("ui.mouse",{version:"1.11.4",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.bind("mousedown."+this.widgetName,function(t){return e._mouseDown(t)}).bind("click."+this.widgetName,function(i){if(!0===t.data(i.target,e.widgetName+".preventClickEvent"))return t.removeData(i.target,e.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName),this._mouseMoveDelegate&&this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){if(!c){this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(e),this._mouseDownEvent=e;var i=this,n=1===e.which,s=!("string"!=typeof this.options.cancel||!e.target.nodeName)&&t(e.target).closest(this.options.cancel).length;return!(n&&!s&&this._mouseCapture(e)&&(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){i.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),0):(!0===t.data(e.target,this.widgetName+".preventClickEvent")&&t.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return i._mouseMove(t)},this._mouseUpDelegate=function(t){return i._mouseUp(t)},this.document.bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),c=!0,0)))}},_mouseMove:function(e){if(this._mouseMoved){if(t.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button)return this._mouseUp(e);if(!e.which)return this._mouseUp(e)}return(e.which||e.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){return this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&t.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),c=!1,!1},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),function(){t.ui=t.ui||{};var e,i,n=Math.max,s=Math.abs,o=Math.round,r=/left|center|right/,a=/top|center|bottom/,l=/[\+\-]\d+(\.[\d]+)?%?/,h=/^\w+/,c=/%$/,u=t.fn.position;function d(t,e,i){return[parseFloat(t[0])*(c.test(t[0])?e/100:1),parseFloat(t[1])*(c.test(t[1])?i/100:1)]}function p(e,i){return parseInt(t.css(e,i),10)||0}t.position={scrollbarWidth:function(){if(void 0!==e)return e;var i,n,s=t("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>"),o=s.children()[0];return t("body").append(s),i=o.offsetWidth,s.css("overflow","scroll"),i===(n=o.offsetWidth)&&(n=s[0].clientWidth),s.remove(),e=i-n},getScrollInfo:function(e){var i=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),n=e.isWindow||e.isDocument?"":e.element.css("overflow-y"),s="scroll"===i||"auto"===i&&e.width<e.element[0].scrollWidth;return{width:"scroll"===n||"auto"===n&&e.height<e.element[0].scrollHeight?t.position.scrollbarWidth():0,height:s?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var i=t(e||window),n=t.isWindow(i[0]),s=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:n,isDocument:s,offset:i.offset()||{left:0,top:0},scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:n||s?i.width():i.outerWidth(),height:n||s?i.height():i.outerHeight()}}},t.fn.position=function(e){if(!e||!e.of)return u.apply(this,arguments);e=t.extend({},e);var c,f,m,g,v,b,y,_,w=t(e.of),x=t.position.getWithinInfo(e.within),C=t.position.getScrollInfo(x),k=(e.collision||"flip").split(" "),D={};return b=9===(_=(y=w)[0]).nodeType?{width:y.width(),height:y.height(),offset:{top:0,left:0}}:t.isWindow(_)?{width:y.width(),height:y.height(),offset:{top:y.scrollTop(),left:y.scrollLeft()}}:_.preventDefault?{width:0,height:0,offset:{top:_.pageY,left:_.pageX}}:{width:y.outerWidth(),height:y.outerHeight(),offset:y.offset()},w[0].preventDefault&&(e.at="left top"),f=b.width,m=b.height,g=b.offset,v=t.extend({},g),t.each(["my","at"],function(){var t,i,n=(e[this]||"").split(" ");1===n.length&&(n=r.test(n[0])?n.concat(["center"]):a.test(n[0])?["center"].concat(n):["center","center"]),n[0]=r.test(n[0])?n[0]:"center",n[1]=a.test(n[1])?n[1]:"center",t=l.exec(n[0]),i=l.exec(n[1]),D[this]=[t?t[0]:0,i?i[0]:0],e[this]=[h.exec(n[0])[0],h.exec(n[1])[0]]}),1===k.length&&(k[1]=k[0]),"right"===e.at[0]?v.left+=f:"center"===e.at[0]&&(v.left+=f/2),"bottom"===e.at[1]?v.top+=m:"center"===e.at[1]&&(v.top+=m/2),c=d(D.at,f,m),v.left+=c[0],v.top+=c[1],this.each(function(){var r,a,l=t(this),h=l.outerWidth(),u=l.outerHeight(),b=p(this,"marginLeft"),y=p(this,"marginTop"),_=h+b+p(this,"marginRight")+C.width,T=u+y+p(this,"marginBottom")+C.height,S=t.extend({},v),E=d(D.my,l.outerWidth(),l.outerHeight());"right"===e.my[0]?S.left-=h:"center"===e.my[0]&&(S.left-=h/2),"bottom"===e.my[1]?S.top-=u:"center"===e.my[1]&&(S.top-=u/2),S.left+=E[0],S.top+=E[1],i||(S.left=o(S.left),S.top=o(S.top)),r={marginLeft:b,marginTop:y},t.each(["left","top"],function(i,n){t.ui.position[k[i]]&&t.ui.position[k[i]][n](S,{targetWidth:f,targetHeight:m,elemWidth:h,elemHeight:u,collisionPosition:r,collisionWidth:_,collisionHeight:T,offset:[c[0]+E[0],c[1]+E[1]],my:e.my,at:e.at,within:x,elem:l})}),e.using&&(a=function(t){var i=g.left-S.left,o=i+f-h,r=g.top-S.top,a=r+m-u,c={target:{element:w,left:g.left,top:g.top,width:f,height:m},element:{element:l,left:S.left,top:S.top,width:h,height:u},horizontal:o<0?"left":i>0?"right":"center",vertical:a<0?"top":r>0?"bottom":"middle"};f<h&&s(i+o)<f&&(c.horizontal="center"),m<u&&s(r+a)<m&&(c.vertical="middle"),n(s(i),s(o))>n(s(r),s(a))?c.important="horizontal":c.important="vertical",e.using.call(this,t,c)}),l.offset(t.extend(S,{using:a}))})},t.ui.position={fit:{left:function(t,e){var i,s=e.within,o=s.isWindow?s.scrollLeft:s.offset.left,r=s.width,a=t.left-e.collisionPosition.marginLeft,l=o-a,h=a+e.collisionWidth-r-o;e.collisionWidth>r?l>0&&h<=0?(i=t.left+l+e.collisionWidth-r-o,t.left+=l-i):t.left=h>0&&l<=0?o:l>h?o+r-e.collisionWidth:o:l>0?t.left+=l:h>0?t.left-=h:t.left=n(t.left-a,t.left)},top:function(t,e){var i,s=e.within,o=s.isWindow?s.scrollTop:s.offset.top,r=e.within.height,a=t.top-e.collisionPosition.marginTop,l=o-a,h=a+e.collisionHeight-r-o;e.collisionHeight>r?l>0&&h<=0?(i=t.top+l+e.collisionHeight-r-o,t.top+=l-i):t.top=h>0&&l<=0?o:l>h?o+r-e.collisionHeight:o:l>0?t.top+=l:h>0?t.top-=h:t.top=n(t.top-a,t.top)}},flip:{left:function(t,e){var i,n,o=e.within,r=o.offset.left+o.scrollLeft,a=o.width,l=o.isWindow?o.scrollLeft:o.offset.left,h=t.left-e.collisionPosition.marginLeft,c=h-l,u=h+e.collisionWidth-a-l,d="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,p="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,f=-2*e.offset[0];c<0?((i=t.left+d+p+f+e.collisionWidth-a-r)<0||i<s(c))&&(t.left+=d+p+f):u>0&&((n=t.left-e.collisionPosition.marginLeft+d+p+f-l)>0||s(n)<u)&&(t.left+=d+p+f)},top:function(t,e){var i,n,o=e.within,r=o.offset.top+o.scrollTop,a=o.height,l=o.isWindow?o.scrollTop:o.offset.top,h=t.top-e.collisionPosition.marginTop,c=h-l,u=h+e.collisionHeight-a-l,d="top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,p="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,f=-2*e.offset[1];c<0?((n=t.top+d+p+f+e.collisionHeight-a-r)<0||n<s(c))&&(t.top+=d+p+f):u>0&&((i=t.top-e.collisionPosition.marginTop+d+p+f-l)>0||s(i)<u)&&(t.top+=d+p+f)}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}},function(){var e,n,s,o,r,a=document.getElementsByTagName("body")[0],l=document.createElement("div");for(r in e=document.createElement(a?"div":"body"),s={visibility:"hidden",width:0,height:0,border:0,margin:0,background:"none"},a&&t.extend(s,{position:"absolute",left:"-1000px",top:"-1000px"}),s)e.style[r]=s[r];e.appendChild(l),(n=a||document.documentElement).insertBefore(e,n.firstChild),l.style.cssText="position: absolute; left: 10.7432222px;",o=t(l).offset().left,i=o>10&&o<11,e.innerHTML="",n.removeChild(e)}()}(),t.ui.position,t.widget("ui.draggable",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative(),this.options.addClasses&&this.element.addClass("ui-draggable"),this.options.disabled&&this.element.addClass("ui-draggable-disabled"),this._setHandleClassName(),this._mouseInit()},_setOption:function(t,e){this._super(t,e),"handle"===t&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){(this.helper||this.element).is(".ui-draggable-dragging")?this.destroyOnClear=!0:(this.element.removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled"),this._removeHandleClassName(),this._mouseDestroy())},_mouseCapture:function(e){var i=this.options;return this._blurActiveElement(e),!(this.helper||i.disabled||t(e.target).closest(".ui-resizable-handle").length>0||(this.handle=this._getHandle(e),!this.handle||(this._blockFrames(!0===i.iframeFix?"iframe":i.iframeFix),0)))},_blockFrames:function(e){this.iframeBlocks=this.document.find(e).map(function(){var e=t(this);return t("<div>").css("position","absolute").appendTo(e.parent()).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).offset(e.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(e){var i=this.document[0];if(this.handleElement.is(e.target))try{i.activeElement&&"body"!==i.activeElement.nodeName.toLowerCase()&&t(i.activeElement).blur()}catch(t){}},_mouseStart:function(e){var i=this.options;return this.helper=this._createHelper(e),this.helper.addClass("ui-draggable-dragging"),this._cacheHelperProportions(),t.ui.ddmanager&&(t.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=this.helper.parents().filter(function(){return"fixed"===t(this).css("position")}).length>0,this.positionAbs=this.element.offset(),this._refreshOffsets(e),this.originalPosition=this.position=this._generatePosition(e,!1),this.originalPageX=e.pageX,this.originalPageY=e.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this._setContainment(),!1===this._trigger("start",e)?(this._clear(),!1):(this._cacheHelperProportions(),t.ui.ddmanager&&!i.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this._normalizeRightBottom(),this._mouseDrag(e,!0),t.ui.ddmanager&&t.ui.ddmanager.dragStart(this,e),!0)},_refreshOffsets:function(t){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()},this.offset.click={left:t.pageX-this.offset.left,top:t.pageY-this.offset.top}},_mouseDrag:function(e,i){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(e,!0),this.positionAbs=this._convertPositionTo("absolute"),!i){var n=this._uiHash();if(!1===this._trigger("drag",e,n))return this._mouseUp({}),!1;this.position=n.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),!1},_mouseStop:function(e){var i=this,n=!1;return t.ui.ddmanager&&!this.options.dropBehaviour&&(n=t.ui.ddmanager.drop(this,e)),this.dropped&&(n=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!n||"valid"===this.options.revert&&n||!0===this.options.revert||t.isFunction(this.options.revert)&&this.options.revert.call(this.element,n)?t(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){!1!==i._trigger("stop",e)&&i._clear()}):!1!==this._trigger("stop",e)&&this._clear(),!1},_mouseUp:function(e){return this._unblockFrames(),t.ui.ddmanager&&t.ui.ddmanager.dragStop(this,e),this.handleElement.is(e.target)&&this.element.focus(),t.ui.mouse.prototype._mouseUp.call(this,e)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp({}):this._clear(),this},_getHandle:function(e){return!this.options.handle||!!t(e.target).closest(this.element.find(this.options.handle)).length},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element,this.handleElement.addClass("ui-draggable-handle")},_removeHandleClassName:function(){this.handleElement.removeClass("ui-draggable-handle")},_createHelper:function(e){var i=this.options,n=t.isFunction(i.helper),s=n?t(i.helper.apply(this.element[0],[e])):"clone"===i.helper?this.element.clone().removeAttr("id"):this.element;return s.parents("body").length||s.appendTo("parent"===i.appendTo?this.element[0].parentNode:i.appendTo),n&&s[0]===this.element[0]&&this._setPositionRelative(),s[0]===this.element[0]||/(fixed|absolute)/.test(s.css("position"))||s.css("position","absolute"),s},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(e){"string"==typeof e&&(e=e.split(" ")),t.isArray(e)&&(e={left:+e[0],top:+e[1]||0}),"left"in e&&(this.offset.click.left=e.left+this.margins.left),"right"in e&&(this.offset.click.left=this.helperProportions.width-e.right+this.margins.left),"top"in e&&(this.offset.click.top=e.top+this.margins.top),"bottom"in e&&(this.offset.click.top=this.helperProportions.height-e.bottom+this.margins.top)},_isRootNode:function(t){return/(html|body)/i.test(t.tagName)||t===this.document[0]},_getParentOffset:function(){var e=this.offsetParent.offset(),i=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==i&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),this._isRootNode(this.offsetParent[0])&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var t=this.element.position(),e=this._isRootNode(this.scrollParent[0]);return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+(e?0:this.scrollParent.scrollTop()),left:t.left-(parseInt(this.helper.css("left"),10)||0)+(e?0:this.scrollParent.scrollLeft())}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,s=this.options,o=this.document[0];this.relativeContainer=null,s.containment?"window"!==s.containment?"document"!==s.containment?s.containment.constructor!==Array?("parent"===s.containment&&(s.containment=this.helper[0].parentNode),(n=(i=t(s.containment))[0])&&(e=/(scroll|auto)/.test(i.css("overflow")),this.containment=[(parseInt(i.css("borderLeftWidth"),10)||0)+(parseInt(i.css("paddingLeft"),10)||0),(parseInt(i.css("borderTopWidth"),10)||0)+(parseInt(i.css("paddingTop"),10)||0),(e?Math.max(n.scrollWidth,n.offsetWidth):n.offsetWidth)-(parseInt(i.css("borderRightWidth"),10)||0)-(parseInt(i.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(e?Math.max(n.scrollHeight,n.offsetHeight):n.offsetHeight)-(parseInt(i.css("borderBottomWidth"),10)||0)-(parseInt(i.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=i)):this.containment=s.containment:this.containment=[0,0,t(o).width()-this.helperProportions.width-this.margins.left,(t(o).height()||o.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:this.containment=[t(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,t(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,t(window).scrollLeft()+t(window).width()-this.helperProportions.width-this.margins.left,t(window).scrollTop()+(t(window).height()||o.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:this.containment=null},_convertPositionTo:function(t,e){e||(e=this.position);var i="absolute"===t?1:-1,n=this._isRootNode(this.scrollParent[0]);return{top:e.top+this.offset.relative.top*i+this.offset.parent.top*i-("fixed"===this.cssPosition?-this.offset.scroll.top:n?0:this.offset.scroll.top)*i,left:e.left+this.offset.relative.left*i+this.offset.parent.left*i-("fixed"===this.cssPosition?-this.offset.scroll.left:n?0:this.offset.scroll.left)*i}},_generatePosition:function(t,e){var i,n,s,o,r=this.options,a=this._isRootNode(this.scrollParent[0]),l=t.pageX,h=t.pageY;return a&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),e&&(this.containment&&(this.relativeContainer?(n=this.relativeContainer.offset(),i=[this.containment[0]+n.left,this.containment[1]+n.top,this.containment[2]+n.left,this.containment[3]+n.top]):i=this.containment,t.pageX-this.offset.click.left<i[0]&&(l=i[0]+this.offset.click.left),t.pageY-this.offset.click.top<i[1]&&(h=i[1]+this.offset.click.top),t.pageX-this.offset.click.left>i[2]&&(l=i[2]+this.offset.click.left),t.pageY-this.offset.click.top>i[3]&&(h=i[3]+this.offset.click.top)),r.grid&&(s=r.grid[1]?this.originalPageY+Math.round((h-this.originalPageY)/r.grid[1])*r.grid[1]:this.originalPageY,h=i?s-this.offset.click.top>=i[1]||s-this.offset.click.top>i[3]?s:s-this.offset.click.top>=i[1]?s-r.grid[1]:s+r.grid[1]:s,o=r.grid[0]?this.originalPageX+Math.round((l-this.originalPageX)/r.grid[0])*r.grid[0]:this.originalPageX,l=i?o-this.offset.click.left>=i[0]||o-this.offset.click.left>i[2]?o:o-this.offset.click.left>=i[0]?o-r.grid[0]:o+r.grid[0]:o),"y"===r.axis&&(l=this.originalPageX),"x"===r.axis&&(h=this.originalPageY)),{top:h-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:a?0:this.offset.scroll.top),left:l-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:a?0:this.offset.scroll.left)}},_clear:function(){this.helper.removeClass("ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1,this.destroyOnClear&&this.destroy()},_normalizeRightBottom:function(){"y"!==this.options.axis&&"auto"!==this.helper.css("right")&&(this.helper.width(this.helper.width()),this.helper.css("right","auto")),"x"!==this.options.axis&&"auto"!==this.helper.css("bottom")&&(this.helper.height(this.helper.height()),this.helper.css("bottom","auto"))},_trigger:function(e,i,n){return n=n||this._uiHash(),t.ui.plugin.call(this,e,[i,n,this],!0),/^(drag|start|stop)/.test(e)&&(this.positionAbs=this._convertPositionTo("absolute"),n.offset=this.positionAbs),t.Widget.prototype._trigger.call(this,e,i,n)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),t.ui.plugin.add("draggable","connectToSortable",{start:function(e,i,n){var s=t.extend({},i,{item:n.element});n.sortables=[],t(n.options.connectToSortable).each(function(){var i=t(this).sortable("instance");i&&!i.options.disabled&&(n.sortables.push(i),i.refreshPositions(),i._trigger("activate",e,s))})},stop:function(e,i,n){var s=t.extend({},i,{item:n.element});n.cancelHelperRemoval=!1,t.each(n.sortables,function(){this.isOver?(this.isOver=0,n.cancelHelperRemoval=!0,this.cancelHelperRemoval=!1,this._storedCSS={position:this.placeholder.css("position"),top:this.placeholder.css("top"),left:this.placeholder.css("left")},this._mouseStop(e),this.options.helper=this.options._helper):(this.cancelHelperRemoval=!0,this._trigger("deactivate",e,s))})},drag:function(e,i,n){t.each(n.sortables,function(){var s=!1,o=this;o.positionAbs=n.positionAbs,o.helperProportions=n.helperProportions,o.offset.click=n.offset.click,o._intersectsWith(o.containerCache)&&(s=!0,t.each(n.sortables,function(){return this.positionAbs=n.positionAbs,this.helperProportions=n.helperProportions,this.offset.click=n.offset.click,this!==o&&this._intersectsWith(this.containerCache)&&t.contains(o.element[0],this.element[0])&&(s=!1),s})),s?(o.isOver||(o.isOver=1,n._parent=i.helper.parent(),o.currentItem=i.helper.appendTo(o.element).data("ui-sortable-item",!0),o.options._helper=o.options.helper,o.options.helper=function(){return i.helper[0]},e.target=o.currentItem[0],o._mouseCapture(e,!0),o._mouseStart(e,!0,!0),o.offset.click.top=n.offset.click.top,o.offset.click.left=n.offset.click.left,o.offset.parent.left-=n.offset.parent.left-o.offset.parent.left,o.offset.parent.top-=n.offset.parent.top-o.offset.parent.top,n._trigger("toSortable",e),n.dropped=o.element,t.each(n.sortables,function(){this.refreshPositions()}),n.currentItem=n.element,o.fromOutside=n),o.currentItem&&(o._mouseDrag(e),i.position=o.position)):o.isOver&&(o.isOver=0,o.cancelHelperRemoval=!0,o.options._revert=o.options.revert,o.options.revert=!1,o._trigger("out",e,o._uiHash(o)),o._mouseStop(e,!0),o.options.revert=o.options._revert,o.options.helper=o.options._helper,o.placeholder&&o.placeholder.remove(),i.helper.appendTo(n._parent),n._refreshOffsets(e),i.position=n._generatePosition(e,!0),n._trigger("fromSortable",e),n.dropped=!1,t.each(n.sortables,function(){this.refreshPositions()}))})}}),t.ui.plugin.add("draggable","cursor",{start:function(e,i,n){var s=t("body"),o=n.options;s.css("cursor")&&(o._cursor=s.css("cursor")),s.css("cursor",o.cursor)},stop:function(e,i,n){var s=n.options;s._cursor&&t("body").css("cursor",s._cursor)}}),t.ui.plugin.add("draggable","opacity",{start:function(e,i,n){var s=t(i.helper),o=n.options;s.css("opacity")&&(o._opacity=s.css("opacity")),s.css("opacity",o.opacity)},stop:function(e,i,n){var s=n.options;s._opacity&&t(i.helper).css("opacity",s._opacity)}}),t.ui.plugin.add("draggable","scroll",{start:function(t,e,i){i.scrollParentNotHidden||(i.scrollParentNotHidden=i.helper.scrollParent(!1)),i.scrollParentNotHidden[0]!==i.document[0]&&"HTML"!==i.scrollParentNotHidden[0].tagName&&(i.overflowOffset=i.scrollParentNotHidden.offset())},drag:function(e,i,n){var s=n.options,o=!1,r=n.scrollParentNotHidden[0],a=n.document[0];r!==a&&"HTML"!==r.tagName?(s.axis&&"x"===s.axis||(n.overflowOffset.top+r.offsetHeight-e.pageY<s.scrollSensitivity?r.scrollTop=o=r.scrollTop+s.scrollSpeed:e.pageY-n.overflowOffset.top<s.scrollSensitivity&&(r.scrollTop=o=r.scrollTop-s.scrollSpeed)),s.axis&&"y"===s.axis||(n.overflowOffset.left+r.offsetWidth-e.pageX<s.scrollSensitivity?r.scrollLeft=o=r.scrollLeft+s.scrollSpeed:e.pageX-n.overflowOffset.left<s.scrollSensitivity&&(r.scrollLeft=o=r.scrollLeft-s.scrollSpeed))):(s.axis&&"x"===s.axis||(e.pageY-t(a).scrollTop()<s.scrollSensitivity?o=t(a).scrollTop(t(a).scrollTop()-s.scrollSpeed):t(window).height()-(e.pageY-t(a).scrollTop())<s.scrollSensitivity&&(o=t(a).scrollTop(t(a).scrollTop()+s.scrollSpeed))),s.axis&&"y"===s.axis||(e.pageX-t(a).scrollLeft()<s.scrollSensitivity?o=t(a).scrollLeft(t(a).scrollLeft()-s.scrollSpeed):t(window).width()-(e.pageX-t(a).scrollLeft())<s.scrollSensitivity&&(o=t(a).scrollLeft(t(a).scrollLeft()+s.scrollSpeed)))),!1!==o&&t.ui.ddmanager&&!s.dropBehaviour&&t.ui.ddmanager.prepareOffsets(n,e)}}),t.ui.plugin.add("draggable","snap",{start:function(e,i,n){var s=n.options;n.snapElements=[],t(s.snap.constructor!==String?s.snap.items||":data(ui-draggable)":s.snap).each(function(){var e=t(this),i=e.offset();this!==n.element[0]&&n.snapElements.push({item:this,width:e.outerWidth(),height:e.outerHeight(),top:i.top,left:i.left})})},drag:function(e,i,n){var s,o,r,a,l,h,c,u,d,p,f=n.options,m=f.snapTolerance,g=i.offset.left,v=g+n.helperProportions.width,b=i.offset.top,y=b+n.helperProportions.height;for(d=n.snapElements.length-1;d>=0;d--)h=(l=n.snapElements[d].left-n.margins.left)+n.snapElements[d].width,u=(c=n.snapElements[d].top-n.margins.top)+n.snapElements[d].height,v<l-m||g>h+m||y<c-m||b>u+m||!t.contains(n.snapElements[d].item.ownerDocument,n.snapElements[d].item)?(n.snapElements[d].snapping&&n.options.snap.release&&n.options.snap.release.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[d].item})),n.snapElements[d].snapping=!1):("inner"!==f.snapMode&&(s=Math.abs(c-y)<=m,o=Math.abs(u-b)<=m,r=Math.abs(l-v)<=m,a=Math.abs(h-g)<=m,s&&(i.position.top=n._convertPositionTo("relative",{top:c-n.helperProportions.height,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:u,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l-n.helperProportions.width}).left),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h}).left)),p=s||o||r||a,"outer"!==f.snapMode&&(s=Math.abs(c-b)<=m,o=Math.abs(u-y)<=m,r=Math.abs(l-g)<=m,a=Math.abs(h-v)<=m,s&&(i.position.top=n._convertPositionTo("relative",{top:c,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:u-n.helperProportions.height,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l}).left),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h-n.helperProportions.width}).left)),!n.snapElements[d].snapping&&(s||o||r||a||p)&&n.options.snap.snap&&n.options.snap.snap.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[d].item})),n.snapElements[d].snapping=s||o||r||a||p)}}),t.ui.plugin.add("draggable","stack",{start:function(e,i,n){var s,o=n.options,r=t.makeArray(t(o.stack)).sort(function(e,i){return(parseInt(t(e).css("zIndex"),10)||0)-(parseInt(t(i).css("zIndex"),10)||0)});r.length&&(s=parseInt(t(r[0]).css("zIndex"),10)||0,t(r).each(function(e){t(this).css("zIndex",s+e)}),this.css("zIndex",s+r.length))}}),t.ui.plugin.add("draggable","zIndex",{start:function(e,i,n){var s=t(i.helper),o=n.options;s.css("zIndex")&&(o._zIndex=s.css("zIndex")),s.css("zIndex",o.zIndex)},stop:function(e,i,n){var s=n.options;s._zIndex&&t(i.helper).css("zIndex",s._zIndex)}}),t.ui.draggable,t.widget("ui.droppable",{version:"1.11.4",widgetEventPrefix:"drop",options:{accept:"*",activeClass:!1,addClasses:!0,greedy:!1,hoverClass:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var e,i=this.options,n=i.accept;this.isover=!1,this.isout=!0,this.accept=t.isFunction(n)?n:function(t){return t.is(n)},this.proportions=function(){if(!arguments.length)return e||(e={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight});e=arguments[0]},this._addToManager(i.scope),i.addClasses&&this.element.addClass("ui-droppable")},_addToManager:function(e){t.ui.ddmanager.droppables[e]=t.ui.ddmanager.droppables[e]||[],t.ui.ddmanager.droppables[e].push(this)},_splice:function(t){for(var e=0;e<t.length;e++)t[e]===this&&t.splice(e,1)},_destroy:function(){var e=t.ui.ddmanager.droppables[this.options.scope];this._splice(e),this.element.removeClass("ui-droppable ui-droppable-disabled")},_setOption:function(e,i){if("accept"===e)this.accept=t.isFunction(i)?i:function(t){return t.is(i)};else if("scope"===e){var n=t.ui.ddmanager.droppables[this.options.scope];this._splice(n),this._addToManager(i)}this._super(e,i)},_activate:function(e){var i=t.ui.ddmanager.current;this.options.activeClass&&this.element.addClass(this.options.activeClass),i&&this._trigger("activate",e,this.ui(i))},_deactivate:function(e){var i=t.ui.ddmanager.current;this.options.activeClass&&this.element.removeClass(this.options.activeClass),i&&this._trigger("deactivate",e,this.ui(i))},_over:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.hoverClass&&this.element.addClass(this.options.hoverClass),this._trigger("over",e,this.ui(i)))},_out:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("out",e,this.ui(i)))},_drop:function(e,i){var n=i||t.ui.ddmanager.current,s=!1;return!(!n||(n.currentItem||n.element)[0]===this.element[0])&&(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var i=t(this).droppable("instance");if(i.options.greedy&&!i.options.disabled&&i.options.scope===n.options.scope&&i.accept.call(i.element[0],n.currentItem||n.element)&&t.ui.intersect(n,t.extend(i,{offset:i.element.offset()}),i.options.tolerance,e))return s=!0,!1}),!s&&!!this.accept.call(this.element[0],n.currentItem||n.element)&&(this.options.activeClass&&this.element.removeClass(this.options.activeClass),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("drop",e,this.ui(n)),this.element))},ui:function(t){return{draggable:t.currentItem||t.element,helper:t.helper,position:t.position,offset:t.positionAbs}}}),t.ui.intersect=function(){function t(t,e,i){return t>=e&&t<e+i}return function(e,i,n,s){if(!i.offset)return!1;var o=(e.positionAbs||e.position.absolute).left+e.margins.left,r=(e.positionAbs||e.position.absolute).top+e.margins.top,a=o+e.helperProportions.width,l=r+e.helperProportions.height,h=i.offset.left,c=i.offset.top,u=h+i.proportions().width,d=c+i.proportions().height;switch(n){case"fit":return h<=o&&a<=u&&c<=r&&l<=d;case"intersect":return h<o+e.helperProportions.width/2&&a-e.helperProportions.width/2<u&&c<r+e.helperProportions.height/2&&l-e.helperProportions.height/2<d;case"pointer":return t(s.pageY,c,i.proportions().height)&&t(s.pageX,h,i.proportions().width);case"touch":return(r>=c&&r<=d||l>=c&&l<=d||r<c&&l>d)&&(o>=h&&o<=u||a>=h&&a<=u||o<h&&a>u);default:return!1}}}(),t.ui.ddmanager={current:null,droppables:{default:[]},prepareOffsets:function(e,i){var n,s,o=t.ui.ddmanager.droppables[e.options.scope]||[],r=i?i.type:null,a=(e.currentItem||e.element).find(":data(ui-droppable)").addBack();t:for(n=0;n<o.length;n++)if(!(o[n].options.disabled||e&&!o[n].accept.call(o[n].element[0],e.currentItem||e.element))){for(s=0;s<a.length;s++)if(a[s]===o[n].element[0]){o[n].proportions().height=0;continue t}o[n].visible="none"!==o[n].element.css("display"),o[n].visible&&("mousedown"===r&&o[n]._activate.call(o[n],i),o[n].offset=o[n].element.offset(),o[n].proportions({width:o[n].element[0].offsetWidth,height:o[n].element[0].offsetHeight}))}},drop:function(e,i){var n=!1;return t.each((t.ui.ddmanager.droppables[e.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&t.ui.intersect(e,this,this.options.tolerance,i)&&(n=this._drop.call(this,i)||n),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],e.currentItem||e.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,i)))}),n},dragStart:function(e,i){e.element.parentsUntil("body").bind("scroll.droppable",function(){e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)})},drag:function(e,i){e.options.refreshPositions&&t.ui.ddmanager.prepareOffsets(e,i),t.each(t.ui.ddmanager.droppables[e.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var n,s,o,r=t.ui.intersect(e,this,this.options.tolerance,i),a=!r&&this.isover?"isout":r&&!this.isover?"isover":null;a&&(this.options.greedy&&(s=this.options.scope,(o=this.element.parents(":data(ui-droppable)").filter(function(){return t(this).droppable("instance").options.scope===s})).length&&((n=t(o[0]).droppable("instance")).greedyChild="isover"===a)),n&&"isover"===a&&(n.isover=!1,n.isout=!0,n._out.call(n,i)),this[a]=!0,this["isout"===a?"isover":"isout"]=!1,this["isover"===a?"_over":"_out"].call(this,i),n&&"isout"===a&&(n.isout=!1,n.isover=!0,n._over.call(n,i)))}})},dragStop:function(e,i){e.element.parentsUntil("body").unbind("scroll.droppable"),e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}},t.ui.droppable,t.widget("ui.resizable",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(t){return parseInt(t,10)||0},_isNumber:function(t){return!isNaN(parseInt(t,10))},_hasScroll:function(e,i){if("hidden"===t(e).css("overflow"))return!1;var n=i&&"left"===i?"scrollLeft":"scrollTop",s=!1;return e[n]>0||(e[n]=1,s=e[n]>0,e[n]=0,s)},_create:function(){var e,i,n,s,o=this,r=this.options;if(this.element.addClass("ui-resizable"),t.extend(this,{_aspectRatio:!!r.aspectRatio,aspectRatio:r.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:r.helper||r.ghost||r.animate?r.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(t("<div class='ui-wrapper' style='overflow: hidden;'></div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,this.element.css({marginLeft:this.originalElement.css("marginLeft"),marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom")}),this.originalElement.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0}),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css({margin:this.originalElement.css("margin")}),this._proportionallyResize()),this.handles=r.handles||(t(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=t(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),e=this.handles.split(","),this.handles={},i=0;i<e.length;i++)n=t.trim(e[i]),(s=t("<div class='ui-resizable-handle ui-resizable-"+n+"'></div>")).css({zIndex:r.zIndex}),"se"===n&&s.addClass("ui-icon ui-icon-gripsmall-diagonal-se"),this.handles[n]=".ui-resizable-"+n,this.element.append(s);this._renderAxis=function(e){var i,n,s,r;for(i in e=e||this.element,this.handles)this.handles[i].constructor===String?this.handles[i]=this.element.children(this.handles[i]).first().show():(this.handles[i].jquery||this.handles[i].nodeType)&&(this.handles[i]=t(this.handles[i]),this._on(this.handles[i],{mousedown:o._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(n=t(this.handles[i],this.element),r=/sw|ne|nw|se|n|s/.test(i)?n.outerHeight():n.outerWidth(),s=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),e.css(s,r),this._proportionallyResize()),this._handles=this._handles.add(this.handles[i])},this._renderAxis(this.element),this._handles=this._handles.add(this.element.find(".ui-resizable-handle")),this._handles.disableSelection(),this._handles.mouseover(function(){o.resizing||(this.className&&(s=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),o.axis=s&&s[1]?s[1]:"se")}),r.autoHide&&(this._handles.hide(),t(this.element).addClass("ui-resizable-autohide").mouseenter(function(){r.disabled||(t(this).removeClass("ui-resizable-autohide"),o._handles.show())}).mouseleave(function(){r.disabled||o.resizing||(t(this).addClass("ui-resizable-autohide"),o._handles.hide())})),this._mouseInit()},_destroy:function(){this._mouseDestroy();var e,i=function(e){t(e).removeClass("ui-resizable ui-resizable-disabled ui-resizable-resizing").removeData("resizable").removeData("ui-resizable").unbind(".resizable").find(".ui-resizable-handle").remove()};return this.elementIsWrapper&&(i(this.element),e=this.element,this.originalElement.css({position:e.css("position"),width:e.outerWidth(),height:e.outerHeight(),top:e.css("top"),left:e.css("left")}).insertAfter(e),e.remove()),this.originalElement.css("resize",this.originalResizeStyle),i(this.originalElement),this},_mouseCapture:function(e){var i,n,s=!1;for(i in this.handles)((n=t(this.handles[i])[0])===e.target||t.contains(n,e.target))&&(s=!0);return!this.options.disabled&&s},_mouseStart:function(e){var i,n,s,o=this.options,r=this.element;return this.resizing=!0,this._renderProxy(),i=this._num(this.helper.css("left")),n=this._num(this.helper.css("top")),o.containment&&(i+=t(o.containment).scrollLeft()||0,n+=t(o.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:i,top:n},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:r.width(),height:r.height()},this.originalSize=this._helper?{width:r.outerWidth(),height:r.outerHeight()}:{width:r.width(),height:r.height()},this.sizeDiff={width:r.outerWidth()-r.width(),height:r.outerHeight()-r.height()},this.originalPosition={left:i,top:n},this.originalMousePosition={left:e.pageX,top:e.pageY},this.aspectRatio="number"==typeof o.aspectRatio?o.aspectRatio:this.originalSize.width/this.originalSize.height||1,s=t(".ui-resizable-"+this.axis).css("cursor"),t("body").css("cursor","auto"===s?this.axis+"-resize":s),r.addClass("ui-resizable-resizing"),this._propagate("start",e),!0},_mouseDrag:function(e){var i,n,s=this.originalMousePosition,o=this.axis,r=e.pageX-s.left||0,a=e.pageY-s.top||0,l=this._change[o];return this._updatePrevProperties(),!!l&&(i=l.apply(this,[e,r,a]),this._updateVirtualBoundaries(e.shiftKey),(this._aspectRatio||e.shiftKey)&&(i=this._updateRatio(i,e)),i=this._respectSize(i,e),this._updateCache(i),this._propagate("resize",e),n=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),t.isEmptyObject(n)||(this._updatePrevProperties(),this._trigger("resize",e,this.ui()),this._applyChanges()),!1)},_mouseStop:function(e){this.resizing=!1;var i,n,s,o,r,a,l,h=this.options;return this._helper&&(s=(n=(i=this._proportionallyResizeElements).length&&/textarea/i.test(i[0].nodeName))&&this._hasScroll(i[0],"left")?0:this.sizeDiff.height,o=n?0:this.sizeDiff.width,r={width:this.helper.width()-o,height:this.helper.height()-s},a=parseInt(this.element.css("left"),10)+(this.position.left-this.originalPosition.left)||null,l=parseInt(this.element.css("top"),10)+(this.position.top-this.originalPosition.top)||null,h.animate||this.element.css(t.extend(r,{top:l,left:a})),this.helper.height(this.size.height),this.helper.width(this.size.width),this._helper&&!h.animate&&this._proportionallyResize()),t("body").css("cursor","auto"),this.element.removeClass("ui-resizable-resizing"),this._propagate("stop",e),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var t={};return this.position.top!==this.prevPosition.top&&(t.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(t.left=this.position.left+"px"),this.size.width!==this.prevSize.width&&(t.width=this.size.width+"px"),this.size.height!==this.prevSize.height&&(t.height=this.size.height+"px"),this.helper.css(t),t},_updateVirtualBoundaries:function(t){var e,i,n,s,o,r=this.options;o={minWidth:this._isNumber(r.minWidth)?r.minWidth:0,maxWidth:this._isNumber(r.maxWidth)?r.maxWidth:1/0,minHeight:this._isNumber(r.minHeight)?r.minHeight:0,maxHeight:this._isNumber(r.maxHeight)?r.maxHeight:1/0},(this._aspectRatio||t)&&(e=o.minHeight*this.aspectRatio,n=o.minWidth/this.aspectRatio,i=o.maxHeight*this.aspectRatio,s=o.maxWidth/this.aspectRatio,e>o.minWidth&&(o.minWidth=e),n>o.minHeight&&(o.minHeight=n),i<o.maxWidth&&(o.maxWidth=i),s<o.maxHeight&&(o.maxHeight=s)),this._vBoundaries=o},_updateCache:function(t){this.offset=this.helper.offset(),this._isNumber(t.left)&&(this.position.left=t.left),this._isNumber(t.top)&&(this.position.top=t.top),this._isNumber(t.height)&&(this.size.height=t.height),this._isNumber(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var e=this.position,i=this.size,n=this.axis;return this._isNumber(t.height)?t.width=t.height*this.aspectRatio:this._isNumber(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===n&&(t.left=e.left+(i.width-t.width),t.top=null),"nw"===n&&(t.top=e.top+(i.height-t.height),t.left=e.left+(i.width-t.width)),t},_respectSize:function(t){var e=this._vBoundaries,i=this.axis,n=this._isNumber(t.width)&&e.maxWidth&&e.maxWidth<t.width,s=this._isNumber(t.height)&&e.maxHeight&&e.maxHeight<t.height,o=this._isNumber(t.width)&&e.minWidth&&e.minWidth>t.width,r=this._isNumber(t.height)&&e.minHeight&&e.minHeight>t.height,a=this.originalPosition.left+this.originalSize.width,l=this.position.top+this.size.height,h=/sw|nw|w/.test(i),c=/nw|ne|n/.test(i);return o&&(t.width=e.minWidth),r&&(t.height=e.minHeight),n&&(t.width=e.maxWidth),s&&(t.height=e.maxHeight),o&&h&&(t.left=a-e.minWidth),n&&h&&(t.left=a-e.maxWidth),r&&c&&(t.top=l-e.minHeight),s&&c&&(t.top=l-e.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_getPaddingPlusBorderDimensions:function(t){for(var e=0,i=[],n=[t.css("borderTopWidth"),t.css("borderRightWidth"),t.css("borderBottomWidth"),t.css("borderLeftWidth")],s=[t.css("paddingTop"),t.css("paddingRight"),t.css("paddingBottom"),t.css("paddingLeft")];e<4;e++)i[e]=parseInt(n[e],10)||0,i[e]+=parseInt(s[e],10)||0;return{height:i[0]+i[2],width:i[1]+i[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,e=0,i=this.helper||this.element;e<this._proportionallyResizeElements.length;e++)t=this._proportionallyResizeElements[e],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(t)),t.css({height:i.height()-this.outerDimensions.height||0,width:i.width()-this.outerDimensions.width||0})},_renderProxy:function(){var e=this.element,i=this.options;this.elementOffset=e.offset(),this._helper?(this.helper=this.helper||t("<div style='overflow:hidden;'></div>"),this.helper.addClass(this._helper).css({width:this.element.outerWidth()-1,height:this.element.outerHeight()-1,position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,e){return{width:this.originalSize.width+e}},w:function(t,e){var i=this.originalSize;return{left:this.originalPosition.left+e,width:i.width-e}},n:function(t,e,i){var n=this.originalSize;return{top:this.originalPosition.top+i,height:n.height-i}},s:function(t,e,i){return{height:this.originalSize.height+i}},se:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},sw:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[e,i,n]))},ne:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},nw:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[e,i,n]))}},_propagate:function(e,i){t.ui.plugin.call(this,e,[i,this.ui()]),"resize"!==e&&this._trigger(e,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),t.ui.plugin.add("resizable","animate",{stop:function(e){var i=t(this).resizable("instance"),n=i.options,s=i._proportionallyResizeElements,o=s.length&&/textarea/i.test(s[0].nodeName),r=o&&i._hasScroll(s[0],"left")?0:i.sizeDiff.height,a=o?0:i.sizeDiff.width,l={width:i.size.width-a,height:i.size.height-r},h=parseInt(i.element.css("left"),10)+(i.position.left-i.originalPosition.left)||null,c=parseInt(i.element.css("top"),10)+(i.position.top-i.originalPosition.top)||null;i.element.animate(t.extend(l,c&&h?{top:c,left:h}:{}),{duration:n.animateDuration,easing:n.animateEasing,step:function(){var n={width:parseInt(i.element.css("width"),10),height:parseInt(i.element.css("height"),10),top:parseInt(i.element.css("top"),10),left:parseInt(i.element.css("left"),10)};s&&s.length&&t(s[0]).css({width:n.width,height:n.height}),i._updateCache(n),i._propagate("resize",e)}})}}),t.ui.plugin.add("resizable","containment",{start:function(){var e,i,n,s,o,r,a,l=t(this).resizable("instance"),h=l.options,c=l.element,u=h.containment,d=u instanceof t?u.get(0):/parent/.test(u)?c.parent().get(0):u;d&&(l.containerElement=t(d),/document/.test(u)||u===document?(l.containerOffset={left:0,top:0},l.containerPosition={left:0,top:0},l.parentData={element:t(document),left:0,top:0,width:t(document).width(),height:t(document).height()||document.body.parentNode.scrollHeight}):(e=t(d),i=[],t(["Top","Right","Left","Bottom"]).each(function(t,n){i[t]=l._num(e.css("padding"+n))}),l.containerOffset=e.offset(),l.containerPosition=e.position(),l.containerSize={height:e.innerHeight()-i[3],width:e.innerWidth()-i[1]},n=l.containerOffset,s=l.containerSize.height,o=l.containerSize.width,r=l._hasScroll(d,"left")?d.scrollWidth:o,a=l._hasScroll(d)?d.scrollHeight:s,l.parentData={element:d,left:n.left,top:n.top,width:r,height:a}))},resize:function(e){var i,n,s,o,r=t(this).resizable("instance"),a=r.options,l=r.containerOffset,h=r.position,c=r._aspectRatio||e.shiftKey,u={top:0,left:0},d=r.containerElement,p=!0;d[0]!==document&&/static/.test(d.css("position"))&&(u=l),h.left<(r._helper?l.left:0)&&(r.size.width=r.size.width+(r._helper?r.position.left-l.left:r.position.left-u.left),c&&(r.size.height=r.size.width/r.aspectRatio,p=!1),r.position.left=a.helper?l.left:0),h.top<(r._helper?l.top:0)&&(r.size.height=r.size.height+(r._helper?r.position.top-l.top:r.position.top),c&&(r.size.width=r.size.height*r.aspectRatio,p=!1),r.position.top=r._helper?l.top:0),s=r.containerElement.get(0)===r.element.parent().get(0),o=/relative|absolute/.test(r.containerElement.css("position")),s&&o?(r.offset.left=r.parentData.left+r.position.left,r.offset.top=r.parentData.top+r.position.top):(r.offset.left=r.element.offset().left,r.offset.top=r.element.offset().top),i=Math.abs(r.sizeDiff.width+(r._helper?r.offset.left-u.left:r.offset.left-l.left)),n=Math.abs(r.sizeDiff.height+(r._helper?r.offset.top-u.top:r.offset.top-l.top)),i+r.size.width>=r.parentData.width&&(r.size.width=r.parentData.width-i,c&&(r.size.height=r.size.width/r.aspectRatio,p=!1)),n+r.size.height>=r.parentData.height&&(r.size.height=r.parentData.height-n,c&&(r.size.width=r.size.height*r.aspectRatio,p=!1)),p||(r.position.left=r.prevPosition.left,r.position.top=r.prevPosition.top,r.size.width=r.prevSize.width,r.size.height=r.prevSize.height)},stop:function(){var e=t(this).resizable("instance"),i=e.options,n=e.containerOffset,s=e.containerPosition,o=e.containerElement,r=t(e.helper),a=r.offset(),l=r.outerWidth()-e.sizeDiff.width,h=r.outerHeight()-e.sizeDiff.height;e._helper&&!i.animate&&/relative/.test(o.css("position"))&&t(this).css({left:a.left-s.left-n.left,width:l,height:h}),e._helper&&!i.animate&&/static/.test(o.css("position"))&&t(this).css({left:a.left-s.left-n.left,width:l,height:h})}}),t.ui.plugin.add("resizable","alsoResize",{start:function(){var e=t(this).resizable("instance").options;t(e.alsoResize).each(function(){var e=t(this);e.data("ui-resizable-alsoresize",{width:parseInt(e.width(),10),height:parseInt(e.height(),10),left:parseInt(e.css("left"),10),top:parseInt(e.css("top"),10)})})},resize:function(e,i){var n=t(this).resizable("instance"),s=n.options,o=n.originalSize,r=n.originalPosition,a={height:n.size.height-o.height||0,width:n.size.width-o.width||0,top:n.position.top-r.top||0,left:n.position.left-r.left||0};t(s.alsoResize).each(function(){var e=t(this),n=t(this).data("ui-resizable-alsoresize"),s={},o=e.parents(i.originalElement[0]).length?["width","height"]:["width","height","top","left"];t.each(o,function(t,e){var i=(n[e]||0)+(a[e]||0);i&&i>=0&&(s[e]=i||null)}),e.css(s)})},stop:function(){t(this).removeData("resizable-alsoresize")}}),t.ui.plugin.add("resizable","ghost",{start:function(){var e=t(this).resizable("instance"),i=e.options,n=e.size;e.ghost=e.originalElement.clone(),e.ghost.css({opacity:.25,display:"block",position:"relative",height:n.height,width:n.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass("string"==typeof i.ghost?i.ghost:""),e.ghost.appendTo(e.helper)},resize:function(){var e=t(this).resizable("instance");e.ghost&&e.ghost.css({position:"relative",height:e.size.height,width:e.size.width})},stop:function(){var e=t(this).resizable("instance");e.ghost&&e.helper&&e.helper.get(0).removeChild(e.ghost.get(0))}}),t.ui.plugin.add("resizable","grid",{resize:function(){var e,i=t(this).resizable("instance"),n=i.options,s=i.size,o=i.originalSize,r=i.originalPosition,a=i.axis,l="number"==typeof n.grid?[n.grid,n.grid]:n.grid,h=l[0]||1,c=l[1]||1,u=Math.round((s.width-o.width)/h)*h,d=Math.round((s.height-o.height)/c)*c,p=o.width+u,f=o.height+d,m=n.maxWidth&&n.maxWidth<p,g=n.maxHeight&&n.maxHeight<f,v=n.minWidth&&n.minWidth>p,b=n.minHeight&&n.minHeight>f;n.grid=l,v&&(p+=h),b&&(f+=c),m&&(p-=h),g&&(f-=c),/^(se|s|e)$/.test(a)?(i.size.width=p,i.size.height=f):/^(ne)$/.test(a)?(i.size.width=p,i.size.height=f,i.position.top=r.top-d):/^(sw)$/.test(a)?(i.size.width=p,i.size.height=f,i.position.left=r.left-u):((f-c<=0||p-h<=0)&&(e=i._getPaddingPlusBorderDimensions(this)),f-c>0?(i.size.height=f,i.position.top=r.top-d):(f=c-e.height,i.size.height=f,i.position.top=r.top+o.height-f),p-h>0?(i.size.width=p,i.position.left=r.left-u):(p=h-e.width,i.size.width=p,i.position.left=r.left+o.width-p))}}),t.ui.resizable,t.widget("ui.selectable",t.ui.mouse,{version:"1.11.4",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var e,i=this;this.element.addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){(e=t(i.options.filter,i.element[0])).addClass("ui-selectee"),e.each(function(){var e=t(this),i=e.offset();t.data(this,"selectable-item",{element:this,$element:e,left:i.left,top:i.top,right:i.left+e.outerWidth(),bottom:i.top+e.outerHeight(),startselected:!1,selected:e.hasClass("ui-selected"),selecting:e.hasClass("ui-selecting"),unselecting:e.hasClass("ui-unselecting")})})},this.refresh(),this.selectees=e.addClass("ui-selectee"),this._mouseInit(),this.helper=t("<div class='ui-selectable-helper'></div>")},_destroy:function(){this.selectees.removeClass("ui-selectee").removeData("selectable-item"),this.element.removeClass("ui-selectable ui-selectable-disabled"),this._mouseDestroy()},_mouseStart:function(e){var i=this,n=this.options;this.opos=[e.pageX,e.pageY],this.options.disabled||(this.selectees=t(n.filter,this.element[0]),this._trigger("start",e),t(n.appendTo).append(this.helper),this.helper.css({left:e.pageX,top:e.pageY,width:0,height:0}),n.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var n=t.data(this,"selectable-item");n.startselected=!0,e.metaKey||e.ctrlKey||(n.$element.removeClass("ui-selected"),n.selected=!1,n.$element.addClass("ui-unselecting"),n.unselecting=!0,i._trigger("unselecting",e,{unselecting:n.element}))}),t(e.target).parents().addBack().each(function(){var n,s=t.data(this,"selectable-item");if(s)return n=!e.metaKey&&!e.ctrlKey||!s.$element.hasClass("ui-selected"),s.$element.removeClass(n?"ui-unselecting":"ui-selected").addClass(n?"ui-selecting":"ui-unselecting"),s.unselecting=!n,s.selecting=n,s.selected=n,n?i._trigger("selecting",e,{selecting:s.element}):i._trigger("unselecting",e,{unselecting:s.element}),!1}))},_mouseDrag:function(e){if(this.dragged=!0,!this.options.disabled){var i,n=this,s=this.options,o=this.opos[0],r=this.opos[1],a=e.pageX,l=e.pageY;return o>a&&(i=a,a=o,o=i),r>l&&(i=l,l=r,r=i),this.helper.css({left:o,top:r,width:a-o,height:l-r}),this.selectees.each(function(){var i=t.data(this,"selectable-item"),h=!1;i&&i.element!==n.element[0]&&("touch"===s.tolerance?h=!(i.left>a||i.right<o||i.top>l||i.bottom<r):"fit"===s.tolerance&&(h=i.left>o&&i.right<a&&i.top>r&&i.bottom<l),h?(i.selected&&(i.$element.removeClass("ui-selected"),i.selected=!1),i.unselecting&&(i.$element.removeClass("ui-unselecting"),i.unselecting=!1),i.selecting||(i.$element.addClass("ui-selecting"),i.selecting=!0,n._trigger("selecting",e,{selecting:i.element}))):(i.selecting&&((e.metaKey||e.ctrlKey)&&i.startselected?(i.$element.removeClass("ui-selecting"),i.selecting=!1,i.$element.addClass("ui-selected"),i.selected=!0):(i.$element.removeClass("ui-selecting"),i.selecting=!1,i.startselected&&(i.$element.addClass("ui-unselecting"),i.unselecting=!0),n._trigger("unselecting",e,{unselecting:i.element}))),i.selected&&(e.metaKey||e.ctrlKey||i.startselected||(i.$element.removeClass("ui-selected"),i.selected=!1,i.$element.addClass("ui-unselecting"),i.unselecting=!0,n._trigger("unselecting",e,{unselecting:i.element})))))}),!1}},_mouseStop:function(e){var i=this;return this.dragged=!1,t(".ui-unselecting",this.element[0]).each(function(){var n=t.data(this,"selectable-item");n.$element.removeClass("ui-unselecting"),n.unselecting=!1,n.startselected=!1,i._trigger("unselected",e,{unselected:n.element})}),t(".ui-selecting",this.element[0]).each(function(){var n=t.data(this,"selectable-item");n.$element.removeClass("ui-selecting").addClass("ui-selected"),n.selecting=!1,n.selected=!0,n.startselected=!0,i._trigger("selected",e,{selected:n.element})}),this._trigger("stop",e),this.helper.remove(),!1}}),t.widget("ui.sortable",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(t,e,i){return t>=e&&t<e+i},_isFloating:function(t){return/left|right/.test(t.css("float"))||/inline|table-cell/.test(t.css("display"))},_create:function(){this.containerCache={},this.element.addClass("ui-sortable"),this.refresh(),this.offset=this.element.offset(),this._mouseInit(),this._setHandleClassName(),this.ready=!0},_setOption:function(t,e){this._super(t,e),"handle"===t&&this._setHandleClassName()},_setHandleClassName:function(){this.element.find(".ui-sortable-handle").removeClass("ui-sortable-handle"),t.each(this.items,function(){(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item).addClass("ui-sortable-handle")})},_destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled").find(".ui-sortable-handle").removeClass("ui-sortable-handle"),this._mouseDestroy();for(var t=this.items.length-1;t>=0;t--)this.items[t].item.removeData(this.widgetName+"-item");return this},_mouseCapture:function(e,i){var n=null,s=!1,o=this;return!(this.reverting||this.options.disabled||"static"===this.options.type||(this._refreshItems(e),t(e.target).parents().each(function(){if(t.data(this,o.widgetName+"-item")===o)return n=t(this),!1}),t.data(e.target,o.widgetName+"-item")===o&&(n=t(e.target)),!n||this.options.handle&&!i&&(t(this.options.handle,n).find("*").addBack().each(function(){this===e.target&&(s=!0)}),!s)||(this.currentItem=n,this._removeCurrentsFromItems(),0)))},_mouseStart:function(e,i,n){var s,o,r=this.options;if(this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(e),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},t.extend(this.offset,{click:{left:e.pageX-this.offset.left,top:e.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(e),this.originalPageX=e.pageX,this.originalPageY=e.pageY,r.cursorAt&&this._adjustOffsetFromHelper(r.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),r.containment&&this._setContainment(),r.cursor&&"auto"!==r.cursor&&(o=this.document.find("body"),this.storedCursor=o.css("cursor"),o.css("cursor",r.cursor),this.storedStylesheet=t("<style>*{ cursor: "+r.cursor+" !important; }</style>").appendTo(o)),r.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",r.opacity)),r.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",r.zIndex)),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",e,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!n)for(s=this.containers.length-1;s>=0;s--)this.containers[s]._trigger("activate",e,this._uiHash(this));return t.ui.ddmanager&&(t.ui.ddmanager.current=this),t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(e),!0},_mouseDrag:function(e){var i,n,s,o,r=this.options,a=!1;for(this.position=this._generatePosition(e),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-e.pageY<r.scrollSensitivity?this.scrollParent[0].scrollTop=a=this.scrollParent[0].scrollTop+r.scrollSpeed:e.pageY-this.overflowOffset.top<r.scrollSensitivity&&(this.scrollParent[0].scrollTop=a=this.scrollParent[0].scrollTop-r.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-e.pageX<r.scrollSensitivity?this.scrollParent[0].scrollLeft=a=this.scrollParent[0].scrollLeft+r.scrollSpeed:e.pageX-this.overflowOffset.left<r.scrollSensitivity&&(this.scrollParent[0].scrollLeft=a=this.scrollParent[0].scrollLeft-r.scrollSpeed)):(e.pageY-this.document.scrollTop()<r.scrollSensitivity?a=this.document.scrollTop(this.document.scrollTop()-r.scrollSpeed):this.window.height()-(e.pageY-this.document.scrollTop())<r.scrollSensitivity&&(a=this.document.scrollTop(this.document.scrollTop()+r.scrollSpeed)),e.pageX-this.document.scrollLeft()<r.scrollSensitivity?a=this.document.scrollLeft(this.document.scrollLeft()-r.scrollSpeed):this.window.width()-(e.pageX-this.document.scrollLeft())<r.scrollSensitivity&&(a=this.document.scrollLeft(this.document.scrollLeft()+r.scrollSpeed))),!1!==a&&t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),i=this.items.length-1;i>=0;i--)if(s=(n=this.items[i]).item[0],(o=this._intersectsWithPointer(n))&&n.instance===this.currentContainer&&!(s===this.currentItem[0]||this.placeholder[1===o?"next":"prev"]()[0]===s||t.contains(this.placeholder[0],s)||"semi-dynamic"===this.options.type&&t.contains(this.element[0],s))){if(this.direction=1===o?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(n))break;this._rearrange(e,n),this._trigger("change",e,this._uiHash());break}return this._contactContainers(e),t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),this._trigger("sort",e,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(e,i){if(e){if(t.ui.ddmanager&&!this.options.dropBehaviour&&t.ui.ddmanager.drop(this,e),this.options.revert){var n=this,s=this.placeholder.offset(),o=this.options.axis,r={};o&&"x"!==o||(r.left=s.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollLeft)),o&&"y"!==o||(r.top=s.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,t(this.helper).animate(r,parseInt(this.options.revert,10)||500,function(){n._clear(e)})}else this._clear(e,i);return!1}},cancel:function(){if(this.dragging){this._mouseUp({target:null}),"original"===this.options.helper?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show();for(var e=this.containers.length-1;e>=0;e--)this.containers[e]._trigger("deactivate",null,this._uiHash(this)),this.containers[e].containerCache.over&&(this.containers[e]._trigger("out",null,this._uiHash(this)),this.containers[e].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),t.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?t(this.domPosition.prev).after(this.currentItem):t(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},t(i).each(function(){var i=(t(e.item||this).attr(e.attribute||"id")||"").match(e.expression||/(.+)[\-=_](.+)/);i&&n.push((e.key||i[1]+"[]")+"="+(e.key&&e.expression?i[1]:i[2]))}),!n.length&&e.key&&n.push(e.key+"="),n.join("&")},toArray:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},i.each(function(){n.push(t(e.item||this).attr(e.attribute||"id")||"")}),n},_intersectsWith:function(t){var e=this.positionAbs.left,i=e+this.helperProportions.width,n=this.positionAbs.top,s=n+this.helperProportions.height,o=t.left,r=o+t.width,a=t.top,l=a+t.height,h=this.offset.click.top,c=this.offset.click.left,u="x"===this.options.axis||n+h>a&&n+h<l,d="y"===this.options.axis||e+c>o&&e+c<r,p=u&&d;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>t[this.floating?"width":"height"]?p:o<e+this.helperProportions.width/2&&i-this.helperProportions.width/2<r&&a<n+this.helperProportions.height/2&&s-this.helperProportions.height/2<l},_intersectsWithPointer:function(t){var e="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height),i="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width),n=e&&i,s=this._getDragVerticalDirection(),o=this._getDragHorizontalDirection();return!!n&&(this.floating?o&&"right"===o||"down"===s?2:1:s&&("down"===s?2:1))},_intersectsWithSides:function(t){var e=this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),i=this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),n=this._getDragVerticalDirection(),s=this._getDragHorizontalDirection();return this.floating&&s?"right"===s&&i||"left"===s&&!i:n&&("down"===n&&e||"up"===n&&!e)},_getDragVerticalDirection:function(){var t=this.positionAbs.top-this.lastPositionAbs.top;return 0!==t&&(t>0?"down":"up")},_getDragHorizontalDirection:function(){var t=this.positionAbs.left-this.lastPositionAbs.left;return 0!==t&&(t>0?"right":"left")},refresh:function(t){return this._refreshItems(t),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var t=this.options;return t.connectWith.constructor===String?[t.connectWith]:t.connectWith},_getItemsAsjQuery:function(e){var i,n,s,o,r=[],a=[],l=this._connectWith();if(l&&e)for(i=l.length-1;i>=0;i--)for(n=(s=t(l[i],this.document[0])).length-1;n>=0;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&a.push([t.isFunction(o.options.items)?o.options.items.call(o.element):t(o.options.items,o.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),o]);function h(){r.push(this)}for(a.push([t.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):t(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),i=a.length-1;i>=0;i--)a[i][0].each(h);return t(r)},_removeCurrentsFromItems:function(){var e=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=t.grep(this.items,function(t){for(var i=0;i<e.length;i++)if(e[i]===t.item[0])return!1;return!0})},_refreshItems:function(e){this.items=[],this.containers=[this];var i,n,s,o,r,a,l,h,c=this.items,u=[[t.isFunction(this.options.items)?this.options.items.call(this.element[0],e,{item:this.currentItem}):t(this.options.items,this.element),this]],d=this._connectWith();if(d&&this.ready)for(i=d.length-1;i>=0;i--)for(n=(s=t(d[i],this.document[0])).length-1;n>=0;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&(u.push([t.isFunction(o.options.items)?o.options.items.call(o.element[0],e,{item:this.currentItem}):t(o.options.items,o.element),o]),this.containers.push(o));for(i=u.length-1;i>=0;i--)for(r=u[i][1],n=0,h=(a=u[i][0]).length;n<h;n++)(l=t(a[n])).data(this.widgetName+"-item",r),c.push({item:l,instance:r,width:0,height:0,left:0,top:0})},refreshPositions:function(e){var i,n,s,o;for(this.floating=!!this.items.length&&("x"===this.options.axis||this._isFloating(this.items[0].item)),this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset()),i=this.items.length-1;i>=0;i--)(n=this.items[i]).instance!==this.currentContainer&&this.currentContainer&&n.item[0]!==this.currentItem[0]||(s=this.options.toleranceElement?t(this.options.toleranceElement,n.item):n.item,e||(n.width=s.outerWidth(),n.height=s.outerHeight()),o=s.offset(),n.left=o.left,n.top=o.top);if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(i=this.containers.length-1;i>=0;i--)o=this.containers[i].element.offset(),this.containers[i].containerCache.left=o.left,this.containers[i].containerCache.top=o.top,this.containers[i].containerCache.width=this.containers[i].element.outerWidth(),this.containers[i].containerCache.height=this.containers[i].element.outerHeight();return this},_createPlaceholder:function(e){var i,n=(e=e||this).options;n.placeholder&&n.placeholder.constructor!==String||(i=n.placeholder,n.placeholder={element:function(){var n=e.currentItem[0].nodeName.toLowerCase(),s=t("<"+n+">",e.document[0]).addClass(i||e.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper");return"tbody"===n?e._createTrPlaceholder(e.currentItem.find("tr").eq(0),t("<tr>",e.document[0]).appendTo(s)):"tr"===n?e._createTrPlaceholder(e.currentItem,s):"img"===n&&s.attr("src",e.currentItem.attr("src")),i||s.css("visibility","hidden"),s},update:function(t,s){i&&!n.forcePlaceholderSize||(s.height()||s.height(e.currentItem.innerHeight()-parseInt(e.currentItem.css("paddingTop")||0,10)-parseInt(e.currentItem.css("paddingBottom")||0,10)),s.width()||s.width(e.currentItem.innerWidth()-parseInt(e.currentItem.css("paddingLeft")||0,10)-parseInt(e.currentItem.css("paddingRight")||0,10)))}}),e.placeholder=t(n.placeholder.element.call(e.element,e.currentItem)),e.currentItem.after(e.placeholder),n.placeholder.update(e,e.placeholder)},_createTrPlaceholder:function(e,i){var n=this;e.children().each(function(){t("<td>&#160;</td>",n.document[0]).attr("colspan",t(this).attr("colspan")||1).appendTo(i)})},_contactContainers:function(e){var i,n,s,o,r,a,l,h,c,u,d=null,p=null;for(i=this.containers.length-1;i>=0;i--)if(!t.contains(this.currentItem[0],this.containers[i].element[0]))if(this._intersectsWith(this.containers[i].containerCache)){if(d&&t.contains(this.containers[i].element[0],d.element[0]))continue;d=this.containers[i],p=i}else this.containers[i].containerCache.over&&(this.containers[i]._trigger("out",e,this._uiHash(this)),this.containers[i].containerCache.over=0);if(d)if(1===this.containers.length)this.containers[p].containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1);else{for(s=1e4,o=null,r=(c=d.floating||this._isFloating(this.currentItem))?"left":"top",a=c?"width":"height",u=c?"clientX":"clientY",n=this.items.length-1;n>=0;n--)t.contains(this.containers[p].element[0],this.items[n].item[0])&&this.items[n].item[0]!==this.currentItem[0]&&(l=this.items[n].item.offset()[r],h=!1,e[u]-l>this.items[n][a]/2&&(h=!0),Math.abs(e[u]-l)<s&&(s=Math.abs(e[u]-l),o=this.items[n],this.direction=h?"up":"down"));if(!o&&!this.options.dropOnEmpty)return;if(this.currentContainer===this.containers[p])return void(this.currentContainer.containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash()),this.currentContainer.containerCache.over=1));o?this._rearrange(e,o,null,!0):this._rearrange(e,null,this.containers[p].element,!0),this._trigger("change",e,this._uiHash()),this.containers[p]._trigger("change",e,this._uiHash(this)),this.currentContainer=this.containers[p],this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1}},_createHelper:function(e){var i=this.options,n=t.isFunction(i.helper)?t(i.helper.apply(this.element[0],[e,this.currentItem])):"clone"===i.helper?this.currentItem.clone():this.currentItem;return n.parents("body").length||t("parent"!==i.appendTo?i.appendTo:this.currentItem[0].parentNode)[0].appendChild(n[0]),n[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),n[0].style.width&&!i.forceHelperSize||n.width(this.currentItem.width()),n[0].style.height&&!i.forceHelperSize||n.height(this.currentItem.height()),n},_adjustOffsetFromHelper:function(e){"string"==typeof e&&(e=e.split(" ")),t.isArray(e)&&(e={left:+e[0],top:+e[1]||0}),"left"in e&&(this.offset.click.left=e.left+this.margins.left),"right"in e&&(this.offset.click.left=this.helperProportions.width-e.right+this.margins.left),"top"in e&&(this.offset.click.top=e.top+this.margins.top),"bottom"in e&&(this.offset.click.top=this.helperProportions.height-e.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var e=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),(this.offsetParent[0]===this.document[0].body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&t.ui.ie)&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"===this.cssPosition){var t=this.currentItem.position();return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,s=this.options;"parent"===s.containment&&(s.containment=this.helper[0].parentNode),"document"!==s.containment&&"window"!==s.containment||(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,"document"===s.containment?this.document.width():this.window.width()-this.helperProportions.width-this.margins.left,("document"===s.containment?this.document.width():this.window.height()||this.document[0].body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(s.containment)||(e=t(s.containment)[0],i=t(s.containment).offset(),n="hidden"!==t(e).css("overflow"),this.containment=[i.left+(parseInt(t(e).css("borderLeftWidth"),10)||0)+(parseInt(t(e).css("paddingLeft"),10)||0)-this.margins.left,i.top+(parseInt(t(e).css("borderTopWidth"),10)||0)+(parseInt(t(e).css("paddingTop"),10)||0)-this.margins.top,i.left+(n?Math.max(e.scrollWidth,e.offsetWidth):e.offsetWidth)-(parseInt(t(e).css("borderLeftWidth"),10)||0)-(parseInt(t(e).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,i.top+(n?Math.max(e.scrollHeight,e.offsetHeight):e.offsetHeight)-(parseInt(t(e).css("borderTopWidth"),10)||0)-(parseInt(t(e).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(e,i){i||(i=this.position);var n="absolute"===e?1:-1,s="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,o=/(html|body)/i.test(s[0].tagName);return{top:i.top+this.offset.relative.top*n+this.offset.parent.top*n-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():o?0:s.scrollTop())*n,left:i.left+this.offset.relative.left*n+this.offset.parent.left*n-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():o?0:s.scrollLeft())*n}},_generatePosition:function(e){var i,n,s=this.options,o=e.pageX,r=e.pageY,a="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,l=/(html|body)/i.test(a[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(e.pageX-this.offset.click.left<this.containment[0]&&(o=this.containment[0]+this.offset.click.left),e.pageY-this.offset.click.top<this.containment[1]&&(r=this.containment[1]+this.offset.click.top),e.pageX-this.offset.click.left>this.containment[2]&&(o=this.containment[2]+this.offset.click.left),e.pageY-this.offset.click.top>this.containment[3]&&(r=this.containment[3]+this.offset.click.top)),s.grid&&(i=this.originalPageY+Math.round((r-this.originalPageY)/s.grid[1])*s.grid[1],r=this.containment?i-this.offset.click.top>=this.containment[1]&&i-this.offset.click.top<=this.containment[3]?i:i-this.offset.click.top>=this.containment[1]?i-s.grid[1]:i+s.grid[1]:i,n=this.originalPageX+Math.round((o-this.originalPageX)/s.grid[0])*s.grid[0],o=this.containment?n-this.offset.click.left>=this.containment[0]&&n-this.offset.click.left<=this.containment[2]?n:n-this.offset.click.left>=this.containment[0]?n-s.grid[0]:n+s.grid[0]:n)),{top:r-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():l?0:a.scrollTop()),left:o-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():l?0:a.scrollLeft())}},_rearrange:function(t,e,i,n){i?i[0].appendChild(this.placeholder[0]):e.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?e.item[0]:e.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var s=this.counter;this._delay(function(){s===this.counter&&this.refreshPositions(!n)})},_clear:function(t,e){this.reverting=!1;var i,n=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(i in this._storedCSS)"auto"!==this._storedCSS[i]&&"static"!==this._storedCSS[i]||(this._storedCSS[i]="");this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();function s(t,e,i){return function(n){i._trigger(t,n,e._uiHash(e))}}for(this.fromOutside&&!e&&n.push(function(t){this._trigger("receive",t,this._uiHash(this.fromOutside))}),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||e||n.push(function(t){this._trigger("update",t,this._uiHash())}),this!==this.currentContainer&&(e||(n.push(function(t){this._trigger("remove",t,this._uiHash())}),n.push(function(t){return function(e){t._trigger("receive",e,this._uiHash(this))}}.call(this,this.currentContainer)),n.push(function(t){return function(e){t._trigger("update",e,this._uiHash(this))}}.call(this,this.currentContainer)))),i=this.containers.length-1;i>=0;i--)e||n.push(s("deactivate",this,this.containers[i])),this.containers[i].containerCache.over&&(n.push(s("out",this,this.containers[i])),this.containers[i].containerCache.over=0);if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,e||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!e){for(i=0;i<n.length;i++)n[i].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){!1===t.Widget.prototype._trigger.apply(this,arguments)&&this.cancel()},_uiHash:function(e){var i=e||this;return{helper:i.helper,placeholder:i.placeholder||t([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:e?e.element:null}}}),t.widget("ui.accordion",{version:"1.11.4",options:{active:0,animate:{},collapsible:!1,event:"click",header:"> li > :first-child,> :not(li):even",heightStyle:"auto",icons:{activeHeader:"ui-icon-triangle-1-s",header:"ui-icon-triangle-1-e"},activate:null,beforeActivate:null},hideProps:{borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},showProps:{borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},_create:function(){var e=this.options;this.prevShow=this.prevHide=t(),this.element.addClass("ui-accordion ui-widget ui-helper-reset").attr("role","tablist"),e.collapsible||!1!==e.active&&null!=e.active||(e.active=0),this._processPanels(),e.active<0&&(e.active+=this.headers.length),this._refresh()},_getCreateEventData:function(){return{header:this.active,panel:this.active.length?this.active.next():t()}},_createIcons:function(){var e=this.options.icons;e&&(t("<span>").addClass("ui-accordion-header-icon ui-icon "+e.header).prependTo(this.headers),this.active.children(".ui-accordion-header-icon").removeClass(e.header).addClass(e.activeHeader),this.headers.addClass("ui-accordion-icons"))},_destroyIcons:function(){this.headers.removeClass("ui-accordion-icons").children(".ui-accordion-header-icon").remove()},_destroy:function(){var t;this.element.removeClass("ui-accordion ui-widget ui-helper-reset").removeAttr("role"),this.headers.removeClass("ui-accordion-header ui-accordion-header-active ui-state-default ui-corner-all ui-state-active ui-state-disabled ui-corner-top").removeAttr("role").removeAttr("aria-expanded").removeAttr("aria-selected").removeAttr("aria-controls").removeAttr("tabIndex").removeUniqueId(),this._destroyIcons(),t=this.headers.next().removeClass("ui-helper-reset ui-widget-content ui-corner-bottom ui-accordion-content ui-accordion-content-active ui-state-disabled").css("display","").removeAttr("role").removeAttr("aria-hidden").removeAttr("aria-labelledby").removeUniqueId(),"content"!==this.options.heightStyle&&t.css("height","")},_setOption:function(t,e){"active"!==t?("event"===t&&(this.options.event&&this._off(this.headers,this.options.event),this._setupEvents(e)),this._super(t,e),"collapsible"!==t||e||!1!==this.options.active||this._activate(0),"icons"===t&&(this._destroyIcons(),e&&this._createIcons()),"disabled"===t&&(this.element.toggleClass("ui-state-disabled",!!e).attr("aria-disabled",e),this.headers.add(this.headers.next()).toggleClass("ui-state-disabled",!!e))):this._activate(e)},_keydown:function(e){if(!e.altKey&&!e.ctrlKey){var i=t.ui.keyCode,n=this.headers.length,s=this.headers.index(e.target),o=!1;switch(e.keyCode){case i.RIGHT:case i.DOWN:o=this.headers[(s+1)%n];break;case i.LEFT:case i.UP:o=this.headers[(s-1+n)%n];break;case i.SPACE:case i.ENTER:this._eventHandler(e);break;case i.HOME:o=this.headers[0];break;case i.END:o=this.headers[n-1]}o&&(t(e.target).attr("tabIndex",-1),t(o).attr("tabIndex",0),o.focus(),e.preventDefault())}},_panelKeyDown:function(e){e.keyCode===t.ui.keyCode.UP&&e.ctrlKey&&t(e.currentTarget).prev().focus()},refresh:function(){var e=this.options;this._processPanels(),!1===e.active&&!0===e.collapsible||!this.headers.length?(e.active=!1,this.active=t()):!1===e.active?this._activate(0):this.active.length&&!t.contains(this.element[0],this.active[0])?this.headers.length===this.headers.find(".ui-state-disabled").length?(e.active=!1,this.active=t()):this._activate(Math.max(0,e.active-1)):e.active=this.headers.index(this.active),this._destroyIcons(),this._refresh()},_processPanels:function(){var t=this.headers,e=this.panels;this.headers=this.element.find(this.options.header).addClass("ui-accordion-header ui-state-default ui-corner-all"),this.panels=this.headers.next().addClass("ui-accordion-content ui-helper-reset ui-widget-content ui-corner-bottom").filter(":not(.ui-accordion-content-active)").hide(),e&&(this._off(t.not(this.headers)),this._off(e.not(this.panels)))},_refresh:function(){var e,i=this.options,n=i.heightStyle,s=this.element.parent();this.active=this._findActive(i.active).addClass("ui-accordion-header-active ui-state-active ui-corner-top").removeClass("ui-corner-all"),this.active.next().addClass("ui-accordion-content-active").show(),this.headers.attr("role","tab").each(function(){var e=t(this),i=e.uniqueId().attr("id"),n=e.next(),s=n.uniqueId().attr("id");e.attr("aria-controls",s),n.attr("aria-labelledby",i)}).next().attr("role","tabpanel"),this.headers.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}).next().attr({"aria-hidden":"true"}).hide(),this.active.length?this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}).next().attr({"aria-hidden":"false"}):this.headers.eq(0).attr("tabIndex",0),this._createIcons(),this._setupEvents(i.event),"fill"===n?(e=s.height(),this.element.siblings(":visible").each(function(){var i=t(this),n=i.css("position");"absolute"!==n&&"fixed"!==n&&(e-=i.outerHeight(!0))}),this.headers.each(function(){e-=t(this).outerHeight(!0)}),this.headers.next().each(function(){t(this).height(Math.max(0,e-t(this).innerHeight()+t(this).height()))}).css("overflow","auto")):"auto"===n&&(e=0,this.headers.next().each(function(){e=Math.max(e,t(this).css("height","").height())}).height(e))},_activate:function(e){var i=this._findActive(e)[0];i!==this.active[0]&&(i=i||this.active[0],this._eventHandler({target:i,currentTarget:i,preventDefault:t.noop}))},_findActive:function(e){return"number"==typeof e?this.headers.eq(e):t()},_setupEvents:function(e){var i={keydown:"_keydown"};e&&t.each(e.split(" "),function(t,e){i[e]="_eventHandler"}),this._off(this.headers.add(this.headers.next())),this._on(this.headers,i),this._on(this.headers.next(),{keydown:"_panelKeyDown"}),this._hoverable(this.headers),this._focusable(this.headers)},_eventHandler:function(e){var i=this.options,n=this.active,s=t(e.currentTarget),o=s[0]===n[0],r=o&&i.collapsible,a=r?t():s.next(),l={oldHeader:n,oldPanel:n.next(),newHeader:r?t():s,newPanel:a};e.preventDefault(),o&&!i.collapsible||!1===this._trigger("beforeActivate",e,l)||(i.active=!r&&this.headers.index(s),this.active=o?t():s,this._toggle(l),n.removeClass("ui-accordion-header-active ui-state-active"),i.icons&&n.children(".ui-accordion-header-icon").removeClass(i.icons.activeHeader).addClass(i.icons.header),o||(s.removeClass("ui-corner-all").addClass("ui-accordion-header-active ui-state-active ui-corner-top"),i.icons&&s.children(".ui-accordion-header-icon").removeClass(i.icons.header).addClass(i.icons.activeHeader),s.next().addClass("ui-accordion-content-active")))},_toggle:function(e){var i=e.newPanel,n=this.prevShow.length?this.prevShow:e.oldPanel;this.prevShow.add(this.prevHide).stop(!0,!0),this.prevShow=i,this.prevHide=n,this.options.animate?this._animate(i,n,e):(n.hide(),i.show(),this._toggleComplete(e)),n.attr({"aria-hidden":"true"}),n.prev().attr({"aria-selected":"false","aria-expanded":"false"}),i.length&&n.length?n.prev().attr({tabIndex:-1,"aria-expanded":"false"}):i.length&&this.headers.filter(function(){return 0===parseInt(t(this).attr("tabIndex"),10)}).attr("tabIndex",-1),i.attr("aria-hidden","false").prev().attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_animate:function(t,e,i){var n,s,o,r=this,a=0,l=t.css("box-sizing"),h=t.length&&(!e.length||t.index()<e.index()),c=this.options.animate||{},u=h&&c.down||c,d=function(){r._toggleComplete(i)};return"number"==typeof u&&(o=u),"string"==typeof u&&(s=u),s=s||u.easing||c.easing,o=o||u.duration||c.duration,e.length?t.length?(n=t.show().outerHeight(),e.animate(this.hideProps,{duration:o,easing:s,step:function(t,e){e.now=Math.round(t)}}),void t.hide().animate(this.showProps,{duration:o,easing:s,complete:d,step:function(t,i){i.now=Math.round(t),"height"!==i.prop?"content-box"===l&&(a+=i.now):"content"!==r.options.heightStyle&&(i.now=Math.round(n-e.outerHeight()-a),a=0)}})):e.animate(this.hideProps,o,s,d):t.animate(this.showProps,o,s,d)},_toggleComplete:function(t){var e=t.oldPanel;e.removeClass("ui-accordion-content-active").prev().removeClass("ui-corner-top").addClass("ui-corner-all"),e.length&&(e.parent()[0].className=e.parent()[0].className),this._trigger("activate",null,t)}}),t.widget("ui.menu",{version:"1.11.4",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-carat-1-e"},items:"> *",menus:"ul",position:{my:"left-1 top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.element.uniqueId().addClass("ui-menu ui-widget ui-widget-content").toggleClass("ui-menu-icons",!!this.element.find(".ui-icon").length).attr({role:this.options.role,tabIndex:0}),this.options.disabled&&this.element.addClass("ui-state-disabled").attr("aria-disabled","true"),this._on({"mousedown .ui-menu-item":function(t){t.preventDefault()},"click .ui-menu-item":function(e){var i=t(e.target);!this.mouseHandled&&i.not(".ui-state-disabled").length&&(this.select(e),e.isPropagationStopped()||(this.mouseHandled=!0),i.has(".ui-menu").length?this.expand(e):!this.element.is(":focus")&&t(this.document[0].activeElement).closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer)))},"mouseenter .ui-menu-item":function(e){if(!this.previousFilter){var i=t(e.currentTarget);i.siblings(".ui-state-active").removeClass("ui-state-active"),this.focus(e,i)}},mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(t,e){var i=this.active||this.element.find(this.options.items).eq(0);e||this.focus(t,i)},blur:function(e){this._delay(function(){t.contains(this.element[0],this.document[0].activeElement)||this.collapseAll(e)})},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(t){this._closeOnDocumentClick(t)&&this.collapseAll(t),this.mouseHandled=!1}})},_destroy:function(){this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeClass("ui-menu ui-widget ui-widget-content ui-menu-icons ui-front").removeAttr("role").removeAttr("tabIndex").removeAttr("aria-labelledby").removeAttr("aria-expanded").removeAttr("aria-hidden").removeAttr("aria-disabled").removeUniqueId().show(),this.element.find(".ui-menu-item").removeClass("ui-menu-item").removeAttr("role").removeAttr("aria-disabled").removeUniqueId().removeClass("ui-state-hover").removeAttr("tabIndex").removeAttr("role").removeAttr("aria-haspopup").children().each(function(){var e=t(this);e.data("ui-menu-submenu-carat")&&e.remove()}),this.element.find(".ui-menu-divider").removeClass("ui-menu-divider ui-widget-content")},_keydown:function(e){var i,n,s,o,r=!0;switch(e.keyCode){case t.ui.keyCode.PAGE_UP:this.previousPage(e);break;case t.ui.keyCode.PAGE_DOWN:this.nextPage(e);break;case t.ui.keyCode.HOME:this._move("first","first",e);break;case t.ui.keyCode.END:this._move("last","last",e);break;case t.ui.keyCode.UP:this.previous(e);break;case t.ui.keyCode.DOWN:this.next(e);break;case t.ui.keyCode.LEFT:this.collapse(e);break;case t.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(e);break;case t.ui.keyCode.ENTER:case t.ui.keyCode.SPACE:this._activate(e);break;case t.ui.keyCode.ESCAPE:this.collapse(e);break;default:r=!1,n=this.previousFilter||"",s=String.fromCharCode(e.keyCode),o=!1,clearTimeout(this.filterTimer),s===n?o=!0:s=n+s,i=this._filterMenuItems(s),(i=o&&-1!==i.index(this.active.next())?this.active.nextAll(".ui-menu-item"):i).length||(s=String.fromCharCode(e.keyCode),i=this._filterMenuItems(s)),i.length?(this.focus(e,i),this.previousFilter=s,this.filterTimer=this._delay(function(){delete this.previousFilter},1e3)):delete this.previousFilter}r&&e.preventDefault()},_activate:function(t){this.active.is(".ui-state-disabled")||(this.active.is("[aria-haspopup='true']")?this.expand(t):this.select(t))},refresh:function(){var e,i=this,n=this.options.icons.submenu,s=this.element.find(this.options.menus);this.element.toggleClass("ui-menu-icons",!!this.element.find(".ui-icon").length),s.filter(":not(.ui-menu)").addClass("ui-menu ui-widget ui-widget-content ui-front").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each(function(){var e=t(this),i=e.parent(),s=t("<span>").addClass("ui-menu-icon ui-icon "+n).data("ui-menu-submenu-carat",!0);i.attr("aria-haspopup","true").prepend(s),e.attr("aria-labelledby",i.attr("id"))}),(e=s.add(this.element).find(this.options.items)).not(".ui-menu-item").each(function(){var e=t(this);i._isDivider(e)&&e.addClass("ui-widget-content ui-menu-divider")}),e.not(".ui-menu-item, .ui-menu-divider").addClass("ui-menu-item").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),e.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!t.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(t,e){"icons"===t&&this.element.find(".ui-menu-icon").removeClass(this.options.icons.submenu).addClass(e.submenu),"disabled"===t&&this.element.toggleClass("ui-state-disabled",!!e).attr("aria-disabled",e),this._super(t,e)},focus:function(t,e){var i,n;this.blur(t,t&&"focus"===t.type),this._scrollIntoView(e),this.active=e.first(),n=this.active.addClass("ui-state-focus").removeClass("ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",n.attr("id")),this.active.parent().closest(".ui-menu-item").addClass("ui-state-active"),t&&"keydown"===t.type?this._close():this.timer=this._delay(function(){this._close()},this.delay),(i=e.children(".ui-menu")).length&&t&&/^mouse/.test(t.type)&&this._startOpening(i),this.activeMenu=e.parent(),this._trigger("focus",t,{item:e})},_scrollIntoView:function(e){var i,n,s,o,r,a;this._hasScroll()&&(i=parseFloat(t.css(this.activeMenu[0],"borderTopWidth"))||0,n=parseFloat(t.css(this.activeMenu[0],"paddingTop"))||0,s=e.offset().top-this.activeMenu.offset().top-i-n,o=this.activeMenu.scrollTop(),r=this.activeMenu.height(),a=e.outerHeight(),s<0?this.activeMenu.scrollTop(o+s):s+a>r&&this.activeMenu.scrollTop(o+s-r+a))},blur:function(t,e){e||clearTimeout(this.timer),this.active&&(this.active.removeClass("ui-state-focus"),this.active=null,this._trigger("blur",t,{item:this.active}))},_startOpening:function(t){clearTimeout(this.timer),"true"===t.attr("aria-hidden")&&(this.timer=this._delay(function(){this._close(),this._open(t)},this.delay))},_open:function(e){var i=t.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(e.parents(".ui-menu")).hide().attr("aria-hidden","true"),e.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(i)},collapseAll:function(e,i){clearTimeout(this.timer),this.timer=this._delay(function(){var n=i?this.element:t(e&&e.target).closest(this.element.find(".ui-menu"));n.length||(n=this.element),this._close(n),this.blur(e),this.activeMenu=n},this.delay)},_close:function(t){t||(t=this.active?this.active.parent():this.element),t.find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false").end().find(".ui-state-active").not(".ui-state-focus").removeClass("ui-state-active")},_closeOnDocumentClick:function(e){return!t(e.target).closest(".ui-menu").length},_isDivider:function(t){return!/[^\-\u2014\u2013\s]/.test(t.text())},collapse:function(t){var e=this.active&&this.active.parent().closest(".ui-menu-item",this.element);e&&e.length&&(this._close(),this.focus(t,e))},expand:function(t){var e=this.active&&this.active.children(".ui-menu ").find(this.options.items).first();e&&e.length&&(this._open(e.parent()),this._delay(function(){this.focus(t,e)}))},next:function(t){this._move("next","first",t)},previous:function(t){this._move("prev","last",t)},isFirstItem:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_move:function(t,e,i){var n;this.active&&(n="first"===t||"last"===t?this.active["first"===t?"prevAll":"nextAll"](".ui-menu-item").eq(-1):this.active[t+"All"](".ui-menu-item").eq(0)),n&&n.length&&this.active||(n=this.activeMenu.find(this.options.items)[e]()),this.focus(i,n)},nextPage:function(e){var i,n,s;this.active?this.isLastItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.height(),this.active.nextAll(".ui-menu-item").each(function(){return(i=t(this)).offset().top-n-s<0}),this.focus(e,i)):this.focus(e,this.activeMenu.find(this.options.items)[this.active?"last":"first"]())):this.next(e)},previousPage:function(e){var i,n,s;this.active?this.isFirstItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.height(),this.active.prevAll(".ui-menu-item").each(function(){return(i=t(this)).offset().top-n+s>0}),this.focus(e,i)):this.focus(e,this.activeMenu.find(this.options.items).first())):this.next(e)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(e){this.active=this.active||t(e.target).closest(".ui-menu-item");var i={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(e,!0),this._trigger("select",e,i)},_filterMenuItems:function(e){var i=e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"),n=new RegExp("^"+i,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter(function(){return n.test(t.trim(t(this).text()))})}}),t.widget("ui.autocomplete",{version:"1.11.4",defaultElement:"<input>",options:{appendTo:null,autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null,change:null,close:null,focus:null,open:null,response:null,search:null,select:null},requestIndex:0,pending:0,_create:function(){var e,i,n,s=this.element[0].nodeName.toLowerCase(),o="textarea"===s,r="input"===s;this.isMultiLine=!!o||!r&&this.element.prop("isContentEditable"),this.valueMethod=this.element[o||r?"val":"text"],this.isNewMenu=!0,this.element.addClass("ui-autocomplete-input").attr("autocomplete","off"),this._on(this.element,{keydown:function(s){if(this.element.prop("readOnly"))return e=!0,n=!0,void(i=!0);e=!1,n=!1,i=!1;var o=t.ui.keyCode;switch(s.keyCode){case o.PAGE_UP:e=!0,this._move("previousPage",s);break;case o.PAGE_DOWN:e=!0,this._move("nextPage",s);break;case o.UP:e=!0,this._keyEvent("previous",s);break;case o.DOWN:e=!0,this._keyEvent("next",s);break;case o.ENTER:this.menu.active&&(e=!0,s.preventDefault(),this.menu.select(s));break;case o.TAB:this.menu.active&&this.menu.select(s);break;case o.ESCAPE:this.menu.element.is(":visible")&&(this.isMultiLine||this._value(this.term),this.close(s),s.preventDefault());break;default:i=!0,this._searchTimeout(s)}},keypress:function(n){if(e)return e=!1,void(this.isMultiLine&&!this.menu.element.is(":visible")||n.preventDefault());if(!i){var s=t.ui.keyCode;switch(n.keyCode){case s.PAGE_UP:this._move("previousPage",n);break;case s.PAGE_DOWN:this._move("nextPage",n);break;case s.UP:this._keyEvent("previous",n);break;case s.DOWN:this._keyEvent("next",n)}}},input:function(t){if(n)return n=!1,void t.preventDefault();this._searchTimeout(t)},focus:function(){this.selectedItem=null,this.previous=this._value()},blur:function(t){this.cancelBlur?delete this.cancelBlur:(clearTimeout(this.searching),this.close(t),this._change(t))}}),this._initSource(),this.menu=t("<ul>").addClass("ui-autocomplete ui-front").appendTo(this._appendTo()).menu({role:null}).hide().menu("instance"),this._on(this.menu.element,{mousedown:function(e){e.preventDefault(),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur});var i=this.menu.element[0];t(e.target).closest(".ui-menu-item").length||this._delay(function(){var e=this;this.document.one("mousedown",function(n){n.target===e.element[0]||n.target===i||t.contains(i,n.target)||e.close()})})},menufocus:function(e,i){var n,s;if(this.isNewMenu&&(this.isNewMenu=!1,e.originalEvent&&/^mouse/.test(e.originalEvent.type)))return this.menu.blur(),void this.document.one("mousemove",function(){t(e.target).trigger(e.originalEvent)});s=i.item.data("ui-autocomplete-item"),!1!==this._trigger("focus",e,{item:s})&&e.originalEvent&&/^key/.test(e.originalEvent.type)&&this._value(s.value),(n=i.item.attr("aria-label")||s.value)&&t.trim(n).length&&(this.liveRegion.children().hide(),t("<div>").text(n).appendTo(this.liveRegion))},menuselect:function(t,e){var i=e.item.data("ui-autocomplete-item"),n=this.previous;this.element[0]!==this.document[0].activeElement&&(this.element.focus(),this.previous=n,this._delay(function(){this.previous=n,this.selectedItem=i})),!1!==this._trigger("select",t,{item:i})&&this._value(i.value),this.term=this._value(),this.close(t),this.selectedItem=i}}),this.liveRegion=t("<span>",{role:"status","aria-live":"assertive","aria-relevant":"additions"}).addClass("ui-helper-hidden-accessible").appendTo(this.document[0].body),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_destroy:function(){clearTimeout(this.searching),this.element.removeClass("ui-autocomplete-input").removeAttr("autocomplete"),this.menu.element.remove(),this.liveRegion.remove()},_setOption:function(t,e){this._super(t,e),"source"===t&&this._initSource(),"appendTo"===t&&this.menu.element.appendTo(this._appendTo()),"disabled"===t&&e&&this.xhr&&this.xhr.abort()},_appendTo:function(){var e=this.options.appendTo;return e&&(e=e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)),e&&e[0]||(e=this.element.closest(".ui-front")),e.length||(e=this.document[0].body),e},_initSource:function(){var e,i,n=this;t.isArray(this.options.source)?(e=this.options.source,this.source=function(i,n){n(t.ui.autocomplete.filter(e,i.term))}):"string"==typeof this.options.source?(i=this.options.source,this.source=function(e,s){n.xhr&&n.xhr.abort(),n.xhr=t.ajax({url:i,data:e,dataType:"json",success:function(t){s(t)},error:function(){s([])}})}):this.source=this.options.source},_searchTimeout:function(t){clearTimeout(this.searching),this.searching=this._delay(function(){var e=this.term===this._value(),i=this.menu.element.is(":visible"),n=t.altKey||t.ctrlKey||t.metaKey||t.shiftKey;e&&(!e||i||n)||(this.selectedItem=null,this.search(null,t))},this.options.delay)},search:function(t,e){return t=null!=t?t:this._value(),this.term=this._value(),t.length<this.options.minLength?this.close(e):!1!==this._trigger("search",e)?this._search(t):void 0},_search:function(t){this.pending++,this.element.addClass("ui-autocomplete-loading"),this.cancelSearch=!1,this.source({term:t},this._response())},_response:function(){var e=++this.requestIndex;return t.proxy(function(t){e===this.requestIndex&&this.__response(t),this.pending--,this.pending||this.element.removeClass("ui-autocomplete-loading")},this)},__response:function(t){t&&(t=this._normalize(t)),this._trigger("response",null,{content:t}),!this.options.disabled&&t&&t.length&&!this.cancelSearch?(this._suggest(t),this._trigger("open")):this._close()},close:function(t){this.cancelSearch=!0,this._close(t)},_close:function(t){this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.blur(),this.isNewMenu=!0,this._trigger("close",t))},_change:function(t){this.previous!==this._value()&&this._trigger("change",t,{item:this.selectedItem})},_normalize:function(e){return e.length&&e[0].label&&e[0].value?e:t.map(e,function(e){return"string"==typeof e?{label:e,value:e}:t.extend({},e,{label:e.label||e.value,value:e.value||e.label})})},_suggest:function(e){var i=this.menu.element.empty();this._renderMenu(i,e),this.isNewMenu=!0,this.menu.refresh(),i.show(),this._resizeMenu(),i.position(t.extend({of:this.element},this.options.position)),this.options.autoFocus&&this.menu.next()},_resizeMenu:function(){var t=this.menu.element;t.outerWidth(Math.max(t.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(e,i){var n=this;t.each(i,function(t,i){n._renderItemData(e,i)})},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-autocomplete-item",e)},_renderItem:function(e,i){return t("<li>").text(i.label).appendTo(e)},_move:function(t,e){if(this.menu.element.is(":visible"))return this.menu.isFirstItem()&&/^previous/.test(t)||this.menu.isLastItem()&&/^next/.test(t)?(this.isMultiLine||this._value(this.term),void this.menu.blur()):void this.menu[t](e);this.search(null,e)},widget:function(){return this.menu.element},_value:function(){return this.valueMethod.apply(this.element,arguments)},_keyEvent:function(t,e){this.isMultiLine&&!this.menu.element.is(":visible")||(this._move(t,e),e.preventDefault())}}),t.extend(t.ui.autocomplete,{escapeRegex:function(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")},filter:function(e,i){var n=new RegExp(t.ui.autocomplete.escapeRegex(i),"i");return t.grep(e,function(t){return n.test(t.label||t.value||t)})}}),t.widget("ui.autocomplete",t.ui.autocomplete,{options:{messages:{noResults:"No search results.",results:function(t){return t+(t>1?" results are":" result is")+" available, use up and down arrow keys to navigate."}}},__response:function(e){var i;this._superApply(arguments),this.options.disabled||this.cancelSearch||(i=e&&e.length?this.options.messages.results(e.length):this.options.messages.noResults,this.liveRegion.children().hide(),t("<div>").text(i).appendTo(this.liveRegion))}}),t.ui.autocomplete;var u,d,p="ui-button ui-widget ui-state-default ui-corner-all",f="ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only",m=function(){var e=t(this);setTimeout(function(){e.find(":ui-button").button("refresh")},1)},g=function(e){var i=e.name,n=e.form,s=t([]);return i&&(i=i.replace(/'/g,"\\'"),s=n?t(n).find("[name='"+i+"'][type=radio]"):t("[name='"+i+"'][type=radio]",e.ownerDocument).filter(function(){return!this.form})),s};function v(){this._curInst=null,this._keyEvent=!1,this._disabledInputs=[],this._datepickerShowing=!1,this._inDialog=!1,this._mainDivId="ui-datepicker-div",this._inlineClass="ui-datepicker-inline",this._appendClass="ui-datepicker-append",this._triggerClass="ui-datepicker-trigger",this._dialogClass="ui-datepicker-dialog",this._disableClass="ui-datepicker-disabled",this._unselectableClass="ui-datepicker-unselectable",this._currentClass="ui-datepicker-current-day",this._dayOverClass="ui-datepicker-days-cell-over",this.regional=[],this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""},this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1},t.extend(this._defaults,this.regional[""]),this.regional.en=t.extend(!0,{},this.regional[""]),this.regional["en-US"]=t.extend(!0,{},this.regional.en),this.dpDiv=b(t("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>"))}function b(e){var i="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return e.delegate(i,"mouseout",function(){t(this).removeClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).removeClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).removeClass("ui-datepicker-next-hover")}).delegate(i,"mouseover",y)}function y(){t.datepicker._isDisabledDatepicker(d.inline?d.dpDiv.parent()[0]:d.input[0])||(t(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),t(this).addClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).addClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).addClass("ui-datepicker-next-hover"))}function _(e,i){for(var n in t.extend(e,i),i)null==i[n]&&(e[n]=i[n]);return e}function w(t){return function(){var e=this.element.val();t.apply(this,arguments),this._refresh(),e!==this.element.val()&&this._trigger("change")}}t.widget("ui.button",{version:"1.11.4",defaultElement:"<button>",options:{disabled:null,text:!0,label:null,icons:{primary:null,secondary:null}},_create:function(){this.element.closest("form").unbind("reset"+this.eventNamespace).bind("reset"+this.eventNamespace,m),"boolean"!=typeof this.options.disabled?this.options.disabled=!!this.element.prop("disabled"):this.element.prop("disabled",this.options.disabled),this._determineButtonType(),this.hasTitle=!!this.buttonElement.attr("title");var e=this,i=this.options,n="checkbox"===this.type||"radio"===this.type,s=n?"":"ui-state-active";null===i.label&&(i.label="input"===this.type?this.buttonElement.val():this.buttonElement.html()),this._hoverable(this.buttonElement),this.buttonElement.addClass(p).attr("role","button").bind("mouseenter"+this.eventNamespace,function(){i.disabled||this===u&&t(this).addClass("ui-state-active")}).bind("mouseleave"+this.eventNamespace,function(){i.disabled||t(this).removeClass(s)}).bind("click"+this.eventNamespace,function(t){i.disabled&&(t.preventDefault(),t.stopImmediatePropagation())}),this._on({focus:function(){this.buttonElement.addClass("ui-state-focus")},blur:function(){this.buttonElement.removeClass("ui-state-focus")}}),n&&this.element.bind("change"+this.eventNamespace,function(){e.refresh()}),"checkbox"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(i.disabled)return!1}):"radio"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(i.disabled)return!1;t(this).addClass("ui-state-active"),e.buttonElement.attr("aria-pressed","true");var n=e.element[0];g(n).not(n).map(function(){return t(this).button("widget")[0]}).removeClass("ui-state-active").attr("aria-pressed","false")}):(this.buttonElement.bind("mousedown"+this.eventNamespace,function(){if(i.disabled)return!1;t(this).addClass("ui-state-active"),u=this,e.document.one("mouseup",function(){u=null})}).bind("mouseup"+this.eventNamespace,function(){if(i.disabled)return!1;t(this).removeClass("ui-state-active")}).bind("keydown"+this.eventNamespace,function(e){if(i.disabled)return!1;e.keyCode!==t.ui.keyCode.SPACE&&e.keyCode!==t.ui.keyCode.ENTER||t(this).addClass("ui-state-active")}).bind("keyup"+this.eventNamespace+" blur"+this.eventNamespace,function(){t(this).removeClass("ui-state-active")}),this.buttonElement.is("a")&&this.buttonElement.keyup(function(e){e.keyCode===t.ui.keyCode.SPACE&&t(this).click()})),this._setOption("disabled",i.disabled),this._resetButton()},_determineButtonType:function(){var t,e,i;this.element.is("[type=checkbox]")?this.type="checkbox":this.element.is("[type=radio]")?this.type="radio":this.element.is("input")?this.type="input":this.type="button","checkbox"===this.type||"radio"===this.type?(t=this.element.parents().last(),e="label[for='"+this.element.attr("id")+"']",this.buttonElement=t.find(e),this.buttonElement.length||(t=t.length?t.siblings():this.element.siblings(),this.buttonElement=t.filter(e),this.buttonElement.length||(this.buttonElement=t.find(e))),this.element.addClass("ui-helper-hidden-accessible"),(i=this.element.is(":checked"))&&this.buttonElement.addClass("ui-state-active"),this.buttonElement.prop("aria-pressed",i)):this.buttonElement=this.element},widget:function(){return this.buttonElement},_destroy:function(){this.element.removeClass("ui-helper-hidden-accessible"),this.buttonElement.removeClass(p+" ui-state-active "+f).removeAttr("role").removeAttr("aria-pressed").html(this.buttonElement.find(".ui-button-text").html()),this.hasTitle||this.buttonElement.removeAttr("title")},_setOption:function(t,e){if(this._super(t,e),"disabled"===t)return this.widget().toggleClass("ui-state-disabled",!!e),this.element.prop("disabled",!!e),void(e&&("checkbox"===this.type||"radio"===this.type?this.buttonElement.removeClass("ui-state-focus"):this.buttonElement.removeClass("ui-state-focus ui-state-active")));this._resetButton()},refresh:function(){var e=this.element.is("input, button")?this.element.is(":disabled"):this.element.hasClass("ui-button-disabled");e!==this.options.disabled&&this._setOption("disabled",e),"radio"===this.type?g(this.element[0]).each(function(){t(this).is(":checked")?t(this).button("widget").addClass("ui-state-active").attr("aria-pressed","true"):t(this).button("widget").removeClass("ui-state-active").attr("aria-pressed","false")}):"checkbox"===this.type&&(this.element.is(":checked")?this.buttonElement.addClass("ui-state-active").attr("aria-pressed","true"):this.buttonElement.removeClass("ui-state-active").attr("aria-pressed","false"))},_resetButton:function(){if("input"!==this.type){var e=this.buttonElement.removeClass(f),i=t("<span></span>",this.document[0]).addClass("ui-button-text").html(this.options.label).appendTo(e.empty()).text(),n=this.options.icons,s=n.primary&&n.secondary,o=[];n.primary||n.secondary?(this.options.text&&o.push("ui-button-text-icon"+(s?"s":n.primary?"-primary":"-secondary")),n.primary&&e.prepend("<span class='ui-button-icon-primary ui-icon "+n.primary+"'></span>"),n.secondary&&e.append("<span class='ui-button-icon-secondary ui-icon "+n.secondary+"'></span>"),this.options.text||(o.push(s?"ui-button-icons-only":"ui-button-icon-only"),this.hasTitle||e.attr("title",t.trim(i)))):o.push("ui-button-text-only"),e.addClass(o.join(" "))}else this.options.label&&this.element.val(this.options.label)}}),t.widget("ui.buttonset",{version:"1.11.4",options:{items:"button, input[type=button], input[type=submit], input[type=reset], input[type=checkbox], input[type=radio], a, :data(ui-button)"},_create:function(){this.element.addClass("ui-buttonset")},_init:function(){this.refresh()},_setOption:function(t,e){"disabled"===t&&this.buttons.button("option",t,e),this._super(t,e)},refresh:function(){var e="rtl"===this.element.css("direction"),i=this.element.find(this.options.items),n=i.filter(":ui-button");i.not(":ui-button").button(),n.button("refresh"),this.buttons=i.map(function(){return t(this).button("widget")[0]}).removeClass("ui-corner-all ui-corner-left ui-corner-right").filter(":first").addClass(e?"ui-corner-right":"ui-corner-left").end().filter(":last").addClass(e?"ui-corner-left":"ui-corner-right").end().end()},_destroy:function(){this.element.removeClass("ui-buttonset"),this.buttons.map(function(){return t(this).button("widget")[0]}).removeClass("ui-corner-left ui-corner-right").end().button("destroy")}}),t.ui.button,t.extend(t.ui,{datepicker:{version:"1.11.4"}}),t.extend(v.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(t){return _(this._defaults,t||{}),this},_attachDatepicker:function(e,i){var n,s,o;s="div"===(n=e.nodeName.toLowerCase())||"span"===n,e.id||(this.uuid+=1,e.id="dp"+this.uuid),(o=this._newInst(t(e),s)).settings=t.extend({},i||{}),"input"===n?this._connectDatepicker(e,o):s&&this._inlineDatepicker(e,o)},_newInst:function(e,i){return{id:e[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1"),input:e,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:i,dpDiv:i?b(t("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>")):this.dpDiv}},_connectDatepicker:function(e,i){var n=t(e);i.append=t([]),i.trigger=t([]),n.hasClass(this.markerClassName)||(this._attachments(n,i),n.addClass(this.markerClassName).keydown(this._doKeyDown).keypress(this._doKeyPress).keyup(this._doKeyUp),this._autoSize(i),t.data(e,"datepicker",i),i.settings.disabled&&this._disableDatepicker(e))},_attachments:function(e,i){var n,s,o,r=this._get(i,"appendText"),a=this._get(i,"isRTL");i.append&&i.append.remove(),r&&(i.append=t("<span class='"+this._appendClass+"'>"+r+"</span>"),e[a?"before":"after"](i.append)),e.unbind("focus",this._showDatepicker),i.trigger&&i.trigger.remove(),"focus"!==(n=this._get(i,"showOn"))&&"both"!==n||e.focus(this._showDatepicker),"button"!==n&&"both"!==n||(s=this._get(i,"buttonText"),o=this._get(i,"buttonImage"),i.trigger=t(this._get(i,"buttonImageOnly")?t("<img/>").addClass(this._triggerClass).attr({src:o,alt:s,title:s}):t("<button type='button'></button>").addClass(this._triggerClass).html(o?t("<img/>").attr({src:o,alt:s,title:s}):s)),e[a?"before":"after"](i.trigger),i.trigger.click(function(){return t.datepicker._datepickerShowing&&t.datepicker._lastInput===e[0]?t.datepicker._hideDatepicker():t.datepicker._datepickerShowing&&t.datepicker._lastInput!==e[0]?(t.datepicker._hideDatepicker(),t.datepicker._showDatepicker(e[0])):t.datepicker._showDatepicker(e[0]),!1}))},_autoSize:function(t){if(this._get(t,"autoSize")&&!t.inline){var e,i,n,s,o=new Date(2009,11,20),r=this._get(t,"dateFormat");r.match(/[DM]/)&&(e=function(t){for(i=0,n=0,s=0;s<t.length;s++)t[s].length>i&&(i=t[s].length,n=s);return n},o.setMonth(e(this._get(t,r.match(/MM/)?"monthNames":"monthNamesShort"))),o.setDate(e(this._get(t,r.match(/DD/)?"dayNames":"dayNamesShort"))+20-o.getDay())),t.input.attr("size",this._formatDate(t,o).length)}},_inlineDatepicker:function(e,i){var n=t(e);n.hasClass(this.markerClassName)||(n.addClass(this.markerClassName).append(i.dpDiv),t.data(e,"datepicker",i),this._setDate(i,this._getDefaultDate(i),!0),this._updateDatepicker(i),this._updateAlternate(i),i.settings.disabled&&this._disableDatepicker(e),i.dpDiv.css("display","block"))},_dialogDatepicker:function(e,i,n,s,o){var r,a,l,h,c,u=this._dialogInst;return u||(this.uuid+=1,r="dp"+this.uuid,this._dialogInput=t("<input type='text' id='"+r+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.keydown(this._doKeyDown),t("body").append(this._dialogInput),(u=this._dialogInst=this._newInst(this._dialogInput,!1)).settings={},t.data(this._dialogInput[0],"datepicker",u)),_(u.settings,s||{}),i=i&&i.constructor===Date?this._formatDate(u,i):i,this._dialogInput.val(i),this._pos=o?o.length?o:[o.pageX,o.pageY]:null,this._pos||(a=document.documentElement.clientWidth,l=document.documentElement.clientHeight,h=document.documentElement.scrollLeft||document.body.scrollLeft,c=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[a/2-100+h,l/2-150+c]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),u.settings.onSelect=n,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),t.blockUI&&t.blockUI(this.dpDiv),t.data(this._dialogInput[0],"datepicker",u),this},_destroyDatepicker:function(e){var i,n=t(e),s=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),t.removeData(e,"datepicker"),"input"===i?(s.append.remove(),s.trigger.remove(),n.removeClass(this.markerClassName).unbind("focus",this._showDatepicker).unbind("keydown",this._doKeyDown).unbind("keypress",this._doKeyPress).unbind("keyup",this._doKeyUp)):"div"!==i&&"span"!==i||n.removeClass(this.markerClassName).empty(),d===s&&(d=null))},_enableDatepicker:function(e){var i,n,s=t(e),o=t.data(e,"datepicker");s.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!1,o.trigger.filter("button").each(function(){this.disabled=!1}).end().filter("img").css({opacity:"1.0",cursor:""})):"div"!==i&&"span"!==i||((n=s.children("."+this._inlineClass)).children().removeClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=t.map(this._disabledInputs,function(t){return t===e?null:t}))},_disableDatepicker:function(e){var i,n,s=t(e),o=t.data(e,"datepicker");s.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!0,o.trigger.filter("button").each(function(){this.disabled=!0}).end().filter("img").css({opacity:"0.5",cursor:"default"})):"div"!==i&&"span"!==i||((n=s.children("."+this._inlineClass)).children().addClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=t.map(this._disabledInputs,function(t){return t===e?null:t}),this._disabledInputs[this._disabledInputs.length]=e)},_isDisabledDatepicker:function(t){if(!t)return!1;for(var e=0;e<this._disabledInputs.length;e++)if(this._disabledInputs[e]===t)return!0;return!1},_getInst:function(e){try{return t.data(e,"datepicker")}catch(t){throw"Missing instance data for this datepicker"}},_optionDatepicker:function(e,i,n){var s,o,r,a,l=this._getInst(e);if(2===arguments.length&&"string"==typeof i)return"defaults"===i?t.extend({},t.datepicker._defaults):l?"all"===i?t.extend({},l.settings):this._get(l,i):null;s=i||{},"string"==typeof i&&((s={})[i]=n),l&&(this._curInst===l&&this._hideDatepicker(),o=this._getDateDatepicker(e,!0),r=this._getMinMaxDate(l,"min"),a=this._getMinMaxDate(l,"max"),_(l.settings,s),null!==r&&void 0!==s.dateFormat&&void 0===s.minDate&&(l.settings.minDate=this._formatDate(l,r)),null!==a&&void 0!==s.dateFormat&&void 0===s.maxDate&&(l.settings.maxDate=this._formatDate(l,a)),"disabled"in s&&(s.disabled?this._disableDatepicker(e):this._enableDatepicker(e)),this._attachments(t(e),l),this._autoSize(l),this._setDate(l,o),this._updateAlternate(l),this._updateDatepicker(l))},_changeDatepicker:function(t,e,i){this._optionDatepicker(t,e,i)},_refreshDatepicker:function(t){var e=this._getInst(t);e&&this._updateDatepicker(e)},_setDateDatepicker:function(t,e){var i=this._getInst(t);i&&(this._setDate(i,e),this._updateDatepicker(i),this._updateAlternate(i))},_getDateDatepicker:function(t,e){var i=this._getInst(t);return i&&!i.inline&&this._setDateFromField(i,e),i?this._getDate(i):null},_doKeyDown:function(e){var i,n,s,o=t.datepicker._getInst(e.target),r=!0,a=o.dpDiv.is(".ui-datepicker-rtl");if(o._keyEvent=!0,t.datepicker._datepickerShowing)switch(e.keyCode){case 9:t.datepicker._hideDatepicker(),r=!1;break;case 13:return(s=t("td."+t.datepicker._dayOverClass+":not(."+t.datepicker._currentClass+")",o.dpDiv))[0]&&t.datepicker._selectDay(e.target,o.selectedMonth,o.selectedYear,s[0]),(i=t.datepicker._get(o,"onSelect"))?(n=t.datepicker._formatDate(o),i.apply(o.input?o.input[0]:null,[n,o])):t.datepicker._hideDatepicker(),!1;case 27:t.datepicker._hideDatepicker();break;case 33:t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(o,"stepBigMonths"):-t.datepicker._get(o,"stepMonths"),"M");break;case 34:t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(o,"stepBigMonths"):+t.datepicker._get(o,"stepMonths"),"M");break;case 35:(e.ctrlKey||e.metaKey)&&t.datepicker._clearDate(e.target),r=e.ctrlKey||e.metaKey;break;case 36:(e.ctrlKey||e.metaKey)&&t.datepicker._gotoToday(e.target),r=e.ctrlKey||e.metaKey;break;case 37:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,a?1:-1,"D"),r=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(o,"stepBigMonths"):-t.datepicker._get(o,"stepMonths"),"M");break;case 38:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,-7,"D"),r=e.ctrlKey||e.metaKey;break;case 39:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,a?-1:1,"D"),r=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(o,"stepBigMonths"):+t.datepicker._get(o,"stepMonths"),"M");break;case 40:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,7,"D"),r=e.ctrlKey||e.metaKey;break;default:r=!1}else 36===e.keyCode&&e.ctrlKey?t.datepicker._showDatepicker(this):r=!1;r&&(e.preventDefault(),e.stopPropagation())},_doKeyPress:function(e){var i,n,s=t.datepicker._getInst(e.target);if(t.datepicker._get(s,"constrainInput"))return i=t.datepicker._possibleChars(t.datepicker._get(s,"dateFormat")),n=String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),e.ctrlKey||e.metaKey||n<" "||!i||i.indexOf(n)>-1},_doKeyUp:function(e){var i=t.datepicker._getInst(e.target);if(i.input.val()!==i.lastVal)try{t.datepicker.parseDate(t.datepicker._get(i,"dateFormat"),i.input?i.input.val():null,t.datepicker._getFormatConfig(i))&&(t.datepicker._setDateFromField(i),t.datepicker._updateAlternate(i),t.datepicker._updateDatepicker(i))}catch(t){}return!0},_showDatepicker:function(e){var i,n,s,o,r,a,l;"input"!==(e=e.target||e).nodeName.toLowerCase()&&(e=t("input",e.parentNode)[0]),t.datepicker._isDisabledDatepicker(e)||t.datepicker._lastInput===e||(i=t.datepicker._getInst(e),t.datepicker._curInst&&t.datepicker._curInst!==i&&(t.datepicker._curInst.dpDiv.stop(!0,!0),i&&t.datepicker._datepickerShowing&&t.datepicker._hideDatepicker(t.datepicker._curInst.input[0])),!1!==(s=(n=t.datepicker._get(i,"beforeShow"))?n.apply(e,[e,i]):{})&&(_(i.settings,s),i.lastVal=null,t.datepicker._lastInput=e,t.datepicker._setDateFromField(i),t.datepicker._inDialog&&(e.value=""),t.datepicker._pos||(t.datepicker._pos=t.datepicker._findPos(e),t.datepicker._pos[1]+=e.offsetHeight),o=!1,t(e).parents().each(function(){return!(o|="fixed"===t(this).css("position"))}),r={left:t.datepicker._pos[0],top:t.datepicker._pos[1]},t.datepicker._pos=null,i.dpDiv.empty(),i.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),t.datepicker._updateDatepicker(i),r=t.datepicker._checkOffset(i,r,o),i.dpDiv.css({position:t.datepicker._inDialog&&t.blockUI?"static":o?"fixed":"absolute",display:"none",left:r.left+"px",top:r.top+"px"}),i.inline||(a=t.datepicker._get(i,"showAnim"),l=t.datepicker._get(i,"duration"),i.dpDiv.css("z-index",function(t){for(var e,i;t.length&&t[0]!==document;){if(("absolute"===(e=t.css("position"))||"relative"===e||"fixed"===e)&&(i=parseInt(t.css("zIndex"),10),!isNaN(i)&&0!==i))return i;t=t.parent()}return 0}(t(e))+1),t.datepicker._datepickerShowing=!0,t.effects&&t.effects.effect[a]?i.dpDiv.show(a,t.datepicker._get(i,"showOptions"),l):i.dpDiv[a||"show"](a?l:null),t.datepicker._shouldFocusInput(i)&&i.input.focus(),t.datepicker._curInst=i)))},_updateDatepicker:function(e){this.maxRows=4,d=e,e.dpDiv.empty().append(this._generateHTML(e)),this._attachHandlers(e);var i,n=this._getNumberOfMonths(e),s=n[1],o=e.dpDiv.find("."+this._dayOverClass+" a");o.length>0&&y.apply(o.get(0)),e.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width(""),s>1&&e.dpDiv.addClass("ui-datepicker-multi-"+s).css("width",17*s+"em"),e.dpDiv[(1!==n[0]||1!==n[1]?"add":"remove")+"Class"]("ui-datepicker-multi"),e.dpDiv[(this._get(e,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl"),e===t.datepicker._curInst&&t.datepicker._datepickerShowing&&t.datepicker._shouldFocusInput(e)&&e.input.focus(),e.yearshtml&&(i=e.yearshtml,setTimeout(function(){i===e.yearshtml&&e.yearshtml&&e.dpDiv.find("select.ui-datepicker-year:first").replaceWith(e.yearshtml),i=e.yearshtml=null},0))},_shouldFocusInput:function(t){return t.input&&t.input.is(":visible")&&!t.input.is(":disabled")&&!t.input.is(":focus")},_checkOffset:function(e,i,n){var s=e.dpDiv.outerWidth(),o=e.dpDiv.outerHeight(),r=e.input?e.input.outerWidth():0,a=e.input?e.input.outerHeight():0,l=document.documentElement.clientWidth+(n?0:t(document).scrollLeft()),h=document.documentElement.clientHeight+(n?0:t(document).scrollTop());return i.left-=this._get(e,"isRTL")?s-r:0,i.left-=n&&i.left===e.input.offset().left?t(document).scrollLeft():0,i.top-=n&&i.top===e.input.offset().top+a?t(document).scrollTop():0,i.left-=Math.min(i.left,i.left+s>l&&l>s?Math.abs(i.left+s-l):0),i.top-=Math.min(i.top,i.top+o>h&&h>o?Math.abs(o+a):0),i},_findPos:function(e){for(var i,n=this._getInst(e),s=this._get(n,"isRTL");e&&("hidden"===e.type||1!==e.nodeType||t.expr.filters.hidden(e));)e=e[s?"previousSibling":"nextSibling"];return[(i=t(e).offset()).left,i.top]},_hideDatepicker:function(e){var i,n,s,o,r=this._curInst;!r||e&&r!==t.data(e,"datepicker")||this._datepickerShowing&&(i=this._get(r,"showAnim"),n=this._get(r,"duration"),s=function(){t.datepicker._tidyDialog(r)},t.effects&&(t.effects.effect[i]||t.effects[i])?r.dpDiv.hide(i,t.datepicker._get(r,"showOptions"),n,s):r.dpDiv["slideDown"===i?"slideUp":"fadeIn"===i?"fadeOut":"hide"](i?n:null,s),i||s(),this._datepickerShowing=!1,(o=this._get(r,"onClose"))&&o.apply(r.input?r.input[0]:null,[r.input?r.input.val():"",r]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),t.blockUI&&(t.unblockUI(),t("body").append(this.dpDiv))),this._inDialog=!1)},_tidyDialog:function(t){t.dpDiv.removeClass(this._dialogClass).unbind(".ui-datepicker-calendar")},_checkExternalClick:function(e){if(t.datepicker._curInst){var i=t(e.target),n=t.datepicker._getInst(i[0]);(i[0].id===t.datepicker._mainDivId||0!==i.parents("#"+t.datepicker._mainDivId).length||i.hasClass(t.datepicker.markerClassName)||i.closest("."+t.datepicker._triggerClass).length||!t.datepicker._datepickerShowing||t.datepicker._inDialog&&t.blockUI)&&(!i.hasClass(t.datepicker.markerClassName)||t.datepicker._curInst===n)||t.datepicker._hideDatepicker()}},_adjustDate:function(e,i,n){var s=t(e),o=this._getInst(s[0]);this._isDisabledDatepicker(s[0])||(this._adjustInstDate(o,i+("M"===n?this._get(o,"showCurrentAtPos"):0),n),this._updateDatepicker(o))},_gotoToday:function(e){var i,n=t(e),s=this._getInst(n[0]);this._get(s,"gotoCurrent")&&s.currentDay?(s.selectedDay=s.currentDay,s.drawMonth=s.selectedMonth=s.currentMonth,s.drawYear=s.selectedYear=s.currentYear):(i=new Date,s.selectedDay=i.getDate(),s.drawMonth=s.selectedMonth=i.getMonth(),s.drawYear=s.selectedYear=i.getFullYear()),this._notifyChange(s),this._adjustDate(n)},_selectMonthYear:function(e,i,n){var s=t(e),o=this._getInst(s[0]);o["selected"+("M"===n?"Month":"Year")]=o["draw"+("M"===n?"Month":"Year")]=parseInt(i.options[i.selectedIndex].value,10),this._notifyChange(o),this._adjustDate(s)},_selectDay:function(e,i,n,s){var o,r=t(e);t(s).hasClass(this._unselectableClass)||this._isDisabledDatepicker(r[0])||((o=this._getInst(r[0])).selectedDay=o.currentDay=t("a",s).html(),o.selectedMonth=o.currentMonth=i,o.selectedYear=o.currentYear=n,this._selectDate(e,this._formatDate(o,o.currentDay,o.currentMonth,o.currentYear)))},_clearDate:function(e){var i=t(e);this._selectDate(i,"")},_selectDate:function(e,i){var n,s=t(e),o=this._getInst(s[0]);i=null!=i?i:this._formatDate(o),o.input&&o.input.val(i),this._updateAlternate(o),(n=this._get(o,"onSelect"))?n.apply(o.input?o.input[0]:null,[i,o]):o.input&&o.input.trigger("change"),o.inline?this._updateDatepicker(o):(this._hideDatepicker(),this._lastInput=o.input[0],"object"!=typeof o.input[0]&&o.input.focus(),this._lastInput=null)},_updateAlternate:function(e){var i,n,s,o=this._get(e,"altField");o&&(i=this._get(e,"altFormat")||this._get(e,"dateFormat"),n=this._getDate(e),s=this.formatDate(i,n,this._getFormatConfig(e)),t(o).each(function(){t(this).val(s)}))},noWeekends:function(t){var e=t.getDay();return[e>0&&e<6,""]},iso8601Week:function(t){var e,i=new Date(t.getTime());return i.setDate(i.getDate()+4-(i.getDay()||7)),e=i.getTime(),i.setMonth(0),i.setDate(1),Math.floor(Math.round((e-i)/864e5)/7)+1},parseDate:function(e,i,n){if(null==e||null==i)throw"Invalid arguments";if(""===(i="object"==typeof i?i.toString():i+""))return null;var s,o,r,a,l=0,h=(n?n.shortYearCutoff:null)||this._defaults.shortYearCutoff,c="string"!=typeof h?h:(new Date).getFullYear()%100+parseInt(h,10),u=(n?n.dayNamesShort:null)||this._defaults.dayNamesShort,d=(n?n.dayNames:null)||this._defaults.dayNames,p=(n?n.monthNamesShort:null)||this._defaults.monthNamesShort,f=(n?n.monthNames:null)||this._defaults.monthNames,m=-1,g=-1,v=-1,b=-1,y=!1,_=function(t){var i=s+1<e.length&&e.charAt(s+1)===t;return i&&s++,i},w=function(t){var e=_(t),n="@"===t?14:"!"===t?20:"y"===t&&e?4:"o"===t?3:2,s=new RegExp("^\\d{"+("y"===t?n:1)+","+n+"}"),o=i.substring(l).match(s);if(!o)throw"Missing number at position "+l;return l+=o[0].length,parseInt(o[0],10)},x=function(e,n,s){var o=-1,r=t.map(_(e)?s:n,function(t,e){return[[e,t]]}).sort(function(t,e){return-(t[1].length-e[1].length)});if(t.each(r,function(t,e){var n=e[1];if(i.substr(l,n.length).toLowerCase()===n.toLowerCase())return o=e[0],l+=n.length,!1}),-1!==o)return o+1;throw"Unknown name at position "+l},C=function(){if(i.charAt(l)!==e.charAt(s))throw"Unexpected literal at position "+l;l++};for(s=0;s<e.length;s++)if(y)"'"!==e.charAt(s)||_("'")?C():y=!1;else switch(e.charAt(s)){case"d":v=w("d");break;case"D":x("D",u,d);break;case"o":b=w("o");break;case"m":g=w("m");break;case"M":g=x("M",p,f);break;case"y":m=w("y");break;case"@":m=(a=new Date(w("@"))).getFullYear(),g=a.getMonth()+1,v=a.getDate();break;case"!":m=(a=new Date((w("!")-this._ticksTo1970)/1e4)).getFullYear(),g=a.getMonth()+1,v=a.getDate();break;case"'":_("'")?C():y=!0;break;default:C()}if(l<i.length&&(r=i.substr(l),!/^\s+/.test(r)))throw"Extra/unparsed characters found in date: "+r;if(-1===m?m=(new Date).getFullYear():m<100&&(m+=(new Date).getFullYear()-(new Date).getFullYear()%100+(m<=c?0:-100)),b>-1)for(g=1,v=b;!(v<=(o=this._getDaysInMonth(m,g-1)));)g++,v-=o;if((a=this._daylightSavingAdjust(new Date(m,g-1,v))).getFullYear()!==m||a.getMonth()+1!==g||a.getDate()!==v)throw"Invalid date";return a},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:24*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925))*60*60*1e7,formatDate:function(t,e,i){if(!e)return"";var n,s=(i?i.dayNamesShort:null)||this._defaults.dayNamesShort,o=(i?i.dayNames:null)||this._defaults.dayNames,r=(i?i.monthNamesShort:null)||this._defaults.monthNamesShort,a=(i?i.monthNames:null)||this._defaults.monthNames,l=function(e){var i=n+1<t.length&&t.charAt(n+1)===e;return i&&n++,i},h=function(t,e,i){var n=""+e;if(l(t))for(;n.length<i;)n="0"+n;return n},c=function(t,e,i,n){return l(t)?n[e]:i[e]},u="",d=!1;if(e)for(n=0;n<t.length;n++)if(d)"'"!==t.charAt(n)||l("'")?u+=t.charAt(n):d=!1;else switch(t.charAt(n)){case"d":u+=h("d",e.getDate(),2);break;case"D":u+=c("D",e.getDay(),s,o);break;case"o":u+=h("o",Math.round((new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),3);break;case"m":u+=h("m",e.getMonth()+1,2);break;case"M":u+=c("M",e.getMonth(),r,a);break;case"y":u+=l("y")?e.getFullYear():(e.getYear()%100<10?"0":"")+e.getYear()%100;break;case"@":u+=e.getTime();break;case"!":u+=1e4*e.getTime()+this._ticksTo1970;break;case"'":l("'")?u+="'":d=!0;break;default:u+=t.charAt(n)}return u},_possibleChars:function(t){var e,i="",n=!1,s=function(i){var n=e+1<t.length&&t.charAt(e+1)===i;return n&&e++,n};for(e=0;e<t.length;e++)if(n)"'"!==t.charAt(e)||s("'")?i+=t.charAt(e):n=!1;else switch(t.charAt(e)){case"d":case"m":case"y":case"@":i+="0123456789";break;case"D":case"M":return null;case"'":s("'")?i+="'":n=!0;break;default:i+=t.charAt(e)}return i},_get:function(t,e){return void 0!==t.settings[e]?t.settings[e]:this._defaults[e]},_setDateFromField:function(t,e){if(t.input.val()!==t.lastVal){var i=this._get(t,"dateFormat"),n=t.lastVal=t.input?t.input.val():null,s=this._getDefaultDate(t),o=s,r=this._getFormatConfig(t);try{o=this.parseDate(i,n,r)||s}catch(t){n=e?"":n}t.selectedDay=o.getDate(),t.drawMonth=t.selectedMonth=o.getMonth(),t.drawYear=t.selectedYear=o.getFullYear(),t.currentDay=n?o.getDate():0,t.currentMonth=n?o.getMonth():0,t.currentYear=n?o.getFullYear():0,this._adjustInstDate(t)}},_getDefaultDate:function(t){return this._restrictMinMax(t,this._determineDate(t,this._get(t,"defaultDate"),new Date))},_determineDate:function(e,i,n){var s,o,r=null==i||""===i?n:"string"==typeof i?function(i){try{return t.datepicker.parseDate(t.datepicker._get(e,"dateFormat"),i,t.datepicker._getFormatConfig(e))}catch(t){}for(var n=(i.toLowerCase().match(/^c/)?t.datepicker._getDate(e):null)||new Date,s=n.getFullYear(),o=n.getMonth(),r=n.getDate(),a=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,l=a.exec(i);l;){switch(l[2]||"d"){case"d":case"D":r+=parseInt(l[1],10);break;case"w":case"W":r+=7*parseInt(l[1],10);break;case"m":case"M":o+=parseInt(l[1],10),r=Math.min(r,t.datepicker._getDaysInMonth(s,o));break;case"y":case"Y":s+=parseInt(l[1],10),r=Math.min(r,t.datepicker._getDaysInMonth(s,o))}l=a.exec(i)}return new Date(s,o,r)}(i):"number"==typeof i?isNaN(i)?n:(s=i,(o=new Date).setDate(o.getDate()+s),o):new Date(i.getTime());return(r=r&&"Invalid Date"===r.toString()?n:r)&&(r.setHours(0),r.setMinutes(0),r.setSeconds(0),r.setMilliseconds(0)),this._daylightSavingAdjust(r)},_daylightSavingAdjust:function(t){return t?(t.setHours(t.getHours()>12?t.getHours()+2:0),t):null},_setDate:function(t,e,i){var n=!e,s=t.selectedMonth,o=t.selectedYear,r=this._restrictMinMax(t,this._determineDate(t,e,new Date));t.selectedDay=t.currentDay=r.getDate(),t.drawMonth=t.selectedMonth=t.currentMonth=r.getMonth(),t.drawYear=t.selectedYear=t.currentYear=r.getFullYear(),s===t.selectedMonth&&o===t.selectedYear||i||this._notifyChange(t),this._adjustInstDate(t),t.input&&t.input.val(n?"":this._formatDate(t))},_getDate:function(t){return!t.currentYear||t.input&&""===t.input.val()?null:this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay))},_attachHandlers:function(e){var i=this._get(e,"stepMonths"),n="#"+e.id.replace(/\\\\/g,"\\");e.dpDiv.find("[data-handler]").map(function(){var e={prev:function(){t.datepicker._adjustDate(n,-i,"M")},next:function(){t.datepicker._adjustDate(n,+i,"M")},hide:function(){t.datepicker._hideDatepicker()},today:function(){t.datepicker._gotoToday(n)},selectDay:function(){return t.datepicker._selectDay(n,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return t.datepicker._selectMonthYear(n,this,"M"),!1},selectYear:function(){return t.datepicker._selectMonthYear(n,this,"Y"),!1}};t(this).bind(this.getAttribute("data-event"),e[this.getAttribute("data-handler")])})},_generateHTML:function(t){var e,i,n,s,o,r,a,l,h,c,u,d,p,f,m,g,v,b,y,_,w,x,C,k,D,T,S,E,I,N,A,M,P,H,O,z,W,L,$,F=new Date,R=this._daylightSavingAdjust(new Date(F.getFullYear(),F.getMonth(),F.getDate())),j=this._get(t,"isRTL"),q=this._get(t,"showButtonPanel"),B=this._get(t,"hideIfNoPrevNext"),Y=this._get(t,"navigationAsDateFormat"),U=this._getNumberOfMonths(t),K=this._get(t,"showCurrentAtPos"),V=this._get(t,"stepMonths"),X=1!==U[0]||1!==U[1],G=this._daylightSavingAdjust(t.currentDay?new Date(t.currentYear,t.currentMonth,t.currentDay):new Date(9999,9,9)),Q=this._getMinMaxDate(t,"min"),J=this._getMinMaxDate(t,"max"),Z=t.drawMonth-K,tt=t.drawYear;if(Z<0&&(Z+=12,tt--),J)for(e=this._daylightSavingAdjust(new Date(J.getFullYear(),J.getMonth()-U[0]*U[1]+1,J.getDate())),e=Q&&e<Q?Q:e;this._daylightSavingAdjust(new Date(tt,Z,1))>e;)--Z<0&&(Z=11,tt--);for(t.drawMonth=Z,t.drawYear=tt,i=this._get(t,"prevText"),i=Y?this.formatDate(i,this._daylightSavingAdjust(new Date(tt,Z-V,1)),this._getFormatConfig(t)):i,n=this._canAdjustMonth(t,-1,tt,Z)?"<a class='ui-datepicker-prev ui-corner-all' data-handler='prev' data-event='click' title='"+i+"'><span class='ui-icon ui-icon-circle-triangle-"+(j?"e":"w")+"'>"+i+"</span></a>":B?"":"<a class='ui-datepicker-prev ui-corner-all ui-state-disabled' title='"+i+"'><span class='ui-icon ui-icon-circle-triangle-"+(j?"e":"w")+"'>"+i+"</span></a>",s=this._get(t,"nextText"),s=Y?this.formatDate(s,this._daylightSavingAdjust(new Date(tt,Z+V,1)),this._getFormatConfig(t)):s,o=this._canAdjustMonth(t,1,tt,Z)?"<a class='ui-datepicker-next ui-corner-all' data-handler='next' data-event='click' title='"+s+"'><span class='ui-icon ui-icon-circle-triangle-"+(j?"w":"e")+"'>"+s+"</span></a>":B?"":"<a class='ui-datepicker-next ui-corner-all ui-state-disabled' title='"+s+"'><span class='ui-icon ui-icon-circle-triangle-"+(j?"w":"e")+"'>"+s+"</span></a>",r=this._get(t,"currentText"),a=this._get(t,"gotoCurrent")&&t.currentDay?G:R,r=Y?this.formatDate(r,a,this._getFormatConfig(t)):r,l=t.inline?"":"<button type='button' class='ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all' data-handler='hide' data-event='click'>"+this._get(t,"closeText")+"</button>",h=q?"<div class='ui-datepicker-buttonpane ui-widget-content'>"+(j?l:"")+(this._isInRange(t,a)?"<button type='button' class='ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all' data-handler='today' data-event='click'>"+r+"</button>":"")+(j?"":l)+"</div>":"",c=parseInt(this._get(t,"firstDay"),10),c=isNaN(c)?0:c,u=this._get(t,"showWeek"),d=this._get(t,"dayNames"),p=this._get(t,"dayNamesMin"),f=this._get(t,"monthNames"),m=this._get(t,"monthNamesShort"),g=this._get(t,"beforeShowDay"),v=this._get(t,"showOtherMonths"),b=this._get(t,"selectOtherMonths"),y=this._getDefaultDate(t),_="",x=0;x<U[0];x++){for(C="",this.maxRows=4,k=0;k<U[1];k++){if(D=this._daylightSavingAdjust(new Date(tt,Z,t.selectedDay)),T=" ui-corner-all",S="",X){if(S+="<div class='ui-datepicker-group",U[1]>1)switch(k){case 0:S+=" ui-datepicker-group-first",T=" ui-corner-"+(j?"right":"left");break;case U[1]-1:S+=" ui-datepicker-group-last",T=" ui-corner-"+(j?"left":"right");break;default:S+=" ui-datepicker-group-middle",T=""}S+="'>"}for(S+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+T+"'>"+(/all|left/.test(T)&&0===x?j?o:n:"")+(/all|right/.test(T)&&0===x?j?n:o:"")+this._generateMonthYearHeader(t,Z,tt,Q,J,x>0||k>0,f,m)+"</div><table class='ui-datepicker-calendar'><thead><tr>",E=u?"<th class='ui-datepicker-week-col'>"+this._get(t,"weekHeader")+"</th>":"",w=0;w<7;w++)E+="<th scope='col'"+((w+c+6)%7>=5?" class='ui-datepicker-week-end'":"")+"><span title='"+d[I=(w+c)%7]+"'>"+p[I]+"</span></th>";for(S+=E+"</tr></thead><tbody>",N=this._getDaysInMonth(tt,Z),tt===t.selectedYear&&Z===t.selectedMonth&&(t.selectedDay=Math.min(t.selectedDay,N)),A=(this._getFirstDayOfMonth(tt,Z)-c+7)%7,M=Math.ceil((A+N)/7),P=X&&this.maxRows>M?this.maxRows:M,this.maxRows=P,H=this._daylightSavingAdjust(new Date(tt,Z,1-A)),O=0;O<P;O++){for(S+="<tr>",z=u?"<td class='ui-datepicker-week-col'>"+this._get(t,"calculateWeek")(H)+"</td>":"",w=0;w<7;w++)W=g?g.apply(t.input?t.input[0]:null,[H]):[!0,""],$=(L=H.getMonth()!==Z)&&!b||!W[0]||Q&&H<Q||J&&H>J,z+="<td class='"+((w+c+6)%7>=5?" ui-datepicker-week-end":"")+(L?" ui-datepicker-other-month":"")+(H.getTime()===D.getTime()&&Z===t.selectedMonth&&t._keyEvent||y.getTime()===H.getTime()&&y.getTime()===D.getTime()?" "+this._dayOverClass:"")+($?" "+this._unselectableClass+" ui-state-disabled":"")+(L&&!v?"":" "+W[1]+(H.getTime()===G.getTime()?" "+this._currentClass:"")+(H.getTime()===R.getTime()?" ui-datepicker-today":""))+"'"+(L&&!v||!W[2]?"":" title='"+W[2].replace(/'/g,"&#39;")+"'")+($?"":" data-handler='selectDay' data-event='click' data-month='"+H.getMonth()+"' data-year='"+H.getFullYear()+"'")+">"+(L&&!v?"&#xa0;":$?"<span class='ui-state-default'>"+H.getDate()+"</span>":"<a class='ui-state-default"+(H.getTime()===R.getTime()?" ui-state-highlight":"")+(H.getTime()===G.getTime()?" ui-state-active":"")+(L?" ui-priority-secondary":"")+"' href='#'>"+H.getDate()+"</a>")+"</td>",H.setDate(H.getDate()+1),H=this._daylightSavingAdjust(H);S+=z+"</tr>"}++Z>11&&(Z=0,tt++),C+=S+="</tbody></table>"+(X?"</div>"+(U[0]>0&&k===U[1]-1?"<div class='ui-datepicker-row-break'></div>":""):"")}_+=C}return _+=h,t._keyEvent=!1,_},_generateMonthYearHeader:function(t,e,i,n,s,o,r,a){var l,h,c,u,d,p,f,m,g=this._get(t,"changeMonth"),v=this._get(t,"changeYear"),b=this._get(t,"showMonthAfterYear"),y="<div class='ui-datepicker-title'>",_="";if(o||!g)_+="<span class='ui-datepicker-month'>"+r[e]+"</span>";else{for(l=n&&n.getFullYear()===i,h=s&&s.getFullYear()===i,_+="<select class='ui-datepicker-month' data-handler='selectMonth' data-event='change'>",c=0;c<12;c++)(!l||c>=n.getMonth())&&(!h||c<=s.getMonth())&&(_+="<option value='"+c+"'"+(c===e?" selected='selected'":"")+">"+a[c]+"</option>");_+="</select>"}if(b||(y+=_+(!o&&g&&v?"":"&#xa0;")),!t.yearshtml)if(t.yearshtml="",o||!v)y+="<span class='ui-datepicker-year'>"+i+"</span>";else{for(u=this._get(t,"yearRange").split(":"),d=(new Date).getFullYear(),f=(p=function(t){var e=t.match(/c[+\-].*/)?i+parseInt(t.substring(1),10):t.match(/[+\-].*/)?d+parseInt(t,10):parseInt(t,10);return isNaN(e)?d:e})(u[0]),m=Math.max(f,p(u[1]||"")),f=n?Math.max(f,n.getFullYear()):f,m=s?Math.min(m,s.getFullYear()):m,t.yearshtml+="<select class='ui-datepicker-year' data-handler='selectYear' data-event='change'>";f<=m;f++)t.yearshtml+="<option value='"+f+"'"+(f===i?" selected='selected'":"")+">"+f+"</option>";t.yearshtml+="</select>",y+=t.yearshtml,t.yearshtml=null}return y+=this._get(t,"yearSuffix"),b&&(y+=(!o&&g&&v?"":"&#xa0;")+_),y+"</div>"},_adjustInstDate:function(t,e,i){var n=t.drawYear+("Y"===i?e:0),s=t.drawMonth+("M"===i?e:0),o=Math.min(t.selectedDay,this._getDaysInMonth(n,s))+("D"===i?e:0),r=this._restrictMinMax(t,this._daylightSavingAdjust(new Date(n,s,o)));t.selectedDay=r.getDate(),t.drawMonth=t.selectedMonth=r.getMonth(),t.drawYear=t.selectedYear=r.getFullYear(),"M"!==i&&"Y"!==i||this._notifyChange(t)},_restrictMinMax:function(t,e){var i=this._getMinMaxDate(t,"min"),n=this._getMinMaxDate(t,"max"),s=i&&e<i?i:e;return n&&s>n?n:s},_notifyChange:function(t){var e=this._get(t,"onChangeMonthYear");e&&e.apply(t.input?t.input[0]:null,[t.selectedYear,t.selectedMonth+1,t])},_getNumberOfMonths:function(t){var e=this._get(t,"numberOfMonths");return null==e?[1,1]:"number"==typeof e?[1,e]:e},_getMinMaxDate:function(t,e){return this._determineDate(t,this._get(t,e+"Date"),null)},_getDaysInMonth:function(t,e){return 32-this._daylightSavingAdjust(new Date(t,e,32)).getDate()},_getFirstDayOfMonth:function(t,e){return new Date(t,e,1).getDay()},_canAdjustMonth:function(t,e,i,n){var s=this._getNumberOfMonths(t),o=this._daylightSavingAdjust(new Date(i,n+(e<0?e:s[0]*s[1]),1));return e<0&&o.setDate(this._getDaysInMonth(o.getFullYear(),o.getMonth())),this._isInRange(t,o)},_isInRange:function(t,e){var i,n,s=this._getMinMaxDate(t,"min"),o=this._getMinMaxDate(t,"max"),r=null,a=null,l=this._get(t,"yearRange");return l&&(i=l.split(":"),n=(new Date).getFullYear(),r=parseInt(i[0],10),a=parseInt(i[1],10),i[0].match(/[+\-].*/)&&(r+=n),i[1].match(/[+\-].*/)&&(a+=n)),(!s||e.getTime()>=s.getTime())&&(!o||e.getTime()<=o.getTime())&&(!r||e.getFullYear()>=r)&&(!a||e.getFullYear()<=a)},_getFormatConfig:function(t){var e=this._get(t,"shortYearCutoff");return{shortYearCutoff:e="string"!=typeof e?e:(new Date).getFullYear()%100+parseInt(e,10),dayNamesShort:this._get(t,"dayNamesShort"),dayNames:this._get(t,"dayNames"),monthNamesShort:this._get(t,"monthNamesShort"),monthNames:this._get(t,"monthNames")}},_formatDate:function(t,e,i,n){e||(t.currentDay=t.selectedDay,t.currentMonth=t.selectedMonth,t.currentYear=t.selectedYear);var s=e?"object"==typeof e?e:this._daylightSavingAdjust(new Date(n,i,e)):this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay));return this.formatDate(this._get(t,"dateFormat"),s,this._getFormatConfig(t))}}),t.fn.datepicker=function(e){if(!this.length)return this;t.datepicker.initialized||(t(document).mousedown(t.datepicker._checkExternalClick),t.datepicker.initialized=!0),0===t("#"+t.datepicker._mainDivId).length&&t("body").append(t.datepicker.dpDiv);var i=Array.prototype.slice.call(arguments,1);return"string"!=typeof e||"isDisabled"!==e&&"getDate"!==e&&"widget"!==e?"option"===e&&2===arguments.length&&"string"==typeof arguments[1]?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i)):this.each(function(){"string"==typeof e?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this].concat(i)):t.datepicker._attachDatepicker(this,e)}):t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i))},t.datepicker=new v,t.datepicker.initialized=!1,t.datepicker.uuid=(new Date).getTime(),t.datepicker.version="1.11.4",t.datepicker,t.widget("ui.dialog",{version:"1.11.4",options:{appendTo:"body",autoOpen:!0,buttons:[],closeOnEscape:!0,closeText:"Close",dialogClass:"",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(e){var i=t(this).css(e).offset().top;i<0&&t(this).css("top",e.top-i)}},resizable:!0,show:null,title:null,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height},this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)},this.originalTitle=this.element.attr("title"),this.options.title=this.options.title||this.originalTitle,this._createWrapper(),this.element.show().removeAttr("title").addClass("ui-dialog-content ui-widget-content").appendTo(this.uiDialog),this._createTitlebar(),this._createButtonPane(),this.options.draggable&&t.fn.draggable&&this._makeDraggable(),this.options.resizable&&t.fn.resizable&&this._makeResizable(),this._isOpen=!1,this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var e=this.options.appendTo;return e&&(e.jquery||e.nodeType)?t(e):this.document.find(e||"body").eq(0)},_destroy:function(){var t,e=this.originalPosition;this._untrackInstance(),this._destroyOverlay(),this.element.removeUniqueId().removeClass("ui-dialog-content ui-widget-content").css(this.originalCss).detach(),this.uiDialog.stop(!0,!0).remove(),this.originalTitle&&this.element.attr("title",this.originalTitle),(t=e.parent.children().eq(e.index)).length&&t[0]!==this.element[0]?t.before(this.element):e.parent.append(this.element)},widget:function(){return this.uiDialog},disable:t.noop,enable:t.noop,close:function(e){var i,n=this;if(this._isOpen&&!1!==this._trigger("beforeClose",e)){if(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),!this.opener.filter(":focusable").focus().length)try{(i=this.document[0].activeElement)&&"body"!==i.nodeName.toLowerCase()&&t(i).blur()}catch(t){}this._hide(this.uiDialog,this.options.hide,function(){n._trigger("close",e)})}},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(e,i){var n=!1,s=this.uiDialog.siblings(".ui-front:visible").map(function(){return+t(this).css("z-index")}).get(),o=Math.max.apply(null,s);return o>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",o+1),n=!0),n&&!i&&this._trigger("focus",e),n},open:function(){var e=this;this._isOpen?this._moveToTop()&&this._focusTabbable():(this._isOpen=!0,this.opener=t(this.document[0].activeElement),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,function(){e._focusTabbable(),e._trigger("focus")}),this._makeFocusTarget(),this._trigger("open"))},_focusTabbable:function(){var t=this._focusedElement;t||(t=this.element.find("[autofocus]")),t.length||(t=this.element.find(":tabbable")),t.length||(t=this.uiDialogButtonPane.find(":tabbable")),t.length||(t=this.uiDialogTitlebarClose.filter(":tabbable")),t.length||(t=this.uiDialog),t.eq(0).focus()},_keepFocus:function(e){function i(){var e=this.document[0].activeElement;this.uiDialog[0]===e||t.contains(this.uiDialog[0],e)||this._focusTabbable()}e.preventDefault(),i.call(this),this._delay(i)},_createWrapper:function(){this.uiDialog=t("<div>").addClass("ui-dialog ui-widget ui-widget-content ui-corner-all ui-front "+this.options.dialogClass).hide().attr({tabIndex:-1,role:"dialog"}).appendTo(this._appendTo()),this._on(this.uiDialog,{keydown:function(e){if(this.options.closeOnEscape&&!e.isDefaultPrevented()&&e.keyCode&&e.keyCode===t.ui.keyCode.ESCAPE)return e.preventDefault(),void this.close(e);if(e.keyCode===t.ui.keyCode.TAB&&!e.isDefaultPrevented()){var i=this.uiDialog.find(":tabbable"),n=i.filter(":first"),s=i.filter(":last");e.target!==s[0]&&e.target!==this.uiDialog[0]||e.shiftKey?e.target!==n[0]&&e.target!==this.uiDialog[0]||!e.shiftKey||(this._delay(function(){s.focus()}),e.preventDefault()):(this._delay(function(){n.focus()}),e.preventDefault())}},mousedown:function(t){this._moveToTop(t)&&this._focusTabbable()}}),this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var e;this.uiDialogTitlebar=t("<div>").addClass("ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix").prependTo(this.uiDialog),this._on(this.uiDialogTitlebar,{mousedown:function(e){t(e.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.focus()}}),this.uiDialogTitlebarClose=t("<button type='button'></button>").button({label:this.options.closeText,icons:{primary:"ui-icon-closethick"},text:!1}).addClass("ui-dialog-titlebar-close").appendTo(this.uiDialogTitlebar),this._on(this.uiDialogTitlebarClose,{click:function(t){t.preventDefault(),this.close(t)}}),e=t("<span>").uniqueId().addClass("ui-dialog-title").prependTo(this.uiDialogTitlebar),this._title(e),this.uiDialog.attr({"aria-labelledby":e.attr("id")})},_title:function(t){this.options.title||t.html("&#160;"),t.text(this.options.title)},_createButtonPane:function(){this.uiDialogButtonPane=t("<div>").addClass("ui-dialog-buttonpane ui-widget-content ui-helper-clearfix"),this.uiButtonSet=t("<div>").addClass("ui-dialog-buttonset").appendTo(this.uiDialogButtonPane),this._createButtons()},_createButtons:function(){var e=this,i=this.options.buttons;this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),t.isEmptyObject(i)||t.isArray(i)&&!i.length?this.uiDialog.removeClass("ui-dialog-buttons"):(t.each(i,function(i,n){var s,o;n=t.isFunction(n)?{click:n,text:i}:n,n=t.extend({type:"button"},n),s=n.click,n.click=function(){s.apply(e.element[0],arguments)},o={icons:n.icons,text:n.showText},delete n.icons,delete n.showText,t("<button></button>",n).button(o).appendTo(e.uiButtonSet)}),this.uiDialog.addClass("ui-dialog-buttons"),this.uiDialogButtonPane.appendTo(this.uiDialog))},_makeDraggable:function(){var e=this,i=this.options;function n(t){return{position:t.position,offset:t.offset}}this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(i,s){t(this).addClass("ui-dialog-dragging"),e._blockFrames(),e._trigger("dragStart",i,n(s))},drag:function(t,i){e._trigger("drag",t,n(i))},stop:function(s,o){var r=o.offset.left-e.document.scrollLeft(),a=o.offset.top-e.document.scrollTop();i.position={my:"left top",at:"left"+(r>=0?"+":"")+r+" top"+(a>=0?"+":"")+a,of:e.window},t(this).removeClass("ui-dialog-dragging"),e._unblockFrames(),e._trigger("dragStop",s,n(o))}})},_makeResizable:function(){var e=this,i=this.options,n=i.resizable,s=this.uiDialog.css("position"),o="string"==typeof n?n:"n,e,s,w,se,sw,ne,nw";function r(t){return{originalPosition:t.originalPosition,originalSize:t.originalSize,position:t.position,size:t.size}}this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:i.maxWidth,maxHeight:i.maxHeight,minWidth:i.minWidth,minHeight:this._minHeight(),handles:o,start:function(i,n){t(this).addClass("ui-dialog-resizing"),e._blockFrames(),e._trigger("resizeStart",i,r(n))},resize:function(t,i){e._trigger("resize",t,r(i))},stop:function(n,s){var o=e.uiDialog.offset(),a=o.left-e.document.scrollLeft(),l=o.top-e.document.scrollTop();i.height=e.uiDialog.height(),i.width=e.uiDialog.width(),i.position={my:"left top",at:"left"+(a>=0?"+":"")+a+" top"+(l>=0?"+":"")+l,of:e.window},t(this).removeClass("ui-dialog-resizing"),e._unblockFrames(),e._trigger("resizeStop",n,r(s))}}).css("position",s)},_trackFocus:function(){this._on(this.widget(),{focusin:function(e){this._makeFocusTarget(),this._focusedElement=t(e.target)}})},_makeFocusTarget:function(){this._untrackInstance(),this._trackingInstances().unshift(this)},_untrackInstance:function(){var e=this._trackingInstances(),i=t.inArray(this,e);-1!==i&&e.splice(i,1)},_trackingInstances:function(){var t=this.document.data("ui-dialog-instances");return t||(t=[],this.document.data("ui-dialog-instances",t)),t},_minHeight:function(){var t=this.options;return"auto"===t.height?t.minHeight:Math.min(t.minHeight,t.height)},_position:function(){var t=this.uiDialog.is(":visible");t||this.uiDialog.show(),this.uiDialog.position(this.options.position),t||this.uiDialog.hide()},_setOptions:function(e){var i=this,n=!1,s={};t.each(e,function(t,e){i._setOption(t,e),t in i.sizeRelatedOptions&&(n=!0),t in i.resizableRelatedOptions&&(s[t]=e)}),n&&(this._size(),this._position()),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",s)},_setOption:function(t,e){var i,n,s=this.uiDialog;"dialogClass"===t&&s.removeClass(this.options.dialogClass).addClass(e),"disabled"!==t&&(this._super(t,e),"appendTo"===t&&this.uiDialog.appendTo(this._appendTo()),"buttons"===t&&this._createButtons(),"closeText"===t&&this.uiDialogTitlebarClose.button({label:""+e}),"draggable"===t&&((i=s.is(":data(ui-draggable)"))&&!e&&s.draggable("destroy"),!i&&e&&this._makeDraggable()),"position"===t&&this._position(),"resizable"===t&&((n=s.is(":data(ui-resizable)"))&&!e&&s.resizable("destroy"),n&&"string"==typeof e&&s.resizable("option","handles",e),n||!1===e||this._makeResizable()),"title"===t&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title")))},_size:function(){var t,e,i,n=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0}),n.minWidth>n.width&&(n.width=n.minWidth),t=this.uiDialog.css({height:"auto",width:n.width}).outerHeight(),e=Math.max(0,n.minHeight-t),i="number"==typeof n.maxHeight?Math.max(0,n.maxHeight-t):"none","auto"===n.height?this.element.css({minHeight:e,maxHeight:i,height:"auto"}):this.element.height(Math.max(0,n.height-t)),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map(function(){var e=t(this);return t("<div>").css({position:"absolute",width:e.outerWidth(),height:e.outerHeight()}).appendTo(e.parent()).offset(e.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(e){return!!t(e.target).closest(".ui-dialog").length||!!t(e.target).closest(".ui-datepicker").length},_createOverlay:function(){if(this.options.modal){var e=!0;this._delay(function(){e=!1}),this.document.data("ui-dialog-overlays")||this._on(this.document,{focusin:function(t){e||this._allowInteraction(t)||(t.preventDefault(),this._trackingInstances()[0]._focusTabbable())}}),this.overlay=t("<div>").addClass("ui-widget-overlay ui-front").appendTo(this._appendTo()),this._on(this.overlay,{mousedown:"_keepFocus"}),this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1)}},_destroyOverlay:function(){if(this.options.modal&&this.overlay){var t=this.document.data("ui-dialog-overlays")-1;t?this.document.data("ui-dialog-overlays",t):this.document.unbind("focusin").removeData("ui-dialog-overlays"),this.overlay.remove(),this.overlay=null}}}),t.widget("ui.progressbar",{version:"1.11.4",options:{max:100,value:0,change:null,complete:null},min:0,_create:function(){this.oldValue=this.options.value=this._constrainedValue(),this.element.addClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").attr({role:"progressbar","aria-valuemin":this.min}),this.valueDiv=t("<div class='ui-progressbar-value ui-widget-header ui-corner-left'></div>").appendTo(this.element),this._refreshValue()},_destroy:function(){this.element.removeClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow"),this.valueDiv.remove()},value:function(t){if(void 0===t)return this.options.value;this.options.value=this._constrainedValue(t),this._refreshValue()},_constrainedValue:function(t){return void 0===t&&(t=this.options.value),this.indeterminate=!1===t,"number"!=typeof t&&(t=0),!this.indeterminate&&Math.min(this.options.max,Math.max(this.min,t))},_setOptions:function(t){var e=t.value;delete t.value,this._super(t),this.options.value=this._constrainedValue(e),this._refreshValue()},_setOption:function(t,e){"max"===t&&(e=Math.max(this.min,e)),"disabled"===t&&this.element.toggleClass("ui-state-disabled",!!e).attr("aria-disabled",e),this._super(t,e)},_percentage:function(){return this.indeterminate?100:100*(this.options.value-this.min)/(this.options.max-this.min)},_refreshValue:function(){var e=this.options.value,i=this._percentage();this.valueDiv.toggle(this.indeterminate||e>this.min).toggleClass("ui-corner-right",e===this.options.max).width(i.toFixed(0)+"%"),this.element.toggleClass("ui-progressbar-indeterminate",this.indeterminate),this.indeterminate?(this.element.removeAttr("aria-valuenow"),this.overlayDiv||(this.overlayDiv=t("<div class='ui-progressbar-overlay'></div>").appendTo(this.valueDiv))):(this.element.attr({"aria-valuemax":this.options.max,"aria-valuenow":e}),this.overlayDiv&&(this.overlayDiv.remove(),this.overlayDiv=null)),this.oldValue!==e&&(this.oldValue=e,this._trigger("change")),e===this.options.max&&this._trigger("complete")}}),t.widget("ui.selectmenu",{version:"1.11.4",defaultElement:"<select>",options:{appendTo:null,disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:null,change:null,close:null,focus:null,open:null,select:null},_create:function(){var t=this.element.uniqueId().attr("id");this.ids={element:t,button:t+"-button",menu:t+"-menu"},this._drawButton(),this._drawMenu(),this.options.disabled&&this.disable()},_drawButton:function(){var e=this;this.label=t("label[for='"+this.ids.element+"']").attr("for",this.ids.button),this._on(this.label,{click:function(t){this.button.focus(),t.preventDefault()}}),this.element.hide(),this.button=t("<span>",{class:"ui-selectmenu-button ui-widget ui-state-default ui-corner-all",tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true"}).insertAfter(this.element),t("<span>",{class:"ui-icon "+this.options.icons.button}).prependTo(this.button),this.buttonText=t("<span>",{class:"ui-selectmenu-text"}).appendTo(this.button),this._setText(this.buttonText,this.element.find("option:selected").text()),this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",function(){e.menuItems||e._refreshMenu()}),this._hoverable(this.button),this._focusable(this.button)},_drawMenu:function(){var e=this;this.menu=t("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=t("<div>",{class:"ui-selectmenu-menu ui-front"}).append(this.menu).appendTo(this._appendTo()),this.menuInstance=this.menu.menu({role:"listbox",select:function(t,i){t.preventDefault(),e._setSelection(),e._select(i.item.data("ui-selectmenu-item"),t)},focus:function(t,i){var n=i.item.data("ui-selectmenu-item");null!=e.focusIndex&&n.index!==e.focusIndex&&(e._trigger("focus",t,{item:n}),e.isOpen||e._select(n,t)),e.focusIndex=n.index,e.button.attr("aria-activedescendant",e.menuItems.eq(n.index).attr("id"))}}).menu("instance"),this.menu.addClass("ui-corner-bottom").removeClass("ui-corner-all"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this._setText(this.buttonText,this._getSelectedItem().text()),this.options.width||this._resizeButton()},_refreshMenu:function(){this.menu.empty();var t,e=this.element.find("option");e.length&&(this._parseOptions(e),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup"),t=this._getSelectedItem(),this.menuInstance.focus(null,t),this._setAria(t.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(t){this.options.disabled||(this.menuItems?(this.menu.find(".ui-state-focus").removeClass("ui-state-focus"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",t))},_position:function(){this.menuWrap.position(t.extend({of:this.button},this.options.position))},close:function(t){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",t))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderMenu:function(e,i){var n=this,s="";t.each(i,function(i,o){o.optgroup!==s&&(t("<li>",{class:"ui-selectmenu-optgroup ui-menu-divider"+(o.element.parent("optgroup").prop("disabled")?" ui-state-disabled":""),text:o.optgroup}).appendTo(e),s=o.optgroup),n._renderItemData(e,o)})},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-selectmenu-item",e)},_renderItem:function(e,i){var n=t("<li>");return i.disabled&&n.addClass("ui-state-disabled"),this._setText(n,i.label),n.appendTo(e)},_setText:function(t,e){e?t.text(e):t.html("&#160;")},_move:function(t,e){var i,n,s=".ui-menu-item";this.isOpen?i=this.menuItems.eq(this.focusIndex):(i=this.menuItems.eq(this.element[0].selectedIndex),s+=":not(.ui-state-disabled)"),(n="first"===t||"last"===t?i["first"===t?"prevAll":"nextAll"](s).eq(-1):i[t+"All"](s).eq(0)).length&&this.menuInstance.focus(e,n)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex)},_toggle:function(t){this[this.isOpen?"close":"open"](t)},_setSelection:function(){var t;this.range&&(window.getSelection?((t=window.getSelection()).removeAllRanges(),t.addRange(this.range)):this.range.select(),this.button.focus())},_documentClick:{mousedown:function(e){this.isOpen&&(t(e.target).closest(".ui-selectmenu-menu, #"+this.ids.button).length||this.close(e))}},_buttonEvents:{mousedown:function(){var t;window.getSelection?(t=window.getSelection()).rangeCount&&(this.range=t.getRangeAt(0)):this.range=document.selection.createRange()},click:function(t){this._setSelection(),this._toggle(t)},keydown:function(e){var i=!0;switch(e.keyCode){case t.ui.keyCode.TAB:case t.ui.keyCode.ESCAPE:this.close(e),i=!1;break;case t.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(e);break;case t.ui.keyCode.UP:e.altKey?this._toggle(e):this._move("prev",e);break;case t.ui.keyCode.DOWN:e.altKey?this._toggle(e):this._move("next",e);break;case t.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(e):this._toggle(e);break;case t.ui.keyCode.LEFT:this._move("prev",e);break;case t.ui.keyCode.RIGHT:this._move("next",e);break;case t.ui.keyCode.HOME:case t.ui.keyCode.PAGE_UP:this._move("first",e);break;case t.ui.keyCode.END:case t.ui.keyCode.PAGE_DOWN:this._move("last",e);break;default:this.menu.trigger(e),i=!1}i&&e.preventDefault()}},_selectFocusedItem:function(t){var e=this.menuItems.eq(this.focusIndex);e.hasClass("ui-state-disabled")||this._select(e.data("ui-selectmenu-item"),t)},_select:function(t,e){var i=this.element[0].selectedIndex;this.element[0].selectedIndex=t.index,this._setText(this.buttonText,t.label),this._setAria(t),this._trigger("select",e,{item:t}),t.index!==i&&this._trigger("change",e,{item:t}),this.close(e)},_setAria:function(t){var e=this.menuItems.eq(t.index).attr("id");this.button.attr({"aria-labelledby":e,"aria-activedescendant":e}),this.menu.attr("aria-activedescendant",e)},_setOption:function(t,e){"icons"===t&&this.button.find("span.ui-icon").removeClass(this.options.icons.button).addClass(e.button),this._super(t,e),"appendTo"===t&&this.menuWrap.appendTo(this._appendTo()),"disabled"===t&&(this.menuInstance.option("disabled",e),this.button.toggleClass("ui-state-disabled",e).attr("aria-disabled",e),this.element.prop("disabled",e),e?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)),"width"===t&&this._resizeButton()},_appendTo:function(){var e=this.options.appendTo;return e&&(e=e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)),e&&e[0]||(e=this.element.closest(".ui-front")),e.length||(e=this.document[0].body),e},_toggleAttr:function(){this.button.toggleClass("ui-corner-top",this.isOpen).toggleClass("ui-corner-all",!this.isOpen).attr("aria-expanded",this.isOpen),this.menuWrap.toggleClass("ui-selectmenu-open",this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var t=this.options.width;t||(t=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(t)},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){return{disabled:this.element.prop("disabled")}},_parseOptions:function(e){var i=[];e.each(function(e,n){var s=t(n),o=s.parent("optgroup");i.push({element:s,index:e,value:s.val(),label:s.text(),optgroup:o.attr("label")||"",disabled:o.prop("disabled")||s.prop("disabled")})}),this.items=i},_destroy:function(){this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.label.attr("for",this.ids.element)}}),t.widget("ui.slider",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"slide",options:{animate:!1,distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this.element.addClass("ui-slider ui-slider-"+this.orientation+" ui-widget ui-widget-content ui-corner-all"),this._refresh(),this._setOption("disabled",this.options.disabled),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,i,n=this.options,s=this.element.find(".ui-slider-handle").addClass("ui-state-default ui-corner-all"),o=[];for(i=n.values&&n.values.length||1,s.length>i&&(s.slice(i).remove(),s=s.slice(0,i)),e=s.length;e<i;e++)o.push("<span class='ui-slider-handle ui-state-default ui-corner-all' tabindex='0'></span>");this.handles=s.add(t(o.join("")).appendTo(this.element)),this.handle=this.handles.eq(0),this.handles.each(function(e){t(this).data("ui-slider-handle-index",e)})},_createRange:function(){var e=this.options,i="";e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:t.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?this.range.removeClass("ui-slider-range-min ui-slider-range-max").css({left:"",bottom:""}):(this.range=t("<div></div>").appendTo(this.element),i="ui-slider-range ui-widget-header ui-corner-all"),this.range.addClass(i+("min"===e.range||"max"===e.range?" ui-slider-range-"+e.range:""))):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this.element.removeClass("ui-slider ui-slider-horizontal ui-slider-vertical ui-widget ui-widget-content ui-corner-all"),this._mouseDestroy()},_mouseCapture:function(e){var i,n,s,o,r,a,l,h=this,c=this.options;return!c.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),i={x:e.pageX,y:e.pageY},n=this._normValueFromMouse(i),s=this._valueMax()-this._valueMin()+1,this.handles.each(function(e){var i=Math.abs(n-h.values(e));(s>i||s===i&&(e===h._lastChangedValue||h.values(e)===c.min))&&(s=i,o=t(this),r=e)}),!1!==this._start(e,r)&&(this._mouseSliding=!0,this._handleIndex=r,o.addClass("ui-state-active").focus(),a=o.offset(),l=!t(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=l?{left:0,top:0}:{left:e.pageX-a.left-o.width()/2,top:e.pageY-a.top-o.height()/2-(parseInt(o.css("borderTopWidth"),10)||0)-(parseInt(o.css("borderBottomWidth"),10)||0)+(parseInt(o.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(e,r,n),this._animateOff=!0,!0))},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY},i=this._normValueFromMouse(e);return this._slide(t,this._handleIndex,i),!1},_mouseStop:function(t){return this.handles.removeClass("ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1,!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e,i,n,s,o;return"horizontal"===this.orientation?(e=this.elementSize.width,i=t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,i=t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)),(n=i/e)>1&&(n=1),n<0&&(n=0),"vertical"===this.orientation&&(n=1-n),s=this._valueMax()-this._valueMin(),o=this._valueMin()+n*s,this._trimAlignValue(o)},_start:function(t,e){var i={handle:this.handles[e],value:this.value()};return this.options.values&&this.options.values.length&&(i.value=this.values(e),i.values=this.values()),this._trigger("start",t,i)},_slide:function(t,e,i){var n,s,o;this.options.values&&this.options.values.length?(n=this.values(e?0:1),2===this.options.values.length&&!0===this.options.range&&(0===e&&i>n||1===e&&i<n)&&(i=n),i!==this.values(e)&&((s=this.values())[e]=i,o=this._trigger("slide",t,{handle:this.handles[e],value:i,values:s}),n=this.values(e?0:1),!1!==o&&this.values(e,i))):i!==this.value()&&!1!==(o=this._trigger("slide",t,{handle:this.handles[e],value:i}))&&this.value(i)},_stop:function(t,e){var i={handle:this.handles[e],value:this.value()};this.options.values&&this.options.values.length&&(i.value=this.values(e),i.values=this.values()),this._trigger("stop",t,i)},_change:function(t,e){if(!this._keySliding&&!this._mouseSliding){var i={handle:this.handles[e],value:this.value()};this.options.values&&this.options.values.length&&(i.value=this.values(e),i.values=this.values()),this._lastChangedValue=e,this._trigger("change",t,i)}},value:function(t){return arguments.length?(this.options.value=this._trimAlignValue(t),this._refreshValue(),void this._change(null,0)):this._value()},values:function(e,i){var n,s,o;if(arguments.length>1)return this.options.values[e]=this._trimAlignValue(i),this._refreshValue(),void this._change(null,e);if(!arguments.length)return this._values();if(!t.isArray(arguments[0]))return this.options.values&&this.options.values.length?this._values(e):this.value();for(n=this.options.values,s=arguments[0],o=0;o<n.length;o+=1)n[o]=this._trimAlignValue(s[o]),this._change(null,o);this._refreshValue()},_setOption:function(e,i){var n,s=0;switch("range"===e&&!0===this.options.range&&("min"===i?(this.options.value=this._values(0),this.options.values=null):"max"===i&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),t.isArray(this.options.values)&&(s=this.options.values.length),"disabled"===e&&this.element.toggleClass("ui-state-disabled",!!i),this._super(e,i),e){case"orientation":this._detectOrientation(),this.element.removeClass("ui-slider-horizontal ui-slider-vertical").addClass("ui-slider-"+this.orientation),this._refreshValue(),this.handles.css("horizontal"===i?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),n=0;n<s;n+=1)this._change(null,n);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_value:function(){var t=this.options.value;return this._trimAlignValue(t)},_values:function(t){var e,i,n;if(arguments.length)return e=this.options.values[t],this._trimAlignValue(e);if(this.options.values&&this.options.values.length){for(i=this.options.values.slice(),n=0;n<i.length;n+=1)i[n]=this._trimAlignValue(i[n]);return i}return[]},_trimAlignValue:function(t){if(t<=this._valueMin())return this._valueMin();if(t>=this._valueMax())return this._valueMax();var e=this.options.step>0?this.options.step:1,i=(t-this._valueMin())%e,n=t-i;return 2*Math.abs(i)>=e&&(n+=i>0?e:-e),parseFloat(n.toFixed(5))},_calculateNewMax:function(){var t=this.options.max,e=this._valueMin(),i=this.options.step;t=Math.floor(+(t-e).toFixed(this._precision())/i)*i+e,this.max=parseFloat(t.toFixed(this._precision()))},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min&&(t=Math.max(t,this._precisionOf(this.options.min))),t},_precisionOf:function(t){var e=t.toString(),i=e.indexOf(".");return-1===i?0:e.length-i-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshValue:function(){var e,i,n,s,o,r=this.options.range,a=this.options,l=this,h=!this._animateOff&&a.animate,c={};this.options.values&&this.options.values.length?this.handles.each(function(n){i=(l.values(n)-l._valueMin())/(l._valueMax()-l._valueMin())*100,c["horizontal"===l.orientation?"left":"bottom"]=i+"%",t(this).stop(1,1)[h?"animate":"css"](c,a.animate),!0===l.options.range&&("horizontal"===l.orientation?(0===n&&l.range.stop(1,1)[h?"animate":"css"]({left:i+"%"},a.animate),1===n&&l.range[h?"animate":"css"]({width:i-e+"%"},{queue:!1,duration:a.animate})):(0===n&&l.range.stop(1,1)[h?"animate":"css"]({bottom:i+"%"},a.animate),1===n&&l.range[h?"animate":"css"]({height:i-e+"%"},{queue:!1,duration:a.animate}))),e=i}):(n=this.value(),s=this._valueMin(),o=this._valueMax(),i=o!==s?(n-s)/(o-s)*100:0,c["horizontal"===this.orientation?"left":"bottom"]=i+"%",this.handle.stop(1,1)[h?"animate":"css"](c,a.animate),"min"===r&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:i+"%"},a.animate),"max"===r&&"horizontal"===this.orientation&&this.range[h?"animate":"css"]({width:100-i+"%"},{queue:!1,duration:a.animate}),"min"===r&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:i+"%"},a.animate),"max"===r&&"vertical"===this.orientation&&this.range[h?"animate":"css"]({height:100-i+"%"},{queue:!1,duration:a.animate}))},_handleEvents:{keydown:function(e){var i,n,s,o=t(e.target).data("ui-slider-handle-index");switch(e.keyCode){case t.ui.keyCode.HOME:case t.ui.keyCode.END:case t.ui.keyCode.PAGE_UP:case t.ui.keyCode.PAGE_DOWN:case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(e.preventDefault(),!this._keySliding&&(this._keySliding=!0,t(e.target).addClass("ui-state-active"),!1===this._start(e,o)))return}switch(s=this.options.step,i=n=this.options.values&&this.options.values.length?this.values(o):this.value(),e.keyCode){case t.ui.keyCode.HOME:n=this._valueMin();break;case t.ui.keyCode.END:n=this._valueMax();break;case t.ui.keyCode.PAGE_UP:n=this._trimAlignValue(i+(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.PAGE_DOWN:n=this._trimAlignValue(i-(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:if(i===this._valueMax())return;n=this._trimAlignValue(i+s);break;case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(i===this._valueMin())return;n=this._trimAlignValue(i-s)}this._slide(e,o,n)},keyup:function(e){var i=t(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,i),this._change(e,i),t(e.target).removeClass("ui-state-active"))}}}),t.widget("ui.spinner",{version:"1.11.4",defaultElement:"<input>",widgetEventPrefix:"spin",options:{culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var e={},i=this.element;return t.each(["min","max","step"],function(t,n){var s=i.attr(n);void 0!==s&&s.length&&(e[n]=s)}),e},_events:{keydown:function(t){this._start(t)&&this._keydown(t)&&t.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(t){this.cancelBlur?delete this.cancelBlur:(this._stop(),this._refresh(),this.previous!==this.element.val()&&this._trigger("change",t))},mousewheel:function(t,e){if(e){if(!this.spinning&&!this._start(t))return!1;this._spin((e>0?1:-1)*this.options.step,t),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay(function(){this.spinning&&this._stop(t)},100),t.preventDefault()}},"mousedown .ui-spinner-button":function(e){var i;function n(){this.element[0]===this.document[0].activeElement||(this.element.focus(),this.previous=i,this._delay(function(){this.previous=i}))}i=this.element[0]===this.document[0].activeElement?this.previous:this.element.val(),e.preventDefault(),n.call(this),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur,n.call(this)}),!1!==this._start(e)&&this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(e){if(t(e.currentTarget).hasClass("ui-state-active"))return!1!==this._start(e)&&void this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseleave .ui-spinner-button":"_stop"},_draw:function(){var t=this.uiSpinner=this.element.addClass("ui-spinner-input").attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml());this.element.attr("role","spinbutton"),this.buttons=t.find(".ui-spinner-button").attr("tabIndex",-1).button().removeClass("ui-corner-all"),this.buttons.height()>Math.ceil(.5*t.height())&&t.height()>0&&t.height(t.height()),this.options.disabled&&this.disable()},_keydown:function(e){var i=this.options,n=t.ui.keyCode;switch(e.keyCode){case n.UP:return this._repeat(null,1,e),!0;case n.DOWN:return this._repeat(null,-1,e),!0;case n.PAGE_UP:return this._repeat(null,i.page,e),!0;case n.PAGE_DOWN:return this._repeat(null,-i.page,e),!0}return!1},_uiSpinnerHtml:function(){return"<span class='ui-spinner ui-widget ui-widget-content ui-corner-all'></span>"},_buttonHtml:function(){return"<a class='ui-spinner-button ui-spinner-up ui-corner-tr'><span class='ui-icon "+this.options.icons.up+"'>&#9650;</span></a><a class='ui-spinner-button ui-spinner-down ui-corner-br'><span class='ui-icon "+this.options.icons.down+"'>&#9660;</span></a>"},_start:function(t){return!(!this.spinning&&!1===this._trigger("start",t)||(this.counter||(this.counter=1),this.spinning=!0,0))},_repeat:function(t,e,i){t=t||500,clearTimeout(this.timer),this.timer=this._delay(function(){this._repeat(40,e,i)},t),this._spin(e*this.options.step,i)},_spin:function(t,e){var i=this.value()||0;this.counter||(this.counter=1),i=this._adjustValue(i+t*this._increment(this.counter)),this.spinning&&!1===this._trigger("spin",e,{value:i})||(this._value(i),this.counter++)},_increment:function(e){var i=this.options.incremental;return i?t.isFunction(i)?i(e):Math.floor(e*e*e/5e4-e*e/500+17*e/200+1):1},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min&&(t=Math.max(t,this._precisionOf(this.options.min))),t},_precisionOf:function(t){var e=t.toString(),i=e.indexOf(".");return-1===i?0:e.length-i-1},_adjustValue:function(t){var e,i,n=this.options;return i=t-(e=null!==n.min?n.min:0),t=e+(i=Math.round(i/n.step)*n.step),t=parseFloat(t.toFixed(this._precision())),null!==n.max&&t>n.max?n.max:null!==n.min&&t<n.min?n.min:t},_stop:function(t){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",t))},_setOption:function(t,e){if("culture"===t||"numberFormat"===t){var i=this._parse(this.element.val());return this.options[t]=e,void this.element.val(this._format(i))}"max"!==t&&"min"!==t&&"step"!==t||"string"==typeof e&&(e=this._parse(e)),"icons"===t&&(this.buttons.first().find(".ui-icon").removeClass(this.options.icons.up).addClass(e.up),this.buttons.last().find(".ui-icon").removeClass(this.options.icons.down).addClass(e.down)),this._super(t,e),"disabled"===t&&(this.widget().toggleClass("ui-state-disabled",!!e),this.element.prop("disabled",!!e),this.buttons.button(e?"disable":"enable"))},_setOptions:w(function(t){this._super(t)}),_parse:function(t){return"string"==typeof t&&""!==t&&(t=window.Globalize&&this.options.numberFormat?Globalize.parseFloat(t,10,this.options.culture):+t),""===t||isNaN(t)?null:t},_format:function(t){return""===t?"":window.Globalize&&this.options.numberFormat?Globalize.format(t,this.options.numberFormat,this.options.culture):t},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var t=this.value();return null!==t&&t===this._adjustValue(t)},_value:function(t,e){var i;""!==t&&null!==(i=this._parse(t))&&(e||(i=this._adjustValue(i)),t=this._format(i)),this.element.val(t),this._refresh()},_destroy:function(){this.element.removeClass("ui-spinner-input").prop("disabled",!1).removeAttr("autocomplete").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:w(function(t){this._stepUp(t)}),_stepUp:function(t){this._start()&&(this._spin((t||1)*this.options.step),this._stop())},stepDown:w(function(t){this._stepDown(t)}),_stepDown:function(t){this._start()&&(this._spin((t||1)*-this.options.step),this._stop())},pageUp:w(function(t){this._stepUp((t||1)*this.options.page)}),pageDown:w(function(t){this._stepDown((t||1)*this.options.page)}),value:function(t){if(!arguments.length)return this._parse(this.element.val());w(this._value).call(this,t)},widget:function(){return this.uiSpinner}}),t.widget("ui.tabs",{version:"1.11.4",delay:300,options:{active:null,collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_isLocal:(x=/#.*$/,function(t){var e,i;e=(t=t.cloneNode(!1)).href.replace(x,""),i=location.href.replace(x,"");try{e=decodeURIComponent(e)}catch(t){}try{i=decodeURIComponent(i)}catch(t){}return t.hash.length>1&&e===i}),_create:function(){var e=this,i=this.options;this.running=!1,this.element.addClass("ui-tabs ui-widget ui-widget-content ui-corner-all").toggleClass("ui-tabs-collapsible",i.collapsible),this._processTabs(),i.active=this._initialActive(),t.isArray(i.disabled)&&(i.disabled=t.unique(i.disabled.concat(t.map(this.tabs.filter(".ui-state-disabled"),function(t){return e.tabs.index(t)}))).sort()),!1!==this.options.active&&this.anchors.length?this.active=this._findActive(i.active):this.active=t(),this._refresh(),this.active.length&&this.load(i.active)},_initialActive:function(){var e=this.options.active,i=this.options.collapsible,n=location.hash.substring(1);return null===e&&(n&&this.tabs.each(function(i,s){if(t(s).attr("aria-controls")===n)return e=i,!1}),null===e&&(e=this.tabs.index(this.tabs.filter(".ui-tabs-active"))),null!==e&&-1!==e||(e=!!this.tabs.length&&0)),!1!==e&&-1===(e=this.tabs.index(this.tabs.eq(e)))&&(e=!i&&0),!i&&!1===e&&this.anchors.length&&(e=0),e},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):t()}},_tabKeydown:function(e){var i=t(this.document[0].activeElement).closest("li"),n=this.tabs.index(i),s=!0;if(!this._handlePageNav(e)){switch(e.keyCode){case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:n++;break;case t.ui.keyCode.UP:case t.ui.keyCode.LEFT:s=!1,n--;break;case t.ui.keyCode.END:n=this.anchors.length-1;break;case t.ui.keyCode.HOME:n=0;break;case t.ui.keyCode.SPACE:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n);case t.ui.keyCode.ENTER:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n!==this.options.active&&n);default:return}e.preventDefault(),clearTimeout(this.activating),n=this._focusNextTab(n,s),e.ctrlKey||e.metaKey||(i.attr("aria-selected","false"),this.tabs.eq(n).attr("aria-selected","true"),this.activating=this._delay(function(){this.option("active",n)},this.delay))}},_panelKeydown:function(e){this._handlePageNav(e)||e.ctrlKey&&e.keyCode===t.ui.keyCode.UP&&(e.preventDefault(),this.active.focus())},_handlePageNav:function(e){return e.altKey&&e.keyCode===t.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):e.altKey&&e.keyCode===t.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(e,i){for(var n=this.tabs.length-1;-1!==t.inArray((e>n&&(e=0),e<0&&(e=n),e),this.options.disabled);)e=i?e+1:e-1;return e},_focusNextTab:function(t,e){return t=this._findNextTab(t,e),this.tabs.eq(t).focus(),t},_setOption:function(t,e){"active"!==t?"disabled"!==t?(this._super(t,e),"collapsible"===t&&(this.element.toggleClass("ui-tabs-collapsible",e),e||!1!==this.options.active||this._activate(0)),"event"===t&&this._setupEvents(e),"heightStyle"===t&&this._setupHeightStyle(e)):this._setupDisabled(e):this._activate(e)},_sanitizeSelector:function(t){return t?t.replace(/[!"$%&'()*+,.\/:;<=>?@\[\]\^`{|}~]/g,"\\$&"):""},refresh:function(){var e=this.options,i=this.tablist.children(":has(a[href])");e.disabled=t.map(i.filter(".ui-state-disabled"),function(t){return i.index(t)}),this._processTabs(),!1!==e.active&&this.anchors.length?this.active.length&&!t.contains(this.tablist[0],this.active[0])?this.tabs.length===e.disabled.length?(e.active=!1,this.active=t()):this._activate(this._findNextTab(Math.max(0,e.active-1),!1)):e.active=this.tabs.index(this.active):(e.active=!1,this.active=t()),this._refresh()},_refresh:function(){this._setupDisabled(this.options.disabled),this._setupEvents(this.options.event),this._setupHeightStyle(this.options.heightStyle),this.tabs.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}),this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-hidden":"true"}),this.active.length?(this.active.addClass("ui-tabs-active ui-state-active").attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}),this._getPanelForTab(this.active).show().attr({"aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var e=this,i=this.tabs,n=this.anchors,s=this.panels;this.tablist=this._getList().addClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").attr("role","tablist").delegate("> li","mousedown"+this.eventNamespace,function(e){t(this).is(".ui-state-disabled")&&e.preventDefault()}).delegate(".ui-tabs-anchor","focus"+this.eventNamespace,function(){t(this).closest("li").is(".ui-state-disabled")&&this.blur()}),this.tabs=this.tablist.find("> li:has(a[href])").addClass("ui-state-default ui-corner-top").attr({role:"tab",tabIndex:-1}),this.anchors=this.tabs.map(function(){return t("a",this)[0]}).addClass("ui-tabs-anchor").attr({role:"presentation",tabIndex:-1}),this.panels=t(),this.anchors.each(function(i,n){var s,o,r,a=t(n).uniqueId().attr("id"),l=t(n).closest("li"),h=l.attr("aria-controls");e._isLocal(n)?(r=(s=n.hash).substring(1),o=e.element.find(e._sanitizeSelector(s))):(s="#"+(r=l.attr("aria-controls")||t({}).uniqueId()[0].id),(o=e.element.find(s)).length||(o=e._createPanel(r)).insertAfter(e.panels[i-1]||e.tablist),o.attr("aria-live","polite")),o.length&&(e.panels=e.panels.add(o)),h&&l.data("ui-tabs-aria-controls",h),l.attr({"aria-controls":r,"aria-labelledby":a}),o.attr("aria-labelledby",a)}),this.panels.addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").attr("role","tabpanel"),i&&(this._off(i.not(this.tabs)),this._off(n.not(this.anchors)),this._off(s.not(this.panels)))},_getList:function(){return this.tablist||this.element.find("ol,ul").eq(0)},_createPanel:function(e){return t("<div>").attr("id",e).addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").data("ui-tabs-destroy",!0)},_setupDisabled:function(e){t.isArray(e)&&(e.length?e.length===this.anchors.length&&(e=!0):e=!1);for(var i,n=0;i=this.tabs[n];n++)!0===e||-1!==t.inArray(n,e)?t(i).addClass("ui-state-disabled").attr("aria-disabled","true"):t(i).removeClass("ui-state-disabled").removeAttr("aria-disabled");this.options.disabled=e},_setupEvents:function(e){var i={};e&&t.each(e.split(" "),function(t,e){i[e]="_eventHandler"}),this._off(this.anchors.add(this.tabs).add(this.panels)),this._on(!0,this.anchors,{click:function(t){t.preventDefault()}}),this._on(this.anchors,i),this._on(this.tabs,{keydown:"_tabKeydown"}),this._on(this.panels,{keydown:"_panelKeydown"}),this._focusable(this.tabs),this._hoverable(this.tabs)},_setupHeightStyle:function(e){var i,n=this.element.parent();"fill"===e?(i=n.height(),i-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each(function(){var e=t(this),n=e.css("position");"absolute"!==n&&"fixed"!==n&&(i-=e.outerHeight(!0))}),this.element.children().not(this.panels).each(function(){i-=t(this).outerHeight(!0)}),this.panels.each(function(){t(this).height(Math.max(0,i-t(this).innerHeight()+t(this).height()))}).css("overflow","auto")):"auto"===e&&(i=0,this.panels.each(function(){i=Math.max(i,t(this).height("").height())}).height(i))},_eventHandler:function(e){var i=this.options,n=this.active,s=t(e.currentTarget).closest("li"),o=s[0]===n[0],r=o&&i.collapsible,a=r?t():this._getPanelForTab(s),l=n.length?this._getPanelForTab(n):t(),h={oldTab:n,oldPanel:l,newTab:r?t():s,newPanel:a};e.preventDefault(),s.hasClass("ui-state-disabled")||s.hasClass("ui-tabs-loading")||this.running||o&&!i.collapsible||!1===this._trigger("beforeActivate",e,h)||(i.active=!r&&this.tabs.index(s),this.active=o?t():s,this.xhr&&this.xhr.abort(),l.length||a.length||t.error("jQuery UI Tabs: Mismatching fragment identifier."),a.length&&this.load(this.tabs.index(s),e),this._toggle(e,h))},_toggle:function(e,i){var n=this,s=i.newPanel,o=i.oldPanel;function r(){n.running=!1,n._trigger("activate",e,i)}function a(){i.newTab.closest("li").addClass("ui-tabs-active ui-state-active"),s.length&&n.options.show?n._show(s,n.options.show,r):(s.show(),r())}this.running=!0,o.length&&this.options.hide?this._hide(o,this.options.hide,function(){i.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),a()}):(i.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),o.hide(),a()),o.attr("aria-hidden","true"),i.oldTab.attr({"aria-selected":"false","aria-expanded":"false"}),s.length&&o.length?i.oldTab.attr("tabIndex",-1):s.length&&this.tabs.filter(function(){return 0===t(this).attr("tabIndex")}).attr("tabIndex",-1),s.attr("aria-hidden","false"),i.newTab.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_activate:function(e){var i,n=this._findActive(e);n[0]!==this.active[0]&&(n.length||(n=this.active),i=n.find(".ui-tabs-anchor")[0],this._eventHandler({target:i,currentTarget:i,preventDefault:t.noop}))},_findActive:function(e){return!1===e?t():this.tabs.eq(e)},_getIndex:function(t){return"string"==typeof t&&(t=this.anchors.index(this.anchors.filter("[href$='"+t+"']"))),t},_destroy:function(){this.xhr&&this.xhr.abort(),this.element.removeClass("ui-tabs ui-widget ui-widget-content ui-corner-all ui-tabs-collapsible"),this.tablist.removeClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").removeAttr("role"),this.anchors.removeClass("ui-tabs-anchor").removeAttr("role").removeAttr("tabIndex").removeUniqueId(),this.tablist.unbind(this.eventNamespace),this.tabs.add(this.panels).each(function(){t.data(this,"ui-tabs-destroy")?t(this).remove():t(this).removeClass("ui-state-default ui-state-active ui-state-disabled ui-corner-top ui-corner-bottom ui-widget-content ui-tabs-active ui-tabs-panel").removeAttr("tabIndex").removeAttr("aria-live").removeAttr("aria-busy").removeAttr("aria-selected").removeAttr("aria-labelledby").removeAttr("aria-hidden").removeAttr("aria-expanded").removeAttr("role")}),this.tabs.each(function(){var e=t(this),i=e.data("ui-tabs-aria-controls");i?e.attr("aria-controls",i).removeData("ui-tabs-aria-controls"):e.removeAttr("aria-controls")}),this.panels.show(),"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(e){var i=this.options.disabled;!1!==i&&(void 0===e?i=!1:(e=this._getIndex(e),i=t.isArray(i)?t.map(i,function(t){return t!==e?t:null}):t.map(this.tabs,function(t,i){return i!==e?i:null})),this._setupDisabled(i))},disable:function(e){var i=this.options.disabled;if(!0!==i){if(void 0===e)i=!0;else{if(e=this._getIndex(e),-1!==t.inArray(e,i))return;i=t.isArray(i)?t.merge([e],i).sort():[e]}this._setupDisabled(i)}},load:function(e,i){e=this._getIndex(e);var n=this,s=this.tabs.eq(e),o=s.find(".ui-tabs-anchor"),r=this._getPanelForTab(s),a={tab:s,panel:r},l=function(t,e){"abort"===e&&n.panels.stop(!1,!0),s.removeClass("ui-tabs-loading"),r.removeAttr("aria-busy"),t===n.xhr&&delete n.xhr};this._isLocal(o[0])||(this.xhr=t.ajax(this._ajaxSettings(o,i,a)),this.xhr&&"canceled"!==this.xhr.statusText&&(s.addClass("ui-tabs-loading"),r.attr("aria-busy","true"),this.xhr.done(function(t,e,s){setTimeout(function(){r.html(t),n._trigger("load",i,a),l(s,e)},1)}).fail(function(t,e){setTimeout(function(){l(t,e)},1)})))},_ajaxSettings:function(e,i,n){var s=this;return{url:e.attr("href"),beforeSend:function(e,o){return s._trigger("beforeLoad",i,t.extend({jqXHR:e,ajaxSettings:o},n))}}},_getPanelForTab:function(e){var i=t(e).attr("aria-controls");return this.element.find(this._sanitizeSelector("#"+i))}});var x,C,k=t;t.effects={effect:{}},function(t,e){var i,n=/^([\-+])=\s*(\d+\.?\d*)/,s=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[t[1],t[2],t[3],t[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[2.55*t[1],2.55*t[2],2.55*t[3],t[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})/,parse:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])/,parse:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(t){return[t[1],t[2]/100,t[3]/100,t[4]]}}],o=t.Color=function(e,i,n,s){return new t.Color.fn.parse(e,i,n,s)},r={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},a={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},l=o.support={},h=t("<p>")[0],c=t.each;function u(t,e,i){var n=a[e.type]||{};return null==t?i||!e.def?null:e.def:(t=n.floor?~~t:parseFloat(t),isNaN(t)?e.def:n.mod?(t+n.mod)%n.mod:0>t?0:n.max<t?n.max:t)}function d(e){var n=o(),a=n._rgba=[];return e=e.toLowerCase(),c(s,function(t,i){var s,o=i.re.exec(e),l=o&&i.parse(o),h=i.space||"rgba";if(l)return s=n[h](l),n[r[h].cache]=s[r[h].cache],a=n._rgba=s._rgba,!1}),a.length?("0,0,0,0"===a.join()&&t.extend(a,i.transparent),n):i[e]}function p(t,e,i){return 6*(i=(i+1)%1)<1?t+(e-t)*i*6:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t}h.style.cssText="background-color:rgba(1,1,1,.5)",l.rgba=h.style.backgroundColor.indexOf("rgba")>-1,c(r,function(t,e){e.cache="_"+t,e.props.alpha={idx:3,type:"percent",def:1}}),o.fn=t.extend(o.prototype,{parse:function(e,n,s,a){if(void 0===e)return this._rgba=[null,null,null,null],this;(e.jquery||e.nodeType)&&(e=t(e).css(n),n=void 0);var l=this,h=t.type(e),p=this._rgba=[];return void 0!==n&&(e=[e,n,s,a],h="array"),"string"===h?this.parse(d(e)||i._default):"array"===h?(c(r.rgba.props,function(t,i){p[i.idx]=u(e[i.idx],i)}),this):"object"===h?(c(r,e instanceof o?function(t,i){e[i.cache]&&(l[i.cache]=e[i.cache].slice())}:function(i,n){var s=n.cache;c(n.props,function(t,i){if(!l[s]&&n.to){if("alpha"===t||null==e[t])return;l[s]=n.to(l._rgba)}l[s][i.idx]=u(e[t],i,!0)}),l[s]&&t.inArray(null,l[s].slice(0,3))<0&&(l[s][3]=1,n.from&&(l._rgba=n.from(l[s])))}),this):void 0},is:function(t){var e=o(t),i=!0,n=this;return c(r,function(t,s){var o,r=e[s.cache];return r&&(o=n[s.cache]||s.to&&s.to(n._rgba)||[],c(s.props,function(t,e){if(null!=r[e.idx])return i=r[e.idx]===o[e.idx]})),i}),i},_space:function(){var t=[],e=this;return c(r,function(i,n){e[n.cache]&&t.push(i)}),t.pop()},transition:function(t,e){var i=o(t),n=i._space(),s=r[n],l=0===this.alpha()?o("transparent"):this,h=l[s.cache]||s.to(l._rgba),d=h.slice();return i=i[s.cache],c(s.props,function(t,n){var s=n.idx,o=h[s],r=i[s],l=a[n.type]||{};null!==r&&(null===o?d[s]=r:(l.mod&&(r-o>l.mod/2?o+=l.mod:o-r>l.mod/2&&(o-=l.mod)),d[s]=u((r-o)*e+o,n)))}),this[n](d)},blend:function(e){if(1===this._rgba[3])return this;var i=this._rgba.slice(),n=i.pop(),s=o(e)._rgba;return o(t.map(i,function(t,e){return(1-n)*s[e]+n*t}))},toRgbaString:function(){var e="rgba(",i=t.map(this._rgba,function(t,e){return null==t?e>2?1:0:t});return 1===i[3]&&(i.pop(),e="rgb("),e+i.join()+")"},toHslaString:function(){var e="hsla(",i=t.map(this.hsla(),function(t,e){return null==t&&(t=e>2?1:0),e&&e<3&&(t=Math.round(100*t)+"%"),t});return 1===i[3]&&(i.pop(),e="hsl("),e+i.join()+")"},toHexString:function(e){var i=this._rgba.slice(),n=i.pop();return e&&i.push(~~(255*n)),"#"+t.map(i,function(t){return 1===(t=(t||0).toString(16)).length?"0"+t:t}).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}}),o.fn.parse.prototype=o.fn,r.hsla.to=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e,i,n=t[0]/255,s=t[1]/255,o=t[2]/255,r=t[3],a=Math.max(n,s,o),l=Math.min(n,s,o),h=a-l,c=a+l,u=.5*c;return e=l===a?0:n===a?60*(s-o)/h+360:s===a?60*(o-n)/h+120:60*(n-s)/h+240,i=0===h?0:u<=.5?h/c:h/(2-c),[Math.round(e)%360,i,u,null==r?1:r]},r.hsla.from=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e=t[0]/360,i=t[1],n=t[2],s=t[3],o=n<=.5?n*(1+i):n+i-n*i,r=2*n-o;return[Math.round(255*p(r,o,e+1/3)),Math.round(255*p(r,o,e)),Math.round(255*p(r,o,e-1/3)),s]},c(r,function(e,i){var s=i.props,r=i.cache,a=i.to,l=i.from;o.fn[e]=function(e){if(a&&!this[r]&&(this[r]=a(this._rgba)),void 0===e)return this[r].slice();var i,n=t.type(e),h="array"===n||"object"===n?e:arguments,d=this[r].slice();return c(s,function(t,e){var i=h["object"===n?t:e.idx];null==i&&(i=d[e.idx]),d[e.idx]=u(i,e)}),l?((i=o(l(d)))[r]=d,i):o(d)},c(s,function(i,s){o.fn[i]||(o.fn[i]=function(o){var r,a=t.type(o),l="alpha"===i?this._hsla?"hsla":"rgba":e,h=this[l](),c=h[s.idx];return"undefined"===a?c:("function"===a&&(o=o.call(this,c),a=t.type(o)),null==o&&s.empty?this:("string"===a&&(r=n.exec(o))&&(o=c+parseFloat(r[2])*("+"===r[1]?1:-1)),h[s.idx]=o,this[l](h)))})})}),o.hook=function(e){var i=e.split(" ");c(i,function(e,i){t.cssHooks[i]={set:function(e,n){var s,r,a="";if("transparent"!==n&&("string"!==t.type(n)||(s=d(n)))){if(n=o(s||n),!l.rgba&&1!==n._rgba[3]){for(r="backgroundColor"===i?e.parentNode:e;(""===a||"transparent"===a)&&r&&r.style;)try{a=t.css(r,"backgroundColor"),r=r.parentNode}catch(t){}n=n.blend(a&&"transparent"!==a?a:"_default")}n=n.toRgbaString()}try{e.style[i]=n}catch(t){}}},t.fx.step[i]=function(e){e.colorInit||(e.start=o(e.elem,i),e.end=o(e.end),e.colorInit=!0),t.cssHooks[i].set(e.elem,e.start.transition(e.end,e.pos))}})},o.hook("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor"),t.cssHooks.borderColor={expand:function(t){var e={};return c(["Top","Right","Bottom","Left"],function(i,n){e["border"+n+"Color"]=t}),e}},i=t.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}}(k),function(){var e,i,n,s=["add","remove","toggle"],o={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};function r(e){var i,n,s=e.ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,null):e.currentStyle,o={};if(s&&s.length&&s[0]&&s[s[0]])for(n=s.length;n--;)"string"==typeof s[i=s[n]]&&(o[t.camelCase(i)]=s[i]);else for(i in s)"string"==typeof s[i]&&(o[i]=s[i]);return o}t.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(e,i){t.fx.step[i]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(k.style(t.elem,i,t.end),t.setAttr=!0)}}),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t.effects.animateClass=function(e,i,n,a){var l=t.speed(i,n,a);return this.queue(function(){var i,n=t(this),a=n.attr("class")||"",h=l.children?n.find("*").addBack():n;h=h.map(function(){return{el:t(this),start:r(this)}}),(i=function(){t.each(s,function(t,i){e[i]&&n[i+"Class"](e[i])})})(),h=h.map(function(){return this.end=r(this.el[0]),this.diff=function(e,i){var n,s,r={};for(n in i)s=i[n],e[n]!==s&&(o[n]||!t.fx.step[n]&&isNaN(parseFloat(s))||(r[n]=s));return r}(this.start,this.end),this}),n.attr("class",a),h=h.map(function(){var e=this,i=t.Deferred(),n=t.extend({},l,{queue:!1,complete:function(){i.resolve(e)}});return this.el.animate(this.diff,n),i.promise()}),t.when.apply(t,h.get()).done(function(){i(),t.each(arguments,function(){var e=this.el;t.each(this.diff,function(t){e.css(t,"")})}),l.complete.call(n[0])})})},t.fn.extend({addClass:(e=t.fn.addClass,function(i,n,s,o){return n?t.effects.animateClass.call(this,{add:i},n,s,o):e.apply(this,arguments)}),removeClass:(n=t.fn.removeClass,function(e,i,s,o){return arguments.length>1?t.effects.animateClass.call(this,{remove:e},i,s,o):n.apply(this,arguments)}),toggleClass:(i=t.fn.toggleClass,function(e,n,s,o,r){return"boolean"==typeof n||void 0===n?s?t.effects.animateClass.call(this,n?{add:e}:{remove:e},s,o,r):i.apply(this,arguments):t.effects.animateClass.call(this,{toggle:e},n,s,o)}),switchClass:function(e,i,n,s,o){return t.effects.animateClass.call(this,{add:i,remove:e},n,s,o)}})}(),function(){function e(e,i,n,s){return t.isPlainObject(e)&&(i=e,e=e.effect),e={effect:e},null==i&&(i={}),t.isFunction(i)&&(s=i,n=null,i={}),("number"==typeof i||t.fx.speeds[i])&&(s=n,n=i,i={}),t.isFunction(n)&&(s=n,n=null),i&&t.extend(e,i),n=n||i.duration,e.duration=t.fx.off?0:"number"==typeof n?n:n in t.fx.speeds?t.fx.speeds[n]:t.fx.speeds._default,e.complete=s||i.complete,e}function i(e){return!(e&&"number"!=typeof e&&!t.fx.speeds[e])||"string"==typeof e&&!t.effects.effect[e]||!!t.isFunction(e)||"object"==typeof e&&!e.effect}var n,s,o;t.extend(t.effects,{version:"1.11.4",save:function(t,e){for(var i=0;i<e.length;i++)null!==e[i]&&t.data("ui-effects-"+e[i],t[0].style[e[i]])},restore:function(t,e){var i,n;for(n=0;n<e.length;n++)null!==e[n]&&(void 0===(i=t.data("ui-effects-"+e[n]))&&(i=""),t.css(e[n],i))},setMode:function(t,e){return"toggle"===e&&(e=t.is(":hidden")?"show":"hide"),e},getBaseline:function(t,e){var i,n;switch(t[0]){case"top":i=0;break;case"middle":i=.5;break;case"bottom":i=1;break;default:i=t[0]/e.height}switch(t[1]){case"left":n=0;break;case"center":n=.5;break;case"right":n=1;break;default:n=t[1]/e.width}return{x:n,y:i}},createWrapper:function(e){if(e.parent().is(".ui-effects-wrapper"))return e.parent();var i={width:e.outerWidth(!0),height:e.outerHeight(!0),float:e.css("float")},n=t("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),s={width:e.width(),height:e.height()},o=document.activeElement;try{o.id}catch(t){o=document.body}return e.wrap(n),(e[0]===o||t.contains(e[0],o))&&t(o).focus(),n=e.parent(),"static"===e.css("position")?(n.css({position:"relative"}),e.css({position:"relative"})):(t.extend(i,{position:e.css("position"),zIndex:e.css("z-index")}),t.each(["top","left","bottom","right"],function(t,n){i[n]=e.css(n),isNaN(parseInt(i[n],10))&&(i[n]="auto")}),e.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),e.css(s),n.css(i).show()},removeWrapper:function(e){var i=document.activeElement;return e.parent().is(".ui-effects-wrapper")&&(e.parent().replaceWith(e),(e[0]===i||t.contains(e[0],i))&&t(i).focus()),e},setTransition:function(e,i,n,s){return s=s||{},t.each(i,function(t,i){var o=e.cssUnit(i);o[0]>0&&(s[i]=o[0]*n+o[1])}),s}}),t.fn.extend({effect:function(){var i=e.apply(this,arguments),n=i.mode,s=i.queue,o=t.effects.effect[i.effect];if(t.fx.off||!o)return n?this[n](i.duration,i.complete):this.each(function(){i.complete&&i.complete.call(this)});function r(e){var n=t(this),s=i.complete,r=i.mode;function a(){t.isFunction(s)&&s.call(n[0]),t.isFunction(e)&&e()}(n.is(":hidden")?"hide"===r:"show"===r)?(n[r](),a()):o.call(n[0],i,a)}return!1===s?this.each(r):this.queue(s||"fx",r)},show:(n=t.fn.show,function(t){if(i(t))return n.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="show",this.effect.call(this,s)}),hide:(o=t.fn.hide,function(t){if(i(t))return o.apply(this,arguments);var n=e.apply(this,arguments);return n.mode="hide",this.effect.call(this,n)}),toggle:(s=t.fn.toggle,function(t){if(i(t)||"boolean"==typeof t)return s.apply(this,arguments);var n=e.apply(this,arguments);return n.mode="toggle",this.effect.call(this,n)}),cssUnit:function(e){var i=this.css(e),n=[];return t.each(["em","px","%","pt"],function(t,e){i.indexOf(e)>0&&(n=[parseFloat(i),e])}),n}})}(),C={},t.each(["Quad","Cubic","Quart","Quint","Expo"],function(t,e){C[e]=function(e){return Math.pow(e,t+2)}}),t.extend(C,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,i=4;t<((e=Math.pow(2,--i))-1)/11;);return 1/Math.pow(4,3-i)-7.5625*Math.pow((3*e-2)/22-t,2)}}),t.each(C,function(e,i){t.easing["easeIn"+e]=i,t.easing["easeOut"+e]=function(t){return 1-i(1-t)},t.easing["easeInOut"+e]=function(t){return t<.5?i(2*t)/2:1-i(-2*t+2)/2}}),t.effects,t.effects.effect.blind=function(e,i){var n,s,o,r=t(this),a=["position","top","bottom","left","right","height","width"],l=t.effects.setMode(r,e.mode||"hide"),h=e.direction||"up",c=/up|down|vertical/.test(h),u=c?"height":"width",d=c?"top":"left",p=/up|left|vertical|horizontal/.test(h),f={},m="show"===l;r.parent().is(".ui-effects-wrapper")?t.effects.save(r.parent(),a):t.effects.save(r,a),r.show(),s=(n=t.effects.createWrapper(r).css({overflow:"hidden"}))[u](),o=parseFloat(n.css(d))||0,f[u]=m?s:0,p||(r.css(c?"bottom":"right",0).css(c?"top":"left","auto").css({position:"absolute"}),f[d]=m?o:s+o),m&&(n.css(u,0),p||n.css(d,o+s)),n.animate(f,{duration:e.duration,easing:e.easing,queue:!1,complete:function(){"hide"===l&&r.hide(),t.effects.restore(r,a),t.effects.removeWrapper(r),i()}})},t.effects.effect.bounce=function(e,i){var n,s,o,r=t(this),a=["position","top","bottom","left","right","height","width"],l=t.effects.setMode(r,e.mode||"effect"),h="hide"===l,c="show"===l,u=e.direction||"up",d=e.distance,p=e.times||5,f=2*p+(c||h?1:0),m=e.duration/f,g=e.easing,v="up"===u||"down"===u?"top":"left",b="up"===u||"left"===u,y=r.queue(),_=y.length;for((c||h)&&a.push("opacity"),t.effects.save(r,a),r.show(),t.effects.createWrapper(r),d||(d=r["top"===v?"outerHeight":"outerWidth"]()/3),c&&((o={opacity:1})[v]=0,r.css("opacity",0).css(v,b?2*-d:2*d).animate(o,m,g)),h&&(d/=Math.pow(2,p-1)),(o={})[v]=0,n=0;n<p;n++)(s={})[v]=(b?"-=":"+=")+d,r.animate(s,m,g).animate(o,m,g),d=h?2*d:d/2;h&&((s={opacity:0})[v]=(b?"-=":"+=")+d,r.animate(s,m,g)),r.queue(function(){h&&r.hide(),t.effects.restore(r,a),t.effects.removeWrapper(r),i()}),_>1&&y.splice.apply(y,[1,0].concat(y.splice(_,f+1))),r.dequeue()},t.effects.effect.clip=function(e,i){var n,s,o,r=t(this),a=["position","top","bottom","left","right","height","width"],l="show"===t.effects.setMode(r,e.mode||"hide"),h="vertical"===(e.direction||"vertical"),c=h?"height":"width",u=h?"top":"left",d={};t.effects.save(r,a),r.show(),n=t.effects.createWrapper(r).css({overflow:"hidden"}),o=(s="IMG"===r[0].tagName?n:r)[c](),l&&(s.css(c,0),s.css(u,o/2)),d[c]=l?o:0,d[u]=l?0:o/2,s.animate(d,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){l||r.hide(),t.effects.restore(r,a),t.effects.removeWrapper(r),i()}})},t.effects.effect.drop=function(e,i){var n,s=t(this),o=["position","top","bottom","left","right","opacity","height","width"],r=t.effects.setMode(s,e.mode||"hide"),a="show"===r,l=e.direction||"left",h="up"===l||"down"===l?"top":"left",c="up"===l||"left"===l?"pos":"neg",u={opacity:a?1:0};t.effects.save(s,o),s.show(),t.effects.createWrapper(s),n=e.distance||s["top"===h?"outerHeight":"outerWidth"](!0)/2,a&&s.css("opacity",0).css(h,"pos"===c?-n:n),u[h]=(a?"pos"===c?"+=":"-=":"pos"===c?"-=":"+=")+n,s.animate(u,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){"hide"===r&&s.hide(),t.effects.restore(s,o),t.effects.removeWrapper(s),i()}})},t.effects.effect.explode=function(e,i){var n,s,o,r,a,l,h=e.pieces?Math.round(Math.sqrt(e.pieces)):3,c=h,u=t(this),d="show"===t.effects.setMode(u,e.mode||"hide"),p=u.show().css("visibility","hidden").offset(),f=Math.ceil(u.outerWidth()/c),m=Math.ceil(u.outerHeight()/h),g=[];function v(){g.push(this),g.length===h*c&&(u.css({visibility:"visible"}),t(g).remove(),d||u.hide(),i())}for(n=0;n<h;n++)for(r=p.top+n*m,l=n-(h-1)/2,s=0;s<c;s++)o=p.left+s*f,a=s-(c-1)/2,u.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-s*f,top:-n*m}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:f,height:m,left:o+(d?a*f:0),top:r+(d?l*m:0),opacity:d?0:1}).animate({left:o+(d?0:a*f),top:r+(d?0:l*m),opacity:d?1:0},e.duration||500,e.easing,v)},t.effects.effect.fade=function(e,i){var n=t(this),s=t.effects.setMode(n,e.mode||"toggle");n.animate({opacity:s},{queue:!1,duration:e.duration,easing:e.easing,complete:i})},t.effects.effect.fold=function(e,i){var n,s,o=t(this),r=["position","top","bottom","left","right","height","width"],a=t.effects.setMode(o,e.mode||"hide"),l="show"===a,h="hide"===a,c=e.size||15,u=/([0-9]+)%/.exec(c),d=!!e.horizFirst,p=l!==d,f=p?["width","height"]:["height","width"],m=e.duration/2,g={},v={};t.effects.save(o,r),o.show(),n=t.effects.createWrapper(o).css({overflow:"hidden"}),s=p?[n.width(),n.height()]:[n.height(),n.width()],u&&(c=parseInt(u[1],10)/100*s[h?0:1]),l&&n.css(d?{height:0,width:c}:{height:c,width:0}),g[f[0]]=l?s[0]:c,v[f[1]]=l?s[1]:0,n.animate(g,m,e.easing).animate(v,m,e.easing,function(){h&&o.hide(),t.effects.restore(o,r),t.effects.removeWrapper(o),i()})},t.effects.effect.highlight=function(e,i){var n=t(this),s=["backgroundImage","backgroundColor","opacity"],o=t.effects.setMode(n,e.mode||"show"),r={backgroundColor:n.css("backgroundColor")};"hide"===o&&(r.opacity=0),t.effects.save(n,s),n.show().css({backgroundImage:"none",backgroundColor:e.color||"#ffff99"}).animate(r,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){"hide"===o&&n.hide(),t.effects.restore(n,s),i()}})},t.effects.effect.size=function(e,i){var n,s,o,r=t(this),a=["position","top","bottom","left","right","width","height","overflow","opacity"],l=["width","height","overflow"],h=["fontSize"],c=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],u=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],d=t.effects.setMode(r,e.mode||"effect"),p=e.restore||"effect"!==d,f=e.scale||"both",m=e.origin||["middle","center"],g=r.css("position"),v=p?a:["position","top","bottom","left","right","overflow","opacity"],b={height:0,width:0,outerHeight:0,outerWidth:0};"show"===d&&r.show(),n={height:r.height(),width:r.width(),outerHeight:r.outerHeight(),outerWidth:r.outerWidth()},"toggle"===e.mode&&"show"===d?(r.from=e.to||b,r.to=e.from||n):(r.from=e.from||("show"===d?b:n),r.to=e.to||("hide"===d?b:n)),o={from:{y:r.from.height/n.height,x:r.from.width/n.width},to:{y:r.to.height/n.height,x:r.to.width/n.width}},"box"!==f&&"both"!==f||(o.from.y!==o.to.y&&(v=v.concat(c),r.from=t.effects.setTransition(r,c,o.from.y,r.from),r.to=t.effects.setTransition(r,c,o.to.y,r.to)),o.from.x!==o.to.x&&(v=v.concat(u),r.from=t.effects.setTransition(r,u,o.from.x,r.from),r.to=t.effects.setTransition(r,u,o.to.x,r.to))),"content"!==f&&"both"!==f||o.from.y!==o.to.y&&(v=v.concat(h).concat(l),r.from=t.effects.setTransition(r,h,o.from.y,r.from),r.to=t.effects.setTransition(r,h,o.to.y,r.to)),t.effects.save(r,v),r.show(),t.effects.createWrapper(r),r.css("overflow","hidden").css(r.from),m&&(s=t.effects.getBaseline(m,n),r.from.top=(n.outerHeight-r.outerHeight())*s.y,r.from.left=(n.outerWidth-r.outerWidth())*s.x,r.to.top=(n.outerHeight-r.to.outerHeight)*s.y,r.to.left=(n.outerWidth-r.to.outerWidth)*s.x),r.css(r.from),"content"!==f&&"both"!==f||(c=c.concat(["marginTop","marginBottom"]).concat(h),u=u.concat(["marginLeft","marginRight"]),l=a.concat(c).concat(u),r.find("*[width]").each(function(){var i=t(this),n=i.height(),s=i.width(),r=i.outerHeight(),a=i.outerWidth();p&&t.effects.save(i,l),i.from={height:n*o.from.y,width:s*o.from.x,outerHeight:r*o.from.y,outerWidth:a*o.from.x},i.to={height:n*o.to.y,width:s*o.to.x,outerHeight:n*o.to.y,outerWidth:s*o.to.x},o.from.y!==o.to.y&&(i.from=t.effects.setTransition(i,c,o.from.y,i.from),i.to=t.effects.setTransition(i,c,o.to.y,i.to)),o.from.x!==o.to.x&&(i.from=t.effects.setTransition(i,u,o.from.x,i.from),i.to=t.effects.setTransition(i,u,o.to.x,i.to)),i.css(i.from),i.animate(i.to,e.duration,e.easing,function(){p&&t.effects.restore(i,l)})})),r.animate(r.to,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){0===r.to.opacity&&r.css("opacity",r.from.opacity),"hide"===d&&r.hide(),t.effects.restore(r,v),p||("static"===g?r.css({position:"relative",top:r.to.top,left:r.to.left}):t.each(["top","left"],function(t,e){r.css(e,function(e,i){var n=parseInt(i,10),s=t?r.to.left:r.to.top;return"auto"===i?s+"px":n+s+"px"})})),t.effects.removeWrapper(r),i()}})},t.effects.effect.scale=function(e,i){var n=t(this),s=t.extend(!0,{},e),o=t.effects.setMode(n,e.mode||"effect"),r=parseInt(e.percent,10)||(0===parseInt(e.percent,10)?0:"hide"===o?0:100),a=e.direction||"both",l=e.origin,h={height:n.height(),width:n.width(),outerHeight:n.outerHeight(),outerWidth:n.outerWidth()},c="horizontal"!==a?r/100:1,u="vertical"!==a?r/100:1;s.effect="size",s.queue=!1,s.complete=i,"effect"!==o&&(s.origin=l||["middle","center"],s.restore=!0),s.from=e.from||("show"===o?{height:0,width:0,outerHeight:0,outerWidth:0}:h),s.to={height:h.height*c,width:h.width*u,outerHeight:h.outerHeight*c,outerWidth:h.outerWidth*u},s.fade&&("show"===o&&(s.from.opacity=0,s.to.opacity=1),"hide"===o&&(s.from.opacity=1,s.to.opacity=0)),n.effect(s)},t.effects.effect.puff=function(e,i){var n=t(this),s=t.effects.setMode(n,e.mode||"hide"),o="hide"===s,r=parseInt(e.percent,10)||150,a=r/100,l={height:n.height(),width:n.width(),outerHeight:n.outerHeight(),outerWidth:n.outerWidth()};t.extend(e,{effect:"scale",queue:!1,fade:!0,mode:s,complete:i,percent:o?r:100,from:o?l:{height:l.height*a,width:l.width*a,outerHeight:l.outerHeight*a,outerWidth:l.outerWidth*a}}),n.effect(e)},t.effects.effect.pulsate=function(e,i){var n,s=t(this),o=t.effects.setMode(s,e.mode||"show"),r="show"===o,a="hide"===o,l=r||"hide"===o,h=2*(e.times||5)+(l?1:0),c=e.duration/h,u=0,d=s.queue(),p=d.length;for(!r&&s.is(":visible")||(s.css("opacity",0).show(),u=1),n=1;n<h;n++)s.animate({opacity:u},c,e.easing),u=1-u;s.animate({opacity:u},c,e.easing),s.queue(function(){a&&s.hide(),i()}),p>1&&d.splice.apply(d,[1,0].concat(d.splice(p,h+1))),s.dequeue()},t.effects.effect.shake=function(e,i){var n,s=t(this),o=["position","top","bottom","left","right","height","width"],r=t.effects.setMode(s,e.mode||"effect"),a=e.direction||"left",l=e.distance||20,h=e.times||3,c=2*h+1,u=Math.round(e.duration/c),d="up"===a||"down"===a?"top":"left",p="up"===a||"left"===a,f={},m={},g={},v=s.queue(),b=v.length;for(t.effects.save(s,o),s.show(),t.effects.createWrapper(s),f[d]=(p?"-=":"+=")+l,m[d]=(p?"+=":"-=")+2*l,g[d]=(p?"-=":"+=")+2*l,s.animate(f,u,e.easing),n=1;n<h;n++)s.animate(m,u,e.easing).animate(g,u,e.easing);s.animate(m,u,e.easing).animate(f,u/2,e.easing).queue(function(){"hide"===r&&s.hide(),t.effects.restore(s,o),t.effects.removeWrapper(s),i()}),b>1&&v.splice.apply(v,[1,0].concat(v.splice(b,c+1))),s.dequeue()},t.effects.effect.slide=function(e,i){var n,s=t(this),o=["position","top","bottom","left","right","width","height"],r=t.effects.setMode(s,e.mode||"show"),a="show"===r,l=e.direction||"left",h="up"===l||"down"===l?"top":"left",c="up"===l||"left"===l,u={};t.effects.save(s,o),s.show(),n=e.distance||s["top"===h?"outerHeight":"outerWidth"](!0),t.effects.createWrapper(s).css({overflow:"hidden"}),a&&s.css(h,c?isNaN(n)?"-"+n:-n:n),u[h]=(a?c?"+=":"-=":c?"-=":"+=")+n,s.animate(u,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){"hide"===r&&s.hide(),t.effects.restore(s,o),t.effects.removeWrapper(s),i()}})},t.effects.effect.transfer=function(e,i){var n=t(this),s=t(e.to),o="fixed"===s.css("position"),r=t("body"),a=o?r.scrollTop():0,l=o?r.scrollLeft():0,h=s.offset(),c={top:h.top-a,left:h.left-l,height:s.innerHeight(),width:s.innerWidth()},u=n.offset(),d=t("<div class='ui-effects-transfer'></div>").appendTo(document.body).addClass(e.className).css({top:u.top-a,left:u.left-l,height:n.innerHeight(),width:n.innerWidth(),position:o?"fixed":"absolute"}).animate(c,e.duration,e.easing,function(){d.remove(),i()})}})?n.apply(e,s):n)||(t.exports=o)},function(t,e,i){window.jquery=i(0),window.$=jquery,window.jQuery=jquery,i(7),i(6),window.wows=i(5),i(4),i(3),i(1)}]);