@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icomoon.eot?adavoq');
  src:  url('../fonts/icomoon.eot?adavoq#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?adavoq') format('truetype'),
    url('../fonts/icomoon.woff?adavoq') format('woff'),
    url('../fonts/icomoon.svg?adavoq#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrows5:before {
  content: "\e92f";
}
.icon-clock:before {
  content: "\e930";
}
.icon-landscape:before {
  content: "\e931";
}
.icon-left4:before {
  content: "\e932";
}
.icon-magnifying-glass:before {
  content: "\e933";
}
.icon-medical:before {
  content: "\e934";
}
.icon-money2:before {
  content: "\e935";
}
.icon-note2:before {
  content: "\e936";
}
.icon-people3:before {
  content: "\e937";
}
.icon-phone-call:before {
  content: "\e938";
}
.icon-signs:before {
  content: "\e939";
}
.icon-sports:before {
  content: "\e93a";
}
.icon-square2:before {
  content: "\e93b";
}
.icon-technology3:before {
  content: "\e93c";
}
.icon-telephone:before {
  content: "\e93d";
}
.icon-transport:before {
  content: "\e93e";
}
.icon-arrows4:before {
  content: "\e92e";
}
.icon-arrows3:before {
  content: "\e92b";
}
.icon-light:before {
  content: "\e92c";
}
.icon-school:before {
  content: "\e92d";
}
.icon-e-mail-envelope:before {
  content: "\e900";
}
.icon-arrows:before {
  content: "\e901";
}
.icon-cogwheel:before {
  content: "\e902";
}
.icon-cup:before {
  content: "\e903";
}
.icon-gps:before {
  content: "\e905";
}
.icon-laptop:before {
  content: "\e906";
}
.icon-money:before {
  content: "\e907";
}
.icon-note:before {
  content: "\e908";
}
.icon-null:before {
  content: "\e90a";
}
.icon-office:before {
  content: "\e90b";
}
.icon-people:before {
  content: "\e90c";
}
.icon-technology:before {
  content: "\e90d";
}
.icon-technology-1:before {
  content: "\e90e";
}
.icon-technology-2:before {
  content: "\e90f";
}
.icon-technology-3:before {
  content: "\e910";
}
.icon-technology-4:before {
  content: "\e911";
}
.icon-video:before {
  content: "\e912";
}
.icon-arrow:before {
  content: "\e91a";
}
.icon-business:before {
  content: "\e91b";
}
.icon-business-1:before {
  content: "\e91c";
}
.icon-business-2:before {
  content: "\e91d";
}
.icon-commerce:before {
  content: "\e904";
}
.icon-graphic:before {
  content: "\e91e";
}
.icon-location:before {
  content: "\e915";
}
.icon-multimedia:before {
  content: "\e91f";
}
.icon-multimedia-1:before {
  content: "\e920";
}
.icon-multiply:before {
  content: "\e909";
}
.icon-people2:before {
  content: "\e921";
}
.icon-science:before {
  content: "\e922";
}
.icon-science-1:before {
  content: "\e923";
}
.icon-search:before {
  content: "\e924";
}
.icon-shapes:before {
  content: "\e918";
}
.icon-sheet:before {
  content: "\e925";
}
.icon-square:before {
  content: "\e913";
}
.icon-technology2:before {
  content: "\e914";
}
.icon-travel:before {
  content: "\e919";
}
.icon-left3:before {
  content: "\e92a";
}
.icon-left:before {
  content: "\e929";
}
.icon-left-arrow-angle:before {
  content: "\e926";
}
.icon-left-arrow-angle2:before {
  content: "\e927";
}
.icon-arrows2 .path1:before {
  content: "\e916";
  color: rgb(219, 43, 66);
}
.icon-arrows2 .path2:before {
  content: "\e917";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.icon-computer:before {
  content: "\e941";
}
.icon-graphic2:before {
  content: "\e93f";
}
.icon-layers:before {
  content: "\e940";
}
.icon-left2:before {
  content: "\e928";
}

