
/***************************************************************************************************************
||||||||||||||||||||||||||||       MASTER STYLESHEET FOR The Expeart      ||||||||||||||||||||||||||||||||||||
****************************************************************************************************************
||||||||||||||||||||||||||||              TABLE OF CONTENT                  ||||||||||||||||||||||||||||||||||||
****************************************************************************************************************
****************************************************************************************************************

1. Imported styles
2. Global styles
3. Header styles
4. Slider styles
5. Our Services styles
6. Our features styles
7. About section styles
8. Team Member styles
9. call to action styles
10. Blog styles
11. Testimonial styles
12. contact styles
13. Subscription styles
14. Footer styles
15. Inner banner styles
16. Inner banner styles
17. Project content styles
21. fact section styles

****************************************************************************************************************
||||||||||||||||||||||||||||            End TABLE OF CONTENT                ||||||||||||||||||||||||||||||||||||
****************************************************************************************************************/

/* ==============================
   1. Imported styles
   ============================== */
/**/


@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700');
@import url('https://fonts.googleapis.com/css?family=Hind:300,400,500,600,700');

@import url(../css/bootstrap.min.css);
@import url(../css/jquery.bootstrap-touchspin.css);

@import url(../css/font-awesome.css);
@import url(../css/flaticon.css);
@import url(../css/icomoon.css);

@import url(../css/settings.css);
@import url(../css/layers.css);
@import url(../css/navigation.css);

@import url(../css/owl.carousel.css);
@import url(../css/jquery.bxslider.css);
@import url(../css/flexslider.css);

@import url(../css/jquery-ui.css);
@import url(../css/menuzord.css);
@import url(../css/animate.min.css);
/*@import url(../css/jquery.fancybox.css);*/
/*@import url(../css/nouislider.css);
@import url(../css/nouislider.pips.css);*/
/*@import url(../css/bootstrap-select.min.css);*/





/* ==============================
   2. Global styles
   ============================== */

body {
  font-family: 'Hind', sans-serif;
  color:#222222;
  font-size: 16px;
}

h1,h2,h3,h4,h5,h6,p,ul { margin:0;padding: 0;}
h1,h2,h3,h4,h5 {
  font-family: 'Poppins', sans-serif;
} 

ul {list-style-type: none;}

p {font-family: 'Hind', sans-serif;font-size: 16px;color: #848484;line-height: 26px; font-weight: 300;}
a {text-decoration: none;display: inline-block;outline: none;}
a:hover,a:focus,a:visited {text-decoration: none; outline: none;}

img {max-width: 100%;display: inline-block;}

button {border:none; outline:none; box-shadow: none; display: block; padding: 0; background: transparent;}
input,textarea {font-family: 'Hind', sans-serif;display: block; font-size: 16px; line-height: 28px; outline: none; box-shadow: none;transition: all 0.3s ease-in-out;}


.boxed_wrapper {
    width: 100%;
    background: #fff;
    overflow-x: hidden;
}
.sec-padd {
  padding: 75px 0 80px;
}
.sec-padd2 {
  padding: 75px 0 30px;
}

.sec-padd-top {
  padding-top: 75px;
}
.sec-padd-bottom {
  padding-bottom: 80px;
}
.no-padd {
  padding: 0px;
}
.clear_fix { clear:both;}
.clear_fix:after {
  display: table;
  content: '';
  clear: both;
}
.list_inline li {
  display: inline-block;
}
.social li {
  display: inline-block;
  font-size: 14px;
  padding: 0 10px;
}
.social li a {
  color: #fff;
  transition: .5s ease-in-out;
  font-size: 14px;
}
.default_link {
  font-size: 14px;
  text-transform: uppercase;
  font-family: "Poppins", sans-serif;
  color: #f2704E;
  font-weight: 600;
}
.default_link:hover {
  color: #f2704E;
}

.default_link i {
  margin-left: 3px;
  font-weight: 700;
  font-size: 18px;
  position: relative;
  top: 1px;
}

.default-overlay-outer{
  position:absolute;
  display:block;
  left:0px;
  top:0px;
  width:100%;
  height:100%;
  padding:10px;
  transition:all 700ms ease;
  -webkit-transition:all 700ms ease;
  -ms-transition:all 700ms ease;
  -o-transition:all 700ms ease;
  -moz-transition:all 700ms ease;
}

.default-overlay-outer .inner{
  position:absolute;
  left:0px;
  top:0px;
  width:100%;
  height:100%;
  display:table;
  vertical-align:middle;
  text-align:center;
}

.default-overlay-outer .inner .content-layer{
  position:relative;
  display:table-cell;
  vertical-align:middle;
  color:#ffffff;
}


.rating {
    font-size: 14px;
    color: #f2704E;
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px #292121;
    background: #292121;
}

::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.75);
    -webkit-box-shadow: inset 0 0 6px rgba(255,255,255,0.75);
}
::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(255,255,255,0.75);
}


.uppercase {
  text-transform: uppercase;
}
.padd-80 {
  padding-top: 80px;
}
.tran3s {
  transition: all 0.3s ease-in-out;
}
.center {
  text-align: center;
}


.row-5 {
  margin: 0 -5px;
}
.row-10 {
  margin: 0 -10px;
}

.thm-color {
  color: #f2704E;
}

.bold {
  font-weight: 700;
}
.s-bold {
  font-weight: 600;
}
.m-bold {
  font-weight: 600;
}

.float_left {
    float: left;
}
.float_right {
    float: right;
}

.border-bottom {
  border-bottom: 1px solid #f7f7f7;
}

@media (min-width: 1200px) {
  .container {
    padding: 0;
  }
}

.thm-btn {
  position: relative;
  background: #f2704E;
  font-size: 14px;
  line-height: 46px;
  font-weight: 600;
  color: #ffffff;
  border: 2px solid #f2704E;
  text-transform: uppercase;
  font-family: 'Poppins', sans-serif;
  display: inline-block;
  padding: 0 38px;
  transition: all .5s cubic-bezier(0.4, 0, 1, 1);
}
.thm-btn:hover {
  background: transparent;
  color: #f2704E;
  transition: all .5s cubic-bezier(0.4, 0, 1, 1);
}
.thm-btn2 {
  position: relative;
  background: transparent;
  font-size: 14px;
  line-height: 46px;
  font-weight: 600;
  color: #f2704E;
  border: 2px solid #f4f4f4;
  text-transform: uppercase;
  font-family: 'Poppins', sans-serif;
  display: inline-block;
  padding: 0 38px;
  transition: all .5s cubic-bezier(0.4, 0, 1, 1);
}
.thm-btn2:hover {
  background: #f2704E;
  border-color: #f2704E;
  color: #fff;
  transition: all .5s cubic-bezier(0.4, 0, 1, 1);
}


.thm-btn-tr {
  position: relative;
  background: transparent;
  font-size: 14px;
  line-height: 46px;
  font-weight: 600;
  color: #ffffff;
  border: 2px solid #fff;
  text-transform: uppercase;
  font-family: 'Poppins', sans-serif;
  display: inline-block;
  padding: 0 38px;
  transition: all .5s cubic-bezier(0.4, 0, 1, 1);
}
.thm-btn-tr:hover {
  background: #f2704E;
  border-color: #f2704E;
  color: #fff;
  transition: all .5s cubic-bezier(0.4, 0, 1, 1);
}

.anim-3 {
  transition: all 0.3s ease;
}
.anim-5 {
  transition: all 0.5s ease;
}


.section-title {
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 50px;
}
.section-title h2 {
  position: relative;
  text-transform: capitalize;
}
.section-title h3 {
  position: relative;
  font-size: 24px;
  text-transform: capitalize;
}
.section-title h4 {
  position: relative;
  font-size: 20px;
  text-transform: capitalize;
}
.section-title:before {
    content: "";
    position: absolute;
    top: 48px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #eaeaea;
}

.section-title:after {
    content: "";
    position: absolute;
    top: 48px;
    left: 0;
    width: 50px;
    height: 1px;
    background: #f2704E;
}
.section-title.center:after {
    left: 50%;
    margin-left: -25px;
}



.inner-title {
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 30px;
}

.inner-title:before {
    content: "";
    position: absolute;
    top: 38px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #eaeaea;
}

.inner-title:after {
    content: "";
    position: absolute;
    top: 38px;
    left: 0;
    width: 50px;
    height: 1px;
    background: #f2704E;
}
.inner-title h4 {
  font-size: 20px;
}

.scroll-top {
    width: 60px;
    height: 60px;
    position: fixed;
    border-radius: 50%;
    bottom: 30px;
    right: 30px;
    z-index: 99;
    display: none;
    color: #f2704E;
    background: #10252e;
    text-align: center;
    border: 2px solid #19333e;
}
.scroll-top span:before {
  font-size: 27px;
}
.scroll-top:after {
  position: absolute;
  z-index: -1;
  content: '';
  top: 100%;
  left: 5%;
  height: 10px;
  width: 90%;
  opacity: 1;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}

.preloader{ position:fixed; left:0px; top:0px; width:100%; height:100%;
 z-index:999999; background-color:#ffffff; background-position:center center;
  background-repeat:no-repeat; background-image:url(../images/icons/preloader.gif);}




.video-image-box{
  position:relative;  
}

.video-image-box .image{
  position:relative;  
}

.video-image-box img{
  display:block;
  width:100%; 
}

.video-image-box .image .overlay-link{
  position:absolute;
  left:0px;
  top:0px;
  width:100%;
  height:100%;
  color:#ffffff;
  text-align:center;
  background:rgba(0,0,0,0.10);
  transition:all 500ms ease;
  -moz-transition:all 500ms ease;
  -webkit-transition:all 500ms ease;
  -ms-transition:all 500ms ease;
  -o-transition:all 500ms ease; 
}

.video-image-box .image .overlay-link:hover {
    background: rgba(72, 199, 236, 0.9);
}

.video-image-box .image .overlay-link .icon{
  position:absolute;
  left:50%;
  top:50%;
  margin-top:-25px;
  margin-left:-25px;
  width: 58px;
  height: 41px;
  line-height: 43px;
  font-size:60px;
  color: #f7f7f7;
}








/* ==============================
   2. header-style
   ============================== */

.top-bar {
  position: relative;
  background: #10252e;
  line-height: 49px;
  position: relative;
  z-index: 9;
}

.top-bar .top-bar-info {
  display: inline-block;
}
.top-bar .top-bar-info li,.top-bar .top-bar-info li a{
  color: #fff;
  margin-right: 20px;
  display: inline-block;
  position: relative;
  top: 3px;
}
.top-bar .top-bar-info li i {
    color: #f2704E;
    margin-right: 10px;
    font-size: 20px;
    position: relative;
    top: 3px;
}


.top-bar .social li {
  color: #fff;
  font-size: 16px;
  font-weight: 300;
}
.top-bar .social li a:hover {
  color: #f2704E;
  transition: .5s ease-in-out;
}

.top-bar .social li:last-child {
  padding-right: 0px;
}
.top-bar .social,
.top-bar .link {
  display: inline-block;
  margin-left: 20px;
}




/*language switcher*/


#polyglotLanguageSwitcher {
  position: relative;
  display: inline-block;
  position: relative;
  margin-right: 20px;
}


#polyglotLanguageSwitcher span.trigger:before {
  content: '\f107';
  font-family: FontAwesome;
  position: absolute;
  color:#cdcdcd;
  top: 0;
  right: 0px;
  line-height: 10px;
}
#polyglotLanguageSwitcher a {
  font-family: 'Hind', sans-serif;
  display: inline-block;
  font-size: 14px;
  color: #fff;
  font-weight: normal;
}


#polyglotLanguageSwitcher a.current:link, 
#polyglotLanguageSwitcher a.current:visited, 
#polyglotLanguageSwitcher a.current:active {
    position: relative;
    background-color: #162f3a;
    border: 0;
    line-height: 34px;
    border-radius: 0;
    color: #fff;
    text-align: center;
    height: 33px;
    padding: 0px 0px 0px 22px;
}

#polyglotLanguageSwitcher a.current:hover {
    background-color: #162f3a;
}

#polyglotLanguageSwitcher ul.dropdown {
  top: 100%;
}











/*logo*/

.main-logo {
  margin: 35px 0  35px;

}

.main-logo a span{
  color:#222;
  transition: .5s ease;
}

.main-logo a {
  color:#f2704E;
  transition: .5s ease;
}

.main-logo a:hover {
  color:#222;
  transition: .5s ease;
}


.main-logo a:hover span{
  color:#f2704E;
}


/*menu*/

.theme_menu .container {
  position: relative;
}

.theme_menu {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 2;
}
.theme_menu .menu-column {
  padding-left: 0;
}

.theme_menu .menuzord {
  padding: 0;
  background: transparent;
}


.menuzord-menu > li > a {
    padding: 49px 28px 41px;
    margin: 0 10px;
    text-transform: uppercase;
    border-bottom: 5px solid transparent;
    color: #222;
    transition: .5s ease;
}
.menuzord-menu > li.active a {
    border-color: #f2704E;
    color: #f2704E;
}
.menuzord-menu > li > a:hover {
  color: #f2704E;
  transition: .5s ease;
}

.menuzord-menu ul.dropdown, .menuzord-menu ul.dropdown li ul.dropdown {
    padding: 12px 30px;
    min-width: 230px;
    background: #10252e;
}

.menuzord-menu ul.dropdown li a {
  padding: 7px 0 9px;
  color: #959595;
  transition: .5s ease-in-out;
  font-size: 14px;
  text-transform: capitalize;
  font-family: 'Poppins', sans-serif;
}

.menuzord-menu ul.dropdown li:hover > a {
    padding-left: 0px;
    padding-right: 0px;
    color: #f2704E;
    transition: .5s ease-in-out;
    font-size: 14px;
}


/*sticky*/
.theme_menu.stricky-fixed .menuzord-menu > li > a {
  padding: 35px 15px 30px;
}

.theme_menu.stricky-fixed .main-logo {
    margin: 23px 0;
}
.theme_menu.stricky-fixed .nav_side_content {
    margin-top: 32px;
}

.theme_menu.stricky-fixed .nav_side_content .search_option form {
  top: 58px;
}




/*megamenu*/



.w100 {
  width: 100%;
}
.w50 {
  width: 50%;
  float: left;
}
.row-10 {
  margin: 0 -10px;
}
.megamenu .form-group {
  padding: 0 10px;
}
.megamenu .default-form {
  max-width: 410px;
  margin-bottom: 30px;
}
.megamenu .default-form.register-form {
  width: 405px;
  border: 1px solid #2f2f2f;
  padding: 10px 30px 24px;
  background: #252525;
}

.default-form.register-form .form-group {
  margin-bottom: 0;
}
.default-form.register-form .link {
  border-bottom: 1px solid #2f2f2f;
  padding-bottom: 10px;
  margin-bottom: 10px;
  color: #fff;
}
.default-form.register-form .link .thm-color-2 {
  color: #848484;
}
.default-form.register-form .thm-color:hover,
.default-form.register-form .thm-color-2:hover {
  color: #f2704E;
  transition: .5s ease;
}

.default-form.register-form .link_2 {
  position: relative;
}

.default-form.register-form .link_2.singn {
  padding-left: 25px;

}
.default-form.register-form .link_2 .fancy_video {
  position: absolute;
  left: 0;
  top: 1px;
  width: 15px;
  border-radius: 50%;
  font-size: 8px;
  text-align: center;
  line-height: 15px;
  height: 15px;
  color: #222;
  background: #f2704E;
  font-family: "FontAwesome";

}
.default-form.register-form .link_2 .fancy_video span {
  position: relative;right:-1px;
}









/*=================== Cart And Search Option ===============*/

.theme_menu .right-column {
    position: absolute;
    right: 0;
    width: 50px;
}


.nav_side_content {
  margin-top: 44px;
  position: relative;
  right: 0;
}
.nav_side_content .cart_select>button {
  font-size: 14px;
  text-transform: uppercase;
  position: relative;
  margin-right: 23px;
  color:#272727;
}
.nav_side_content .cart_select>button span {
  font-weight: normal;
  padding: 0 0px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  right: -20px;
  color: #f2704E;
}
.nav_side_content .cart_select>button i {
  font-size: 18px;
  margin-left:5px;
}
.nav_side_content .cart_select,
.nav_side_content .search_option {
  display: inline-block;
  vertical-align: middle;
}
.nav_side_content .search_option>button {
  height: 26px;
  font-size: 15px;
  color: #222;
  border-left: 1px solid #828d92;
  padding-left: 35px;
}

.nav_side_content .search_option form {
    height: 50px;
    width: 280px;
    padding: 5px;
    border-radius: 4px;
    margin: 0;
    left: auto;
    right: 0;
    border-color: #f2704E;
    top: 70px;
}
.nav_side_content .search_option form input {
    font-family: 'Poppins', sans-serif;
    font-weight: normal;
    width: 100%;
    height: 100%;
    background: transparent;
    color: #9e9e9e;
    border: 1px solid rgba(51, 51, 51, 0.16);
    padding: 0 47px 0 10px;
    font-size: 15px;
    border-radius: 3px;
    box-shadow: inset 0 0 11px 2px rgba(119, 119, 119, 0.25);
}
.nav_side_content .search_option form button {
  display: block;
  width:40px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top:5px;
  right:5px;
  color:#f2704E;
}
.nav_side_content .cart_select {
  border-left: 1px solid #ececec;
  padding-left: 5px;
}

.theme_menu .link_btn {
  margin-top: 24px;
}





/* Mainmenu fixed style */
@keyframes menu_sticky {
  0%   {margin-top:-100px;}
  50%  {margin-top: -90px;}
  100% {margin-top: 0;}
}
.theme_menu.stricky-fixed {
  margin: 0;
  position: fixed;
  background: #fff;
  top:0;
  left:0;
  width: 100%;
  z-index: 99999;
  animation-name: menu_sticky;
  animation-duration: 0.60s;
  animation-timing-function: ease-out;
}





/*** 
=============================================
    Rev Slider Wrapper style
=============================================
***/



.rev_slider_wrapper {
  position: relative;
  margin-top: -115px;
  z-index: 1;
}



.rev_slider_wrapper .slide-content-box h3 {
  background: rgba(0, 0, 0, 0) url("../img/slides/press-bg.png") no-repeat;
  background-size: contain;
  display: inline-block;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 22px;
  font-family: 'Poppins', sans-serif;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.rev_slider_wrapper .slide-content-box h2 {
  color: #ffffff;
  font-size: 55px;
  line-height: 62px;
  margin: 14px 0 9px;
  font-weight: 700;
}
.rev_slider_wrapper .slide-content-box p{
  color: #ffffff;
  font-size: 18px;
  line-height: 30px;
  font-family: 'Poppins', sans-serif;
  font-weight: 300;
  margin: 0 0 30px;
}
.rev_slider_wrapper .slide-content-box .button a{
  transition: all 500ms ease !important;
  -moz-transition: all 500ms ease !important;
  -webkit-transition: all 500ms ease !important;
  -ms-transition: all 500ms ease !important;
  -o-transition: all 500ms ease !important;
}


.rev_slider_wrapper .slide-content-box.last-slide p{
  margin: 0;    
}
.rev_slider_wrapper .slide-content-box.last-slide a{
  margin: 0 8px;    
}

.rev_slider_wrapper .slotholder {
  position: relative;
}

.slotholder:after {
    background: rgba(0, 0, 0, 0.10);
    width: 100%;
    height: 100%;
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    pointer-events: none;
}

.tp-bannertimer {
  display: none;
  
}

.rev_slider_wrapper .tparrows {
  background: transparent;
  border: 2px solid #fff;

}






/************************** 
* Inner Banner styles 
***************************/
.inner-banner {
  position: relative;
  background: #f7f7f7;
  background-size: cover;
  margin-bottom: 30px;
}
.inner-banner:before {
}

.inner-banner h3 {
  margin: 0;
  display: table-cell;
  vertical-align: middle;
  font-size: 36px;
  line-height: 60px;
  color: #222;
  font-weight: 600;
  text-transform: capitalize;
}
.inner-banner .box {
  position: relative;
  display: table;
  height: 140px;
  padding-top: 30px;
  width: 100%;
  text-align: center;
}

.breadcumb-wrapper {
  background: #fff;
  padding: 16px 20px;
  border: 1px solid #f4f4f4;
  position: relative;
  bottom: -30px;
}
.breadcumb-wrapper ul,
.breadcumb-wrapper li {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 18px;
  color: #f2704E;
}
.breadcumb-wrapper ul.link-list li a {
  color: #222;
  text-transform: capitalize;
  font-size: 18px;
  transition: .3s ease;
}
.breadcumb-wrapper ul.link-list li a:hover {
  transition: .3s ease;
}

.breadcumb-wrapper ul.link-list li a i {
  color: #fac012;
  margin-right: 7px;
}

.breadcumb-wrapper ul.link-list li a:after {
  content: "\f105";
  font-family: 'FontAwesome';
  margin: 0 10px;
}
.breadcumb-wrapper a.get-qoute {
  text-transform: capitalize;
  font-weight: 400;
  color: #f2704E;
  font-size: 18px;
}
.breadcumb-wrapper a.get-qoute i {
  margin-left: 7px;
  color: #222;
  margin-right: 7px;
  font-size: 14px;
}







/*** 

====================================================================
  Main Footer
====================================================================

***/

.main-footer{
  position:relative;
  background:#10252e;
  color:#848484;
}

.main-footer .footer-title{
  font-size: 22px;
  color: #fff;
  margin-bottom: 30px;
}
.main-footer .footer-logo {
  margin-bottom: 40px;
}

.main-footer .widgets-section{
  position:relative;
  padding:70px 0px 20px;
}

.main-footer .footer-column{
  margin-bottom:30px; 
}

.main-footer .footer-widget{
  position:relative;  
}

.main-footer .footer-column h2{
  font-size:15px;
  font-weight:700;
  text-transform:uppercase;
  margin-bottom:30px;
  color:#a1c436;  
}


.main-footer .about-widget .text{
  margin-bottom:20px;
  padding-bottom: 5px;
}

.main-footer .contact-info , .main-footer .sample-info{
  margin-bottom: 25px;
}

.main-footer .contact-info li{
  position:relative;
  padding-left: 35px;
  margin-bottom: 16px;
  line-height:24px;
  color: #959595;
  font-family: 'Hind', sans-serif;
  font-weight: 300;
}

.main-footer .sample-info li{
  margin-bottom: 16px;
}

.main-footer .sample-info li a{
  position:relative;
  padding-left: 35px;
  line-height:24px;
  color: #959595;
  font-family: 'Hind', sans-serif;
  font-weight: 300;
}

.main-footer .sample-info li a:hover{
  color: #f2704E;
}


.main-footer .sample-info li span {
  position:absolute;
  left:0px;
  top:0px;
  line-height:24px;
  font-size: 24px;
  color: #f2704E;
}



.main-footer .contact-info li span {
  position:absolute;
  left:0px;
  top:0px;
  line-height:24px;
  font-size: 24px;
  color: #f2704E;

}


.main-footer .contact-widget .social li {
  padding-left: 0px;
  padding-right: 10px;
}
.main-footer .contact-widget .social li a {
  transition: .5s ease;
  color: #37444a;
  border: 2px solid #19333e;
  border-radius: 50%;
  line-height: 38px;
  height: 38px;
  width: 38px;
  text-align: center;

}

.main-footer .contact-widget .social li a:hover {
  border-color: #f2704E;
  color: #f2704E;
  transition: .5s ease;

}







.main-footer .social-links a{
  display:inline-block;
  width:32px;
  height:32px;
  margin-left:5px;
  line-height:30px;
  border:1px solid transparent;
  background:none;
  color:#686868;
  font-size:14px;
  text-align:center;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.main-footer .social-links a:hover{
  color:#a1c436;
  border-color:#a1c436; 
}

.main-footer .posts-widget .post{
  position:relative;
  margin-bottom: 14px;
  padding-bottom: 15px;
  padding-left: 15px;
  border-bottom:1px solid rgba(255,255,255,0.15);
}

.main-footer .posts-widget .post:last-child{
  border-bottom:none;
  padding-bottom:0px;
  margin-bottom:0px;  
}

.main-footer .posts-widget .post .content{
  position:relative;
  min-height: 40px;
  padding-left: 0;
  padding-top:0px;
  margin-bottom: 5px;
}

.main-footer .posts-widget .post .content .post-thumb{
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:52px;
}

.main-footer .posts-widget .post .content .post-thumb img{
  display:block;
  width:100%; 
}

.main-footer .posts-widget .post h4{
  font-size:14px;
  color:#999999;
  line-height: 26px;
}

.main-footer .posts-widget .post h4 a{
  color:#848484;
  font-family: 'Hind', sans-serif;
  font-weight: 300;
  font-size: 16px;
}

.main-footer .posts-widget .post .time{
  color:#f2704E;
  font-family: 'Hind', sans-serif;
  font-weight: 300;
  font-size: 16px;
}

.main-footer .posts-widget .post .time .fa{
  padding-right:8px;  
}

.main-footer .links-widget .list li{
  position:relative;
  margin-bottom: 13px;
}

.main-footer .links-widget .list li a{
  position:relative;
  color:#848484;
  font-family: 'Hind', sans-serif;
  font-weight: 300;
  font-size: 16px;
}

.main-footer .posts-widget .post:before,
.main-footer .links-widget .list li a:before{
  content: '';
  position:absolute;
  left:0px;
  background: transparent;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  transition: .5s ease-in-out;
}

.main-footer .links-widget .list li a:hover:before{
  background: #f2704E;
  transition: .5s ease-in-out;
}

.main-footer .links-widget .list li a:hover{
  color: #f2704E;
}

.main-footer .contact-widget{
  position:relative;
}

.main-footer .news-widget .default-form {
  position:relative;
}

.main-footer .news-widget .default-form input {
  margin-top: 22px;
  margin-bottom: 20px;
  height: 45px;
  padding-left: 50px;
}
.main-footer .news-widget .default-form span {
  position: absolute;
  left: 15px;
  top: 12px;
  color: #f2704E;
  font-size: 14px;
  border-right: 1px solid #ccc;
  padding: 3px 7px 4px 0px;
}


.main-footer .news-widget .default-form .thm-btn {
  line-height: 41px;
  width: 100%;
}

.footer-bottom {
  background: #10252e;
  border-top: 1px solid #142b35;
  padding: 26px 0;
}
.footer-bottom .copy-text {
  font-family: 'Poppins', sans-serif;
  color: #848484;
  font-size: 14px;
}
.footer-bottom .copy-text a {
  color: #f2704E;
  transition: .3s ease;
}
.footer-bottom .get-text ul li {
  float: left;
}
.footer-bottom .get-text ul li a {
  color: #848484;
  font-size: 14px;
  margin-right: 5px;
  font-family: "Popins", sans-serif;
}
.footer-bottom .get-text ul li a:hover {
  color: #f2704E;
  transition: .3s ease;
}







/************************** 
* why-choose styles 
***************************/


.why-choose {
  position: relative;
}


.why-choose .img-box {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.why-choose .overlay-box {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: -60px;
  transition: .3s ease-out;
  background: rgba(0, 0, 0, 0.75);
  transform: translate(0,100%);
}
.why-choose .item:hover .overlay-box {
  transition: .5s ease-in-out;
  top: 0px;
  transform: translate(0,0);
}

.why-choose .inner-box {
  position: relative;
  padding-left: 80px;
  padding-right: 21px;
  transition: 1s ease;
}
.why-choose .item:hover .inner-box {
  padding: 43px 21px 43px 80px;
  transition: .5s ease;
}

.why-choose .icon_box {
  position: absolute;
  width: 60px;
  height: 100%;
  text-align: center;
  background: #f2704E;
  left: 0;
  top: 0;
  bottom: 0;
  line-height: 60px;
  color: #fff;
  transition: .5s ease;
}
.why-choose .icon_box span {
    font-size: 33px;
    line-height: 60px;
}

.why-choose .item:hover .icon_box span {
    position: absolute;
    top: 50%;
    width: 100%;
    left: 0;
    margin-top: -20px;
    transition: .5s ease;
}

.why-choose .overlay-box .text {
  color: #fff;
}

.why-choose .overlay-box h4 {
  color: #fff;
  line-height: 60px;
  transition: .5s ease-in-out;
}
.why-choose .item:hover .overlay-box h4 {
  color: #f2704E;
  transition: .5s ease-in-out;
}

.why-choose .overlay-box .text p {
  color: rgba(255, 255, 255, 0.06);
  transform: translate(0,100%);
  transition: 1s ease;
  opacity: 0;
}
.why-choose .item:hover .overlay-box .text p {
  transform: translate(0,0);
  transition: .5s ease;
  transition-delay: .3s;
  opacity: 1;
  color: #fff;
}



/************************** 
* default-blog styles 
***************************/
.blog-section {
}
.default-blog-news {
  position: relative;
  transition: .5s ease-in-out;
  margin-bottom: 50px;
}

.default-blog-news:hover {
  box-shadow: 0 15px 16px 0 rgba(0, 0, 0, 0.03), 0 0px 82px 0 rgba(0, 0, 0, 0.02);
  transition: .2s cubic-bezier(0.4, 0, 1, 1);
}

.default-blog-news .lower-content{
  position:relative;
  padding: 30px 0px 20px;
  padding-left: 90px;
  border-bottom: 1px solid #f4f4f4;
}
.default-blog-news .date {
  position: absolute;
  left: 0px;
  top: 30px;
  height: 64px;
  width: 64px;
  background: #f2704E;
  line-height: 20px;
  padding-top: 13px;
  z-index: 9;
  color: #fff;
  text-align: center;
}

.default-blog-news .lower-content .text p{
  margin-bottom: 10px;
}

.default-blog-news .lower-content h4 {
  color:#222;
  font-weight: 400;
  margin-bottom: 10px;
}

.default-blog-news .lower-content h4 a{
  color:#222;
  transition: all 300ms ease;
  font-size: 18px;
}

.default-blog-news .lower-content h4 a:hover{
  color:#f2704E;
  transition: all 300ms ease;
}

.default-blog-news .lower-content .post-meta{
  color: #848484;
  font-size:16px;
  margin-bottom: 7px;
}


.default-blog-news .lower-content .default_link {
  text-transform: capitalize;
  color: #222;
  transition: .5s ease;
}
.default-blog-news .lower-content .default_link:hover {
  transition: .5s ease;
  color: #f2704E;
}


.default-blog-news .img-holder {
    display: block;
    overflow: hidden;
    position: relative;
}
.default-blog-news .img-holder a {
  width: 100%;
}
.default-blog-news .img-holder img {
    transform: scale(1);
    transition: all 0.5s ease 0s;
    width: 100%;
}

.default-blog-news:hover .img-holder img {
    transform: scale(1.1);
}
.default-blog-news .overlay {
    background-color: rgba(16, 37, 46, 0.9);
    text-align: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    color: #fff;
    opacity: 0;
    filter: alpha(opacity=0);
    transition: all 450ms ease-out 0s;
    transform: rotateY(180deg) scale(0.5,0.5);
}

.default-blog-news:hover .img-holder .overlay {
    opacity: 1;
    filter: alpha(opacity=100);
    transform: rotateY(0deg) scale(1,1);
}
.default-blog-news .overlay .box {
    display: table;
    height: 100%;
    width: 100%;
}
.default-blog-news .overlay .box .content {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
}
.default-blog-news .img-holder .overlay .box .content a i {
    background: #f2704E;
    border-radius: 50%;
    color: #ffffff;
    display: inline-block;
    font-size: 16px;
    height: 45px;
    line-height: 48px;
    transition: all 500ms ease 0s;
    width: 45px;
}

.default-blog-news .img-holder .overlay .box .content a:hover i {
    background: #f2704E;
}



/*large blog*/

.large-blog-news {
  position: relative;
  margin-left: 80px;
  transition: .5s ease-in-out;
  margin-bottom: 50px;
  padding-bottom: 50px;
  border-bottom: 1px solid #f4f4f4;
}

.large-blog-news .date {
  position: absolute;
  left: -80px;
  top: 0px;
  height: 64px;
  width: 64px;
  background: #f2704E;
  line-height: 20px;
  padding-top: 13px;
  z-index: 9;
  color: #fff;
  text-align: center;
}


.large-blog-news .lower-content{
  position:relative;
  padding: 25px 0px 0px;
}

.large-blog-news .lower-content .text p{
  margin-bottom:20px; 
}

.large-blog-news .lower-content h4 {
  color:#222;
  font-weight: 400;
  margin-bottom: 10px;
}
.large-blog-news .lower-content h5 {
  text-transform: uppercase;
  color: #f2704E;
  font-size: 14px;
  margin-bottom: 15px;
}


.large-blog-news .lower-content h4 a{
  color:#222;
  transition: all 300ms ease;
  font-size: 24px;
}

.large-blog-news .lower-content h4 a:hover{
  color:#f2704E;
  transition: all 300ms ease;
}

.large-blog-news .lower-content .post-meta{
  color: #848484;
  font-size:16px;
  margin-bottom: 15px;
}
.large-blog-news .lower-content .thm-btn {
  background: transparent;
  color: #f2704E;
  border-color: #f4f4f4;
}
.large-blog-news .lower-content .thm-btn:hover {
  background: #f2704E;
  color: #fff;
  border-color: #f2704E;
}


.large-blog-news .img-holder {
    display: block;
    overflow: hidden;
    position: relative;
}

.large-blog-news .img-holder img {
    transform: scale(1);
    transition: all 0.5s ease 0s;
    width: 100%;
}

.large-blog-news .img-holder:hover img {
    transform: scale(1.1);
}
.large-blog-news .overlay {
    background-color: rgba(16, 37, 46, 0.9);
    text-align: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    color: #fff;
    opacity: 0;
    filter: alpha(opacity=0);
    transition: all 450ms ease-out 0s;
    transform: rotateY(180deg) scale(0.5,0.5);
}

.large-blog-news .img-holder:hover .overlay {
    opacity: 1;
    filter: alpha(opacity=100);
    transform: rotateY(0deg) scale(1,1);
}
.large-blog-news .overlay .box {
    display: table;
    height: 100%;
    width: 100%;
}
.large-blog-news .overlay .box .content {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
}
.large-blog-news .img-holder .overlay .box .content a i {
    background: #f2704E;
    border-radius: 50%;
    color: #ffffff;
    display: inline-block;
    font-size: 16px;
    height: 45px;
    line-height: 48px;
    transition: all 500ms ease 0s;
    width: 45px;
}

.large-blog-news .img-holder .overlay .box .content a:hover i {
    background: #f2704E;
}


.large-blog-news.single-blog-post .img-holder:hover img {
  transform: scale(1);
}

.blog-section .outer-box {
  padding-left: 80px;
}



/*side-bar-blog*/

.blog-sidebar {
  position: relative;
}
.category-style-one {
  position: relative;
}
.category-style-one ul {
  margin-top:22px;
  margin-bottom: 35px;
  padding: 10px 30px;
  border: 1px solid #f4f4f4;
  border-left: 2px solid #f2704E;
}
.category-style-one ul li {border-bottom: 1px solid #eaeaea;margin: 0;}
.category-style-one ul li:last-child {
  border:none;
}
.category-style-one ul li a {
  font-weight: normal;
  color: #848484;
  position: relative;
  width: 100%;
  transition: .5s ease;
  line-height: 40px;
  display: block;
}
.category-style-one ul li a:hover {
  color: #f2704E;
  transition: .5s ease;
}


/*post-thumb*/


.popular-post {
    position: relative;
}

.popular-post .item {
    position: relative;
    font-size: 14px;
    margin-bottom: 10px;
    min-height: 50px;
    padding: 0px 0px 25px 90px;
    color: #cccccc;
    border-bottom: 1px solid #f1f1f1;
}

.popular-post .item:last-child{
    border-bottom: 0px;
}
.popular-post .item .post-thumb{
    position:absolute;
    left:0px;
    top: 16px;
    width: 75px;
    height: 75px;
}

.popular-post .item .post-thumb img{
    width:100%;
    display:block;  
}


.popular-post .item .post-thumb a{
  position: relative;
}
.popular-post .item .post-thumb a:after {
    position: absolute;
    content: "\f0c1";
    font-family: FontAwesome;
    left: 0px;
    top: 0px;
    text-align: center;
    line-height: 80px;
    width: 100%;
    height: 100%;
    background: rgba(72, 199, 236, 0.9);
    color: #ffffff;
    opacity: 0;
    transition: .5s ease;
}
.popular-post .item .post-thumb:hover a:after {
  opacity: 1;
  transition: .5s ease;
}


.popular-post .item h5{
    position: relative;
    top: 8px;
    font-size: 16px;
    font-weight: 600;
    margin: 0px 0px 17px;
    line-height: 26px;
    color:#333;
    transition: .5s ease;
}
.popular-post .item h5:hover{
  color: #f2704E;
  transition: .5s ease;
}


.recent-posts.post-thumb .post .post-info{
    font-size: 14px;
    font-weight:400;
    margin:0px;
    line-height:1.6em;
    color: #9c9c9c;
}
.popular-post .item .post-info {
  color: #f2704E;
  font-size: 16px;
}

.popular-post .item .post-info i {
    margin-right: 10px;
}




/*archive*/

.sidebar-archive .bootstrap-select {
    background: #f7f7f7;
    color: #848484;
    padding: 10px 0;
    font-size: 16px;
}
.sidebar-archive .btn-group.open .dropdown-toggle {
    box-shadow: none;
}
.sidebar-archive .btn-group.open .dropdown-toggle:focus {
  border: none;
  outline: none;
}
.sidebar-archive  .bootstrap-select .dropdown-toggle:focus {
  outline: none !important;
}
.sidebar-archive  .dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
  background-color: #f2704E;
  color: #fff;
}
.sidebar-archive .form-control:focus {
  border-color: #f2704E;
  box-shadow: none;
}


/*instagram*/
.sidebar-intsgram ul {
  margin-left: -5px;
  margin-right: -5px;
}
.sidebar-intsgram ul li {
  float: left;
  width: 33.333333333333333333%;
  padding: 5px 4px;

}

.sidebar-intsgram ul li .inner-box {
  display: block;
  overflow: hidden;
  position: relative;
}
.sidebar-intsgram ul li .inner-box .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(72, 199, 236, 0.9);
  transform: translate3d(0, 100px, 0);
  opacity: 0;
  visibility: hidden;
  transition: transform .4s ease, opacity .4s ease, visibility .4s ease;
}
.sidebar-intsgram ul li .inner-box .overlay .box {
  display: table;
  width: 100%;
  height: 100%;
}
.sidebar-intsgram ul li .inner-box .overlay .box .content {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.sidebar-intsgram ul li .inner-box .overlay .box .content a {
  font-size: 0;
  color: #fff;
  width: 100%;
  height: 100%;
}
.sidebar-intsgram ul li:hover .inner-box .overlay {
  transform: translate3d(0, 0, 0);
  opacity: 1;
  visibility: visible;
}






/*pacebook feed*/

.facebook-feed {
  position: relative;
  background: #f7f7f7;
  overflow: hidden;
}

.facebook-feed .img-box {
  position: relative;
}

.facebook-feed .overlay {
    position: absolute;
    margin: 10px;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}


.facebook-feed .inner-box {
  position: relative;
  padding-left: 70px;
  margin-bottom: 32px;
}

.facebook-feed .inner-box .logo {
  position: absolute;
  left: 0;
  top: 0;
}
.facebook-feed .inner-box h4 {
  color: #fff;
  font-weight: 600;
  position: relative;
  margin-top: 0px;
  margin-bottom: 3px;
}
.facebook-feed .inner-box .like {
  color: #fff;
}

.facebook-feed .link a {
  color: #58606f;
  background: #fff;
  display: inline-block;
  line-height: 22px;
  font-size: 13px;
  text-align: center;
  padding: 0 10px;
  border: 1px solid #eaeaea;
}
.facebook-feed .link a i.fb-icon {
  color: #fff;
  background: #39579b;
  font-size: 12px;
  padding: 2px 3px 0 4px;
  position: relative;
  top: 2px;
  margin-right: 5px;
}

.facebook-feed .link a i.mail {
  color: #6a7081;
  margin-right: 6px;
}

.facebook-feed .like-people {
  background: #fff;
  margin: 20px 10px;
  padding: 10px;
}







/*single-blog*/




/*---------------- Author -------------*/

.single-blog-post .author {
  background-color:#ffffff;
  position:relative;
  margin-bottom: 35px;
  margin-top: 40px;
}
.single-blog-post .author img {
  position:absolute;
}
.single-blog-post .author-comment {
  margin-left:340px;
  padding-left:30px;
}

.single-blog-post .author-comment .quote {
  font-size: 25px;
  color: #f1f1f1;
  margin-bottom: 5px;
}
.single-blog-post .author-comment h5 {
  font-weight: 600;
  margin: 16px 0 5px 0;
  text-transform: uppercase;
  font-size: 18px;
}
.single-blog-post .author-comment p.a-title {
  color: #f2704E;
  font-size: 16px;
}



/*share-box*/

.share-box {
  background: #ffffff;
  padding: 0px 0px 12px 0px;
  margin-bottom: 20px;
}
.share-box .tag-box span {
  font-size: 14px;
  color: #3F3E3E;
}
.share-box .tag-box a {
  color: #f2704E;
  font-size: 16px;
  transition: .3s ease;
  line-height: 30px;
  text-transform: capitalize;
}
.share-box .tag-box a:hover {
  color: #f2704E;
  transition: .3s ease;
}

.share-box .social-box span {
  color: #252525;
  margin-right: 13px;
  font-size: 14px;
  font-weight: 600;
}
.share-box .social {
  display: inline-block;
  margin-bottom: 0;
  margin-left: -2.5px;
}
.share-box .social > li {
  padding-right: 0;
  margin: 0;
  padding-left: 0;
}
.share-box .social a {
  width: 40px;
  border-radius: 50%;
  height: 40px;
  color: #BCBCBC;
  text-align: center;
  margin: 0 3px;
  border: 1px solid #eee;
  display: block;
  line-height: 41px;
  transition: all 0.5s ease;
}
.share-box .social a:hover {
  color: #ffffff;
  background: #f2704E;
  border-color: #f2704E;
}



.share-box .tag-box li {
    margin: 0;
    display: inline-block;
}





.post-author{
  position:relative;
  color:#777777;
  margin-bottom: 64px;
}


.post-author .inner-box{
  position:relative;
  padding: 33px 35px 12px 155px;
  min-height:160px;
  background:#f4f4f4;
}

.post-author .inner-box .author-thumb{
  position:absolute;
  left:35px;
  top:35px;
  width: 85px;
  height: 85px;
  border-radius:2px;
}

.post-author .inner-box .author-thumb img{
  display:block;
  width:100%; 
}

.post-author h4{
  position:relative;
  margin: 0px 0px 16px;
  font-size:18px;
  text-transform:capitalize;
  font-weight: 600;
  color: #252525;
}
.post-author ul li{
  margin: 0;
  margin-top: 10px;
}
.post-author ul li a {
  color: #848484;
}
.post-author ul li:hover a {
  color: #f2704E;
}


.news .blogList_single_post .post-author h4 .author-designation{
  position:relative;
  color:#a8c41b;
  font-style:italic;
  font-family:'Lato',sans-serif;
}

.post-author .text{
  line-height:1.8em;
  color:#777777;
}

.post-author .text a{
  color:#777777;
  text-decoration:underline;  
}

.post-author .text a:hover{
  color:#a8c41b;  
}




/*review-tab*/

.single-blog .product-review-tab {
  margin: 0 27px;
}
.product-review-tab .item_review_content {
  border-bottom: 1px solid #f7f7f7;
  padding-bottom: 40px;
  margin-bottom: 40px;
  position: relative;
  padding-left: 80px;
}
.product-review-tab .item_review_content .img_holder {
  position: absolute;
  left: 0;
  top: 0;
}
.product-review-tab .add_your_review {
  margin: 50px 0 50px 0px;
}

.product-review-tab .add_your_review>span {
  display: block;
  margin: 36px 0 14px 0;
  color: #a2a2a2;
}
.product-review-tab .add_your_review ul,
.product-review-tab .add_your_review ul li {
  display: inline-block;
  color: #a2a2a2;
}
.product-review-tab .add_your_review ul.rating:hover li,
.product-review-tab .add_your_review ul.rating.active li{
  color: #f2704E;
  transition: .5s ease;
}
.news .blogList_single_post .post .product-review-tab .add_your_review ul.rating li {
  font-size: 12px;
  margin-right: 3px;
  color:#d6d6d6;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.news .blogList_single_post .post .product-review-tab .add_your_review ul.active li {
  color: #ab7442;
}
.news .blogList_single_post .post .product-review-tab .add_your_review ul:hover li{
  color: #ab7442;
}
.product-review-tab .add_your_review ul {
  line-height: 20px;
  border-right: 1px solid #d6d6d6;
  padding-right: 6px;
  margin-right: 5px;
}
.product-review-tab .add_your_review ul.fix_border {
  border:none;
}
.product-review-tab .add_your_review form {
  margin-top:31px;
}
.product-review-tab .add_your_review form label {
  color: #a2a2a2;
  font-size: 16px;
  margin-bottom: 5px;
}
.product-review-tab .add_your_review form input {
  height:54px;
  padding: 0 15px 0 19px;
}
.product-review-tab .add_your_review form textarea {
  max-width: 100%;
  min-height: 165px;
  padding: 15px 17px 5px 19px;
}
.product-review-tab .add_your_review form input,
.product-review-tab .add_your_review form textarea {
    width:100%;
    border:1px solid #f0f0f0;
    margin-bottom: 30px;
    color:#9e9e9e;
    resize: none;
}

.news.single-blog .blogList_single_post .post ul li:before {
  display: none;
}












/*** 

====================================================================
    Testimonials Section style
====================================================================

***/

.d-flexjust-c{
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  justify-content: space-between;
}

.testimonials-section{
    position:relative;
    background: #10252e;
}

.testimonials-section .section-title:before {
    background: #2a3a40;
}


.testimonials-section .section-title h2 {
    color: #fff;
}


.testimonials-section .slide-item{
    position:relative;
    margin: 15px;
    padding: 30px;
    padding-right: 10px;
    background: #142b35;
    transition: .5s ease;
}
.testimonials-section .slide-item:before {
    /* width: 50px; */
    height: 50px;
    pointer-events: none;
    position: absolute;
    content: '';
    bottom: 0;
    right: 0;
    background: white;
    background: linear-gradient(315deg, #222 45%, #333 50%, #333333 56%, #333 80%);
    transition-duration: 0.3s;
    transition-property: width, height;
}
.project-content.testimonials-section .slide-item{
    margin-bottom: 50px;
}
.padd-bottom-30{
  padding-bottom: 30px ;
}

.testimonials-section .slide-item:hover {
  transition: .5s ease;
}



.testimonials-section .quote {
    position: absolute;
    font-size: 30px;
    right: 15px;
    bottom: 15px;
    color: #19333e;
    transition: .5s ease;
}
.testimonials-section .slide-item:hover .quote {
  transition: .5s ease;
}

.testimonials-section .author{
    position:relative;
    padding: 15px 0px 0px 100px;
    margin-bottom: 43px;
    color:#c9c9c9;
    line-height:20px;
}

.testimonials-section .author .img-box {
    position:absolute;
    left:0px;
    top:6px;
}
.testimonials-section .author .img-box img {
    border-radius: 50%;
}


.testimonials-section .author h4 {
  position:relative;
  color: #ffffff;
  font-size: 20px;
}




.testimonials-section .author p {
    position:relative;
    color: #f2704E;
}

.testimonials-section .slide-text{
  margin-top: 15px;
  margin-left: 0px;
}

.testimonials-section .slide-text p {
}


.testimonials-section .owl-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    left: 0;

}

/*nav*/
.testimonials-section .owl-carousel {
  position: inherit;
}
.testimonials-section .owl-theme .owl-nav {
    margin: 0;
    padding: 0;
}

.testimonials-section .owl-theme .owl-nav [class*=owl-] {
    color: #2a3a40;
    font-size: 24px;
    margin: 0;
    padding: 0;
    background-color: transparent;
    padding: 0 5px;
    display: inline-block;
    transition: color .3s ease;
}

.testimonials-section .owl-theme .owl-prev {
    float: left;
    width: 20%;
    text-align: center;
}
.testimonials-section .owl-theme .owl-prev i {
    width: 50px;
    height: 50px;
    line-height: 46px;
    border: 2px solid #26373d;
    transition: .5s ease;
}
.testimonials-section .owl-theme .owl-prev i:hover {
    color: #f2704E;
    border-color: #f2704E;
    transition: .5s ease;
}
.testimonials-section .owl-theme .owl-next {
    float: right;
    width: 20%;
    text-align: center;
}

.testimonials-section .owl-theme .owl-next i {
    width: 50px;
    height: 50px;
    line-height: 46px;
    border: 2px solid #26373d;
    transition: .5s ease;
}
.testimonials-section .owl-theme .owl-next i:hover {
    color: #f2704E;
    border-color: #f2704E;
    transition: .5s ease;
}



/*** 

====================================================================
    client Section style
====================================================================

***/

.clients-section .section-title {
  margin-bottom: 10px;
}
.clients-section .tooltip-inner {
  background: #f2704E;
}
.clients-section .tooltip.top .tooltip-arrow {
  border-top-color: #f2704E;
}

.clients-section .owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0px, 0px, 0px);
    padding-top: 40px;
}
.clients-section .owl-theme .owl-nav {
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: 100%;
    right: 0;
    margin: 0;
    margin-bottom: 36px;
}

.clients-section .owl-theme .owl-nav [class*=owl-] {
    color: #c5c5c5;
    font-size: 24px;
    margin: 0;
    padding: 0;
    background-color: transparent;
    height: 40px;
    width: 40px;
    border: 2px solid #f7f7f7;
    line-height: 36px;
    text-align: center;
    margin-left: 10px;
    display: inline-block;
    transition: color .3s ease;
}

.clients-section .owl-theme .owl-nav [class*=owl-]:hover {
    color: #f2704E;
    border: 2px solid #f2704E;
    transition: .5s ease;
}




/*** 

====================================================================
    whychoos-us Section style
====================================================================

***/
.whychoos-us {
  background: #f7f7f7;
}

.whychoos-us .section-title {
  padding: 0;
}
.whychoos-us .section-title p {
  font-size: 18px;
  margin-top: 50px;
}

.whychoos-us .item {
  position: relative;
  margin-bottom: 50px;
  padding-bottom: 5px;
}



.whychoos-us .item p:before,
.whychoos-us .item:before {
  width: 100%;
  height: 2px;
  position: absolute;
  content: "";
  background: #eaeaea;
  left: 0;
  bottom: 0;
}

.whychoos-us .item p:after,
.whychoos-us .item:after {
  width: 100%;
  height: 2px;
  position: absolute;
  content: "";
  background: #f2704E;
  left: 0;
  bottom: 0;
  transform: scaleX(0);
  transition: .5s ease;
}


.whychoos-us .item:hover p:after,
.whychoos-us .item:hover:after {
  transform: scaleX(1);
  transition: .5s ease;
}




.whychoos-us .icon_box {color: #f2704E;font-size: 50px;}

.whychoos-us h4 {
  font-size: 20px;
  color: #222;
  margin-bottom: 15px;
}

.whychoos-us .item p {
  position: relative;
  padding-bottom: 24px;
}

.whychoos-us .count {
  position: absolute;
  right: 0;
  top: 56px;
  font-size: 40px;
  color: #f0f0f0;
}

/*** 

====================================================================
    service Section style
====================================================================

***/

.service {
  position: relative;
}

.service .item {
  padding: 4px;
  margin-bottom: 5px;
  border: 1px solid #f4f4f4;
}

.service.style-2 .column{
  margin-bottom: 50px;
}

.service .item:before {
    pointer-events: none;
    position: absolute;
    z-index: -1;
    content: '';
    top: 98%;
    left: 10%;
    right: 10%;
    height: 15px;
    width: 80%;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, transparent 80%);
    transition-duration: 0.3s;
    transition-property: transform, opacity;
    z-index: 9;
}
.service .item:hover:before {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    transform: translateY(5px);
}

.service .item .img-box {
  position: relative;
  overflow: hidden;
}

.service .item .img-box img {
  width: 100%;
}
.service .item .default-overlay-outer{
  opacity:0;
  top:-100%;
  background: rgba(16, 37, 46, 0.9);
}

.service .item:hover .default-overlay-outer{
  top:0;
  opacity:1;  
}

.service .item .thm-btn {
  padding: 0 25px;
  line-height: 34px; 
}

.service .owl-carousel .owl-stage-outer {
  padding-bottom: 50px;
}


.content-225 .content{
    min-height: 225px;
}

.service .content {
  padding: 12px 5px;
}

.service .content h5 {color: #f2704E;
  font-size: 14px;}

.service .content h4,.service .content h2 {padding: 15px 0;
  font-size: 20px;
  color: #222;
}

.service .content p {

}


.service .owl-theme .owl-nav {
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: 100%;
    right: 0;
    margin: 0;
    margin-bottom: 65px;
}

.service .owl-theme .owl-nav [class*=owl-] {
    color: #202f35;
    font-size: 20px;
    margin: 0;
    padding: 0;
    background-color: transparent;
    height: 40px;
    width: 18px;
    line-height: 36px;
    font-weight: 700;
    text-align: center;
    margin-left: 1px;
    display: inline-block;
    transition: color .3s ease;
}

.service .owl-theme .owl-nav [class*=owl-]:hover {
    color: #f2704E;
    transition: .5s ease;
}




/************************** 
 service-style3 styles 
***************************/


.service-style3 {
  position: relative;
  background: #f7f7f7;
}


.service-style3 .bottom-content .icon_box {
  position: relative;
  margin-right: 15px;
  color: #222;
  padding-bottom: 27px;
  margin-bottom: 18px;
}
.service-style3 .bottom-content .icon_box:after {
    content: "";
    position: absolute;
    top: 86px;
    left: 0;
    width: 60px;
    height: 1px;
    background: #eaeaea;
}
.service-style3 .bottom-content .icon_box:after {
    content: "";
    position: absolute;
    top: 86px;
    left: 0;
    width: 60px;
    height: 1px;
    background: #eaeaea;
}
.service-style3 .overlay-box .title:after {
    content: "";
    position: absolute;
    top: 45px;
    left: 0;
    width: 60px;
    height: 1px;
    background: #eaeaea;
}

.service-style3 .bottom-content .icon_box span {
  font-size: 55px;
  color: #f2704E;
}
.service-style3 .bottom-content h4 {
  float: left;
  margin-top: 13px;
  color: #222;
  transition: .5s ease-in-out;
  font-size: 20px;
}

.service-style3 .bottom-content p {
  color: #f2704E;
}
.service-style3 .overlay-box p {
  position: relative;
  color: #f2704E;
  margin-bottom: 45px;
}
.service-style3 .item {
  position: relative;
  background: #fff;
}
.service-style3 .item:before {
  pointer-events: none;
  position: absolute;
  z-index: -1;
  content: '';
  top: 98%;
  left: 10%;
  right: 10%;
  height: 15px;
  width: 80%;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, transparent 80%);
  transition-duration: 0.3s;
  transition-property: transform, opacity;
  z-index: 9;
}
.service-style3 .item:hover:before {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  transform: translateY(5px);
}





.service-style3 .overlay-box {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 0%;
  left: 0;
  border: 1px solid #f2704E;
  visibility: hidden;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  top: 0px;
  transition: .3s ease-out;
  background: rgba(255, 255, 255, 0.9);
}
.service-style3 .item {
  margin-bottom: 50px;
}
.service-style3 .item:hover .overlay-box {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  visibility: visible;
  transition: .5s ease-in-out;
  height: 100%;
}

.service-style3 .overlay-box .inner-box {
  position: relative;
  padding: 20px 30px 0px;
}
.service-style3 .overlay-box .icon_box {
  margin-right: 15px;
  color: #fff;
  position: absolute;
  right: -20px;
  top: 0;
}
.service-style3 .overlay-box .icon_box span {
  font-size: 176px;
  color: #f7f7f7;
}


.service-style3 .overlay-box h4 {
  margin-top: 13px;
  position: relative;
  margin-bottom: 8px;
  color: #222;
  transition: .5s ease-in-out;
}


.service-style3 .overlay-box .text {
  color: #fff;
  margin: 20px 0;
  position: relative;
}

.service-style3 .overlay-box .text p {color: #848484;}



.service-style3 .bottom-content {
  position: relative;
  padding: 32px 30px;
  transition: .5s ease;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  visibility: visible;
}
.service-style3 .item:hover .bottom-content {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  visibility: hidden;
  transition: .5s ease;
}


.service-style3 .owl-theme .owl-nav {
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: 100%;
    right: 0;
    margin: 0;
    margin-bottom: 65px;
}

.service-style3 .owl-theme .owl-nav [class*=owl-] {
    color: #202f35;
    font-size: 20px;
    margin: 0;
    padding: 0;
    background-color: transparent;
    height: 40px;
    width: 18px;
    line-height: 36px;
    font-weight: 700;
    text-align: center;
    margin-left: 1px;
    display: inline-block;
    transition: color .3s ease;
}

.service-style3 .owl-theme .owl-nav [class*=owl-]:hover {
    color: #f2704E;
    transition: .5s ease;
}

/*service single*/



/*accordion-style-2*/


.accordion-style-two .accordion .acc-btn .toggle-icon {
    color: #f2704E;
    display: block;
    height: 24px;
    line-height: 24px;
    position: absolute;
    left: 20px;
    top: 15px;
    width: 24px;
}


.accordion-style-two .accordion .acc-btn .toggle-icon .plus:before {
    font-size: 18px;
    top: 3px;
}

.accordion-box.accordion-style-two .accordion .acc-btn .toggle-icon .minus:before {
    font-size: 18px;
    top: 3px;
}
.accordion-box.accordion-style-two {
  border: 0px;
}
.accordion-style-two .accordion .acc-btn {
    font-size: 18px;
    font-weight: 600;
    padding: 14px 30px 14px;
    padding-left: 55px;
    background: #ffffff;
    margin: 0;
    padding-right: 50px;
    border-top: 2px solid #f4f4f4;
}
.accordion-style-two .accordion .acc-btn.active {
    color: #f2704E;
    background: #131d33;
    border: 0px;
}

.accordion-style-two .accordion .acc-btn.active p {
}


/*over view*/

.over-view {
  background: #f7f7f7;
  padding: 18px 0;
  margin-top: 10px;
}
.over-view a {
  color: #fa9928;
  text-transform: uppercase;
  margin: 9px 0;
  font-size: 14px;
}
.over-view a i {
  padding-right: 8px;
}

.analysis-result {
  background: #f7f7f7;
  padding: 20px 20px 37px;
  margin-top: 10px;
}
.analysis-result h4 {
  font-size: 20px;
  margin-bottom: 5px;
}


.analysis-chart {
  margin: 30px 0 50px;
}
.analysis-chart h4 {
  font-size: 20px;
  margin-top: 20px;
}

.intro-img {
  position: relative;
}

.intro-img .img-box {
  position: relative;
  margin-bottom: 30px;
}

.intro-img .img-box h5 {
    position: absolute;
    font-size: 14px;
    background: #f7f7f7;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    padding: 15px 0;
    color: #848484;
}


/*growth service*/

.growth-item {
  border: 2px solid #f4f4f4;
  position: relative;
  margin-bottom: 30px;
  margin-top: 40px;
  padding-bottom: 30px;
  transition: .5s ease;
}

.growth-item .icon_box {
  border-right: 2px solid #e3e3e3;
  width: 65px;
  height: 65px;
  background: #fff;
  position: relative;
  margin: 0 auto;
  margin-top: -33px;
  border-radius: 50%;
  border: 1px solid #f4f4f4;
  line-height: 73px;
  color: #f2704E;
  font-size: 30px;
  text-align: center;
}

.growth-item h4 {
  color: #222;
  margin-top: 18px;
  transition: .5s ease;
}
.growth-item h4:hover {
  color: #f2704E;
  transition: .5s ease;
}

.growth-item .content {
  background: #fff;
}


/*tab*/



/*tab*/
.tabs-section{
  position:relative;
  padding:130px 0px 100px;
}

.tabs-section .column{
  margin-bottom:30px; 
}

.tabs-section .image-box img{
  width:100%;
  display:block;
}

.tabs-style-two .tab-buttons{
  position:relative;
}

.tabs-style-two .tab-buttons .tab-btn{
  position:relative;
  display:inline-block;
  padding:9px 60px;
  margin: 0px -2px 0px;
  width: 25%;
  cursor:pointer;
  font-size:18px;
  text-align:center;
  font-weight: 400;
  color: #222;
  border: 1px solid #f4f4f4;
  text-transform:capitalize;
  transition:all 300ms ease;
}

.tabs-style-two .tab-buttons .tab-btn.active-btn{
  color: #f2704E;
  background: #fff;
  border-color: #f2704E;
  border-bottom: 0;
}

.tabs-style-two .tabs-content{
  position:relative;
  padding:40px;
  border:1px solid #f2f2f2;
  border-top: 0;
}

.tabs-style-one .tab .text-content{
  position:relative;
}

.tabs-style-two .tab .text-content .text{width: 59%;margin-left: 30px;}
.tabs-style-two .tab .text-content .text p {
  margin-bottom: 10px;
}
.tabs-style-two.tabs-box{
  padding-right:15px;
}

.tabs-box .tabs-content .tab{
    position: relative;
    display: none;
}

.tabs-box .tabs-content .active-tab {
    display: block;
}


.tabs-outer{
  position:relative;
  margin-top:60px;
}
.tabs-outer.style-two {
  margin: 0px;
}
.tabs-outer .tabs-box .tab-buttons .tab-btn{
  position:relative;
  padding:9px 30px;
}

.tabs-outer .text-content .image img{
  width:100%;
}

.tabs-outer .tabs-content{
  padding:30px 30px 20px;
}
.tabs-outer.style-two .tabs-content{
  padding: 0px;
}

.tabs-outer .tabs-content .column .text{
  margin-bottom:10px;
}








/*** 

====================================================================
    service-single Section style
====================================================================

***/

.service-catergory {
  border: 1px solid #f4f4f4;
  margin: 0;
  padding: 0;
  list-style: none;
}
.service-catergory li {
  list-style: none;
  position: relative;
  padding: 10px 0;
  border-bottom: 1px solid #f4f4f4;
  margin-bottom: 2px;
}
.service-catergory li:last-child {
  border-bottom: 0px;
}
.service-catergory li .view-all-icon {
  position: absolute;
  right: 20px;
  top: 22px;
  color: #848484;
}
.service-catergory li.active {
}
.service-catergory li.active a {
  color: #f2704E;
}

.service-catergory li a {
  display: block;
  font-size: 18px;
  color: #222;
  line-height: 30px;
  border-left: 5px solid transparent;
  font-weight: 400;
  padding-left: 34px;
  transition: all 0.5s ease;
  position: relative;
}
.service-catergory li a:hover {
  color: #f2704E;
}

.service-single blockquote {
    padding: 20px 20px;
    margin: 0 0 20px;
    padding-left: 30px;
    font-size: 18px;
    border-left: 5px solid #f7f7f7;
    font-style: italic;
    color: #f2704E;
    font-family: "Poppins", sans-serif;
}

.service-catergory li a:after {
  position: absolute;
  content: "\f067";
  font-family: FontAwesome;
  font-size: 12px;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  left: -5px;
  top: 5px;
  color: #848484;
  background: #f4f4f4;
}
.service-catergory li.active a:after {
  color: #fff;
  background: #f2704E;
}

.service-catergory li.active a:before {
  position: absolute;
  content: "\f105";
  font-family: FontAwesome;
  font-size: 18px;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  right: 15px;
  top: 5px;
  color: #f2704E;
}

/*brochures*/

.brochures-list {
  margin-top: 10px;
}
.brochures-lists li a {
    background: #fff;
    line-height: 52px;
    margin-bottom: 10px;
    display: block;
    color: #222;
    height: 55px;
    border: 2px solid #f4f4f4;
    position: relative;
    transition: .5s ease;
}

.brochures-lists li a:hover {
    color: #ffffff;
    background: #f2704E;
    border-color: #f2704E;
    transition: .5s ease;
}

.brochures-lists li a span {
    font-size: 16px;
    margin: 0 20px;
}
.brochures-lists li a i::before {
    font-size: 18px;
    padding-right: 15px;
    position: absolute;
    right: 0;
    top: 18px;
    color: #ffffff;
}


.brochures .img-box {
  padding: 22px 0;
  border: 1px solid #f4f4f4;
}

.author-details.style-2 .item {
    padding: 20px 20px 20px;
    border-bottom: 1px solid rgba(51, 51, 51, 0.08);
    background: transparent;
    margin: 0;
    padding: 0px 0 40px;
}


/*growth-service*/

.growth-service .item {
  background: #f7f7f7;
  padding: 30px 0;
  border: 1px solid #eaeaea;
}
.growth-service .item span {
  color: #f2704E;
  font-size: 48px;
  transition: .5s ease;
}
.growth-service .item h4 {
  font-size: 20px;
  margin-bottom: 5px;
  padding-top: 20px;
}

/*style-2*/

.growth-service.style-2 {
  background-color: #f7f7f7;
}


.growth-service.style-2 .item {
    background: #fff;
    padding: 45px 15px;
    border: none;
    transition: .5s ease
}
.growth-service.style-2 .item.active,
.growth-service.style-2 .item:hover {
  background-color: #f2704E;
  transition: .5s ease
}
.growth-service.style-2 .item h4 {
    font-size: 20px;
    margin-bottom: 18px;
    padding-top: 20px;
    transition: .5s ease;
}
.growth-service.style-2 .item span {
  font-size: 70px;
}
.growth-service.style-2 .item.active span,
.growth-service.style-2 .item:hover span,
.growth-service.style-2 .item.active h4,
.growth-service.style-2 .item:hover h4,
.growth-service.style-2 .item.active p,
.growth-service.style-2 .item:hover p {
  color: #fff;
  transition: .5s ease;
}
/*benifit*/
.benifit li {
    position: relative;
    color: #848484;
    padding-left: 20px;
    margin: 15px 0;
}

.benifit li:after {
    position: absolute;
    content: '';
    background: #f2704E;
    height: 8px;
    width: 8px;
    left: 0;;
    top: 7px;
}

.benifit {margin-bottom: 10px;}
/*benifit-list*/

.benifit-list li {
  margin: 5px 0;
  color: #848484;
  font-size: 14px;
}
.benifit-list li i {
  color: #f2704E;
  margin-right: 20px;
}
.service-single .accordion-box .accordion .acc-content {
  padding: 2px 0 8px;
}
.service-single .accordion {
  margin-bottom: 5px;
}

.default-form.service-form textarea {
    height: 226px;
}



.title-2 {
  line-height: 28px;
}








/*** 

====================================================================
    about Section style
====================================================================

***/

.about-faq .default-form .thm-btn {
  width: auto;
}



.accordion-box {
}

.accordion {
    margin-bottom: 10px;
    position: relative;
}

.accordion .acc-btn .left-icon {
    position: absolute;
    left: 0;
    top: 0;
    background: #f1f1f1;
    width: 60px;
    height: 100%;
    color: #9c9c9c;
    font-size: 20px;
    text-align: center; 
}
.accordion .acc-btn.active .left-icon {
    color: #fff;
}

.accordion .left-icon span {
    position: relative;
    top: 18px;  
}

.accordion .acc-btn {
    cursor: pointer;
    font-size: 18px;
    font-weight: 600;
    padding: 16px 30px 16px;
    margin: 0px;
    padding-right: 50px;
    border: 1px solid #f4f4f4;
}

.accordion .acc-btn.active {
  border-color: #f2704E;
  border-bottom: 0px;
}

.accordion .acc-btn p {
    color: #252525;
    font-family: 'Hind', sans-serif;
}
.accordion .acc-btn.active p {
    color: #f2704E;
}

.accordion .acc-btn.pr {
    position: relative;
}

.accordion .acc-btn .img-box {
    display: none;
}
.accordion .acc-btn.active .img-box {
    display: block;
}


.accordion .acc-btn .toggle-icon {
    color: #9c9c9c;
    display: block;
    font-weight: 100;
    height: 24px;
    line-height: 24px;
    position: absolute;
    right: 20px;
    top: 15px;
    width: 24px;
}
.accordion .acc-btn .toggle-icon.left {
    color: #9c9c9c;
    display: block;
    font-weight: 100;
    height: 24px;
    line-height: 24px;
    position: absolute;
    right: 20px;
    bottom: 20px;
    left: 30px;
}
.accordion .acc-btn .toggle-icon .plus:before {
    font-size: 24px;
    font-weight: 500;
    transition: all 0.3s ease 0s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    color: #252525;
}

.accordion-box .accordion .acc-btn.active .toggle-icon .plus {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}


.accordion-box .accordion .acc-btn .toggle-icon .minus:before {
    font-size: 24px;
    font-weight: 500;
    transition: all 0.3s ease 0s;
    position: absolute;
    top: 0;
    left: 0;
    color: #f2704E;
    width: 100%;
}
.accordion-box .accordion .acc-btn .toggle-icon .minus{
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.accordion-box .accordion .acc-btn.active .toggle-icon .minus {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

.accordion-box .accordion .acc-content {
    color: #9c9c9c;
    display: none;
    padding: 15px 15px 15px 0px;
    background: #ffffff;
    border-top: 0px;
}

.accordion-box .accordion .acc-content.collapsed {
    display: block;

}

.accordion-box .accordion .acc-content p {
    margin: 0;
}


.accordion-box .accordion .acc-btn.active .toggle-icon .minus {
  color: #121d2f;
}

.single-faq-bg {
  position: relative;
}
.single-faq-bg:before {
}



.about-faq .default-form input[type="text"], 
.about-faq .default-form input[type="email"], 
.about-faq .default-form input[type="password"], 
.about-faq .default-form input[type="number"], 
.about-faq .default-form select, 
.about-faq .default-form textarea {
    border: 1px solid #eaeaea;
    height: 50px;
}
.about-faq .default-form .form-control:focus {
    border-color: #f2704E;
    box-shadow: none;
}
.about-faq .default-form-area {
  padding: 40px 30px 10px;
  background: #f7f7f7;
  margin-top: 75px;
}
.about-faq .default-form-area h2 {
  margin-bottom: 30px;
  position: relative;
  font-weight: 400;
}
.about-faq .default-form textarea {
  height: 135px;
}
.accordion-box.style-one .accordion .acc-content {
  padding: 21px 30px;
  background: #f7f7f7;
  border: 1px solid #f2704E;
  border-top: 0px;
}
.accordion-box .accordion {
  margin-bottom: 10px;
}

/*** 

====================================================================
  Fact Counter Section
====================================================================

***/

.fact-counter{
  position:relative;
  padding: 70px 0px 40px;
  background-repeat:no-repeat;
  background-size:cover;
  background-position:center center;
  overflow:hidden;
  color:#ffffff;
  margin-bottom: 3px;
}

.fact-counter:before{
  content:'';
  position:absolute;
  left:0px;
  top:0px;
  width:100%;
  height:100%;
  background: rgba(10, 27, 34, 0.95);
  z-index:0;
}

.fact-counter .auto-container{
  position:relative;
  z-index:1;
}

.fact-counter .column{
  position:relative;
  margin-bottom:40px;
  text-align:center;
  z-index:5;  
}
.fact-counter .item {
  position: relative;
  border: 2px solid #19333e;
  color: #fff;
  padding: 17px 0 25px;
}

.fact-counter .column .icon i {
  font-size: 30px;
  position: absolute;
  bottom: -10px;
  right: 5px;
  margin-bottom:20px;
  color: #19333e;
  transition:all 500ms ease;
  -moz-transition:all 500ms ease;
  -webkit-transition:all 500ms ease;
  -ms-transition:all 500ms ease;
  -o-transition:all 500ms ease;
}

.fact-counter .count-outer{
  position:relative;
  font-size: 36px;
  font-weight: 600;
  padding: 0px 10px 5px;
  font-family: 'Poppins', Sans-serif;
}
.fact-counter .count-outer:before {
}

.fact-counter .column .counter-title{
  position:relative;
  font-size: 18px;
  font-weight: 300;
  font-family: "Hind", Sans-serif;
  color: #f2704E;
}



/*** 

====================================================================
  google map Section
====================================================================

***/
.home-google-map #contact-google-map {
  margin-top: 20px;
  width: 100%;
  height: 420px;
}
.home-google-map .gmnoprint {
    display: none;
}

.contact_us {

}

/*** 

====================================================================
  contact Section
====================================================================

***/


.default-cinfo .icon_box {
  width: 40px;
}
.default-cinfo li + li {
  margin-top: 10px;
  border-top: 1px solid #f1f1f1;
  padding-top: 12px;
}
.default-cinfo li .icon_box,
.default-cinfo li .text-box {
  display: table-cell;
  vertical-align: top;
}

.default-cinfo li .icon_box i {
  font-size: 20px;
  color: #f2704E;
  position: relative;
  top: 7px;
}
.default-cinfo .accordion .acc-btn {
  background: #f7f7f7;
  padding: 14px 30px;
  margin: 0;
}

.default-cinfo .accordion-box {
  border: none;
}
.default-cinfo .accordion {
  border: 1px solid #f7f7f7;
}
.default-cinfo .accordion-box .accordion .acc-content b {
  color: #222;
  font-weight: 600;
}

/*form*/

.default-form {
  position: relative;
}


.default-form p {
  color: #fff;
  font-size: 18px;
  line-height: 34px;
  margin-bottom: 20px;
}
.default-form p span {
  color: #ab7442;
  font-size: 20px;
  font-weight: 600;
}


.default-form .form-box{
  position:relative;
}

.comment-form .form-box{
  padding:25px 25px 0px 25px; 
}

.default-form .form-group{
  position:relative;
  margin-bottom:30px; 
}

.default-form .comment-form .form-box .form-group{
  margin-bottom:25px;
}

.default-form.form-box .row{
  margin-left:-10px;
  margin-right:-10px; 
}

.default-form.form-box .row .form-group{
  padding:0px 0px;  
}

.default-form .form-group .field-label{
  display:block;
  line-height:24px;
  text-transform:uppercase;
  margin-bottom:10px;
  color:#232323;
  font-weight:500;
  font-size:13px;
}

.default-form .form-group .field-label sup{
  color:#ff0000;
  font-size:14px;
}

.default-form input[type="text"],
.default-form input[type="email"],
.default-form input[type="password"],
.default-form input[type="number"],
.default-form select,
.default-form textarea{
  display:block;
  width:100%;
  line-height:24px;
  height:46px;
  font-size:16px;
  box-shadow: none;
  border: 1px solid #f4f4f4;
  padding:12px 15px;
  background: #fff;
  border-radius: 0px;
  transition:all 500ms ease;
  -webkit-transition:all 500ms ease;
  -ms-transition:all 500ms ease;
  -o-transition:all 500ms ease;
}
.default-form textarea{
  height: 115px;
}
.default-form .bootstrap-select {
    border: 1px solid #f4f4f4;
    background: #f7f7f7;
    color: #a2a2a2;
    padding: 10px 0;
    font-size: 16px;
    height: auto;
}
.default-form .btn-group.open .dropdown-toggle {
    box-shadow: none;
}
.default-form .btn-group.open .dropdown-toggle:focus {
  border: none;
  outline: none;
}
.default-form  .bootstrap-select .dropdown-toggle:focus {
  outline: none !important;
}
.default-form  .dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
  background-color: #f2704E;
  color: #fff;
}
.default-form .form-control:focus {
  border-color: #f2704E;
  box-shadow: none;
}



.select-box .btn:after {
  position: absolute;
  content: "\f107";
  font-family: FontAwesome;
  font-size: 16px;
  color: #f2704E;
  width: 20px;
  right: 15px;
  top: 50%;
  margin-top: -11px;
}

 

.default-form .bootstrap-select {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0px;
}

.default-form .g-select {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0px;
}
.default-form .bootstrap-select.btn-group .dropdown-toggle .filter-option {
  color: #848484;
  padding: 13px 15px;
  border: 1px solid #eee;

}

.default-form .bs-caret {
  display: none;
}

.default-form .dropdown-menu {
    border-radius: 0px;
    padding: 0px;
    top: 50px;
    transform: scaleY(0);
        transform-origin: top center;
    transition: .4s ease;
}
.default-form .bootstrap-select.open .dropdown-menu {
  transform: scaleY(1);
  transition: .4s ease;
}

.default-form .dropdown-menu>li>a {
  padding: 8px 15px;
}

.default-form .dropdown-menu>li>a:focus {
    background-color: #fff;
    color: #222;
}
.default-form .dropdown-menu>li>a:hover {
    background-color: #f2704E !important;
    color: #fff !important;
}







/*placeholder*/

.default-form form input::-webkit-input-placeholder {
  color: #9e9e9e;
}
.default-form form input:-moz-placeholder {
  /* Firefox 18- */
  color: #9e9e9e;
}
.default-form form input::-moz-placeholder {
  /* Firefox 19+ */
  color: #9e9e9e;
}
.default-form form input:-ms-input-placeholder {
  color: #9e9e9e;
}


.default-form .alert-success {
    color: #f2704E;
    background: none;
    border: none;
    font-size: 18px;
}


/*author-details*/



.author-details {
  background: #f7f7f7;
  padding: 1px 20px;
}
.author-details .item {
  padding: 20px 20px 20px;
  border-bottom: 1px solid rgba(51, 51, 51, 0.08);
  background: #fff;
  margin: 30px 0;
}
.author-details .item:last-child {
  border-bottom: 0px;
}
.author-details .item .img-box,
.author-details .item .content {
  display: table-cell;
  vertical-align: top;
}

.author-details .item .img-box{
  width: 67px;
}

.author-details .item .content{
  padding-left: 20px;
}

.author-details .item .content p {
  color: #9e9e9e;
  font-size: 14px;
  margin-bottom: 0;
  line-height: 25px;
}
.author-details .item .content p i {
  color: #f2704E;
  margin-right: 5px;
}

.author-details .item h5 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #272727;
}
.author-details .item .content h5 {
  color: #f2704E;
  margin: 0;
}




/*** 

====================================================================
 consultation style
====================================================================

***/
.consultations {
  position: relative;
  padding-bottom: 60px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right center;
}

.consultations .default-form .column {
  padding: 0 10px;
}

.consultations .section-title h2 {
  color: #fff;
}
.consultations .section-title {
    padding-bottom: 21px;
}
.consultations .section-title:after,
.consultations .section-title:before {
  top: 58px;
}
.consultations .section-title:before {
  background: #2a3a40;
}
.consultations .default-form .form-group {
  margin-bottom: 20px;
}

.consultations .default-form .form-group.style-2 {
  padding-right: 90px;
}


.consultations .default-form input[type="text"],
.consultations .default-form input[type="email"],
.consultations .default-form input[type="password"],
.consultations .default-form input[type="number"],
.consultations .default-form select,
.consultations .default-form textarea{
  border: 2px solid #19333e;
  background: transparent;
  height: 50px;
}
.consultations .default-form input:focus{
  border-color: #f2704E;
}
.consultations .default-form textarea{
  height: 190px;
}
.consultations .default-form .bootstrap-select {
  background: transparent;
}

.consultations .default-form .btn.focus, 
.consultations .default-form .btn:focus, 
.consultations .default-form .btn:hover {
  color: #fff;
}

.consultations .default-form .thm-btn {
  width: 70px;
  border: 2px solid transparent;
  line-height: 190px;
  height: 190px;
  position: absolute;
  right: 0;
  top: 0;
  padding: 0;
}
.consultations .select-box .btn:after {
  color: #848484;
}
.consultations .default-form .thm-btn:hover {
  border-color: #fff;
}

.consultations .default-form .bootstrap-select.btn-group .dropdown-toggle .filter-option {
  color: #cacaca;
  border: 2px solid #19333e;
}


.consultations ::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: #cacaca !important;
}
.consultations ::-moz-placeholder { /* Firefox 19+ */
  color: #cacaca !important;
}
.consultations :-ms-input-placeholder { /* IE 10+ */
  color: #cacaca !important;
}
.consultations :-moz-placeholder { /* Firefox 18- */
  color: #cacaca !important;
}


.consultations .contact-info{
  border: 2px solid #f2704E;
  padding: 24px 28px 12px;
  margin-bottom: 20px;
}

.consultations .contact-info .section-title {
  margin-bottom: 25px;
}

.consultations .contact-info .section-title h3 {
  color: #fff;
}

.consultations .contact-info .section-title:after {width: 75px;background: rgba(255, 255, 255, 0.5);top: 41px;}
.consultations .contact-info .section-title:before {display: none;}
.consultations .contact-info .text p {color: #fff;margin-bottom: 22px;}

.consultations .contact-info li{
  position:relative;
  padding-left: 35px;
  margin-bottom: 12px;
  line-height:24px;
  color: #fff;
}

.consultations .contact-info li span {
  position:absolute;
  left:0px;
  top:0px;
  line-height:24px;
  font-size: 16px;
  color: #f2704E;
}










/***
==============================
   8. Team Member styles
============================== 
***/


.our-team {
  position: relative;
}




.single-team-member .img-box {
  position: relative;
  overflow: hidden;
  display: block;
}
.single-team-member .img-box img {
  transition: all 0.5s ease;
  transform: scale(1);
}


.our-team .single-team-member {
  position: relative;
  margin-bottom: 50px;
  display: inline-block;
}




.our-team .single-team-member:before {
  pointer-events: none;
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 0%;
  right: 0;
  opacity: 0;
  background: #f7f7f7;
  transition: .7s ease;
}

.our-team .single-team-member:hover:before {
  opacity: 1;
  height: 100%;
  transition: .7s ease;
}

    
.single-team-member .img-box .overlay {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: rgba(242, 112, 78,0.9);
  width: 100%;
  transform: scale(0.7,0);
  transition: .5s ease-in-out;
}
.single-team-member:hover .img-box .overlay {
  transform: scale(1,1);
  transition: .5s ease-in-out;
}

.single-team-member .inner-box {
    display: table;
    width: 100%;
    height: 100%;
    text-align: center;
}
.single-team-member .social {
    display: table-cell;
    vertical-align: middle;
    position: relative;
    transition: .2s cubic-bezier(0.4, 0, 1, 1);
}

.single-team-member .social li {
  padding: 0;
  margin: 0 3px;
}
.single-team-member .social li a {
  color: #ffffff;
  transition: color .4s ease;
  font-size: 14px;
}


.single-team-member .img-box .overlay ul li a {
  height: 42px;
  width: 42px;
  color: #fff;
  border-radius: 50%;
  background: transparent;
  text-align: center;
  line-height: 44px;
  border: 1px solid #fff;
  transition: .5s ease;
}
.single-team-member .img-box .overlay ul li a:hover {
  background-color: #fff;
  border-color: #fff;
  color: #f2704E;
  transition: .5s ease;
}



.single-team-member .img-box .overlay ul li:last-child a {
}



.single-team-member h4 {
  color: #222;
  margin-top: 12px;
  margin-bottom: 6px;
}
.single-team-member .author-info {
  position: relative;
  overflow: hidden;
  border: 1px solid #f4f4f4;
  padding: 15px 20px;
}

.single-team-member p {
  color: #f2704E;
  transition: .5s ease;
  margin-bottom: 8px;
}

.single-team-member .text {
  margin-top: 15px;

}

.single-team-member ul {border-top: 1px solid #f4f4f4;padding-top: 10px;}
.single-team-member ul li {color: #848484;margin: 4px 0;}
.single-team-member ul li a {color: #848484;}
.single-team-member .author-info ul li i {
  font-size: 11px;
  background: #f2704E;
  margin-right: 10px;
  color: #fff;
  border-radius: 1px;
  height: 15px;width: 15px;text-align: center;
  line-height: 15px;
}









/***
==============================
   8. Team Member styles
============================== 
***/


.testimonials {
  position: relative;
  padding-bottom: 30px;
}



.single-testimonial h3,
.single-testimonial p {
  margin: 0;
}
.single-testimonial h4 {
  font-size: 18px;
  color: #222;
  text-transform: capitalize;
  margin-top: 12px;
  margin-bottom: 6px;
}


.single-testimonial .img-box {
  position: relative;
}
.single-testimonial .img-box img {
  transition: all 0.5s ease;
  transform: scale(1);
}


.testimonials .single-testimonial {
  margin-bottom: 40px;
  border: 1px solid #f4f4f4;
  padding: 30px 10px 26px;
  transition: .5s ease-in-out;
}

.testimonials .single-testimonial:hover {
  box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0), 0 0px 82px 0 rgba(0, 0, 0, 0.05);
  transition: .5s ease-in-out;
}



.single-testimonial .author-info {
  overflow: hidden;
  border: 2px solid #f4f4f4;
  padding: 6px 0 9px;
  margin-top: 5px;
}

.single-testimonial p a {
  color: #f2704E;
  transition: .5s ease;
}

.single-testimonial .text {
  margin-top: 15px;
  border-bottom: 1px solid #f4f4f4;
  padding-bottom: 22px;
  margin-bottom: 20px;
}
.single-testimonial .text p {
  font-size: 16px;

}








/************************** 
* Project styles 
***************************/
.single-project {
  padding-bottom: 30px;
  text-align: center;
}
.single-project .img-box {
  position: relative;
  overflow: hidden;
  padding: 5px;
  border: 1px solid #eaeaea;
}
.single-project .img-box img {
  width: 100%;
}
.single-project .title h5 {
  color: #222;
  padding: 25px 0 0;
  font-size: 16px;
  font-weight: 400;
  transition: .5s ease;
}
.single-project:hover .title h5 {
  color: #f2704E;
  transition: .5s ease;
}

.single-project .img-box .overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  bottom: 5px;
  left: 5px;
  background: rgba(72, 199, 236, 0.9);
  padding: 5px;
  transform: translate3d(0, 100px, 0);
  opacity: 0;
  visibility: hidden;
  transition: transform .4s ease, opacity .4s ease, visibility .4s ease;
}
.single-project .img-box .overlay .box {
  display: table;
  width: 100%;
  height: 100%;
}
.single-project .img-box .overlay .box .content {
  display: table-cell;
  vertical-align: bottom;
}
.single-project .img-box .overlay .top {
  text-align: center;
}
.single-project .img-box .overlay .top ul {
  margin-bottom: 0;
  margin-bottom: 20%;
}
.single-project .img-box .overlay .top li a {
  width: 45px;
  height: 45px;
  color: #fff;
  font-size: 16px;
  line-height: 48px;
  text-align: center;
  background: #222;
  display: block;
  border-radius: 50%;
  transition: .4s ease;
}
.single-project .img-box .overlay .top li a:hover {
  background: #1f1f1f;
  color: #f2704E;
}
.single-project .img-box .overlay .bottom {
  padding: 10px 0px;
  padding-right: 0;
  text-align: center;
}
.single-project .img-box .overlay .bottom .title h3 {
  margin: 0;
  color: #fff;
  line-height: 30px;
  font-size: 15px;
}
.single-project:hover .img-box .overlay {
  transform: translate3d(0, 0, 0);
  opacity: 1;
  visibility: visible;
}
.single-projects .graph-title {
  background: #f2704E;
  color: #fff;
  font-size: 16px;
  padding: 12px 20px;
}
.single-projects .analysis-list li {font-size: 16px;margin: 10px 0px;color: #848484;}
.single-projects .analysis-list li span {color: #f2704E;font-size: 14px;margin-right: 10px;}

.post-filter {
  display: inline-block;
  margin: 0;
  margin-bottom: 30px;
}
.post-filter li {
  padding: 0;
}

.post-filter li span {
  color: #848484;
  font-size: 14px;
  line-height: 24px;
  display: block;
  padding-right: 16px;
  cursor: pointer;
  transition: .5s ease;
}
.post-filter li:hover span,
.post-filter li.active span {
  color: #f2704E;
  transition: .5s ease;
}


/*inform-list*/
.inform-list {
  background: #f7f7f7;
  padding: 23px 20px;
}
.inform-list li  {
  color: #848484;
  font-size: 16px;
  line-height: 43px;
}
.inform-list li span {
  color: #f2704E;
  font-weight: 700;
}
.client-information .title {
  color: #f2704E;
  margin: 10px 0;
}
.client-information .text {margin-bottom: 35px;}
.client-information .thm-btn-tr {
  color: #222;
  border-color: #f4f4f4;
}

.client-information .thm-btn-tr:hover {
  color: #fff;
  border-color: #f2704E;
}





/*contact-service*/

.service-contact {
  background: #f2704E;
  padding: 30px;
  margin-top: 50px;
}
.service-contact h4 {
  color: #fff;
}
.service-contact p {
  color: #fff;
  font-size: 18px;
  margin: 10px 0
}
.service-contact .thm-btn {
  background: #fff;
  line-height: 38px;
  padding: 0 15px;
  color: #f2704E;
}
.service-contact .thm-btn:hover {
  background: #fff;
  color: #f2704E;
}

/*contact-info2*/

.contact-info2 {
  background: #f2704E;
  padding: 30px 20px;
  margin-top: 40px;
  margin-bottom: 50px;
}

.contact-info2 h4 {
  color: #fff;
  font-size: 20px;
  margin-bottom: 13px;
}

.contact-info2 p {
  color: #fff;
}
.contact-info2 .thm-btn {
  width: 100%;
  background: #fff;
  color: #f2704E;
  text-align: center;
  line-height: 41px;
}
.contact-info2 .thm-btn:hover {
  color: #fff;
  background: #222;
}

.contact-info2 ul {padding-top: 10px;margin-bottom: 20px;}
.contact-info2 ul li {color: #fff;margin: 4px 0;}
.contact-info2 ul li a {color: #fff;}
.contact-info2 ul li i {
  font-size: 11px;
  background: #fff;
  margin-right: 10px;
  color: #f2704E;
  border-radius: 1px;
  height: 15px;width: 15px;text-align: center;
  line-height: 15px;
}

    /*page_pagination style* 
    =============================================
***/


. {
  text-align: center;
  padding-top: 20px;
}
.page_pagination li {
  display: inline-block;
  margin:0 4px;
}
.page_pagination li a {
  width: 55px;
  line-height: 55px;
  border:1px solid rgba(229, 229, 229, 0.57);
  text-align: center;
  color: #252525;
  font-size: 18px;
  border-radius: 2px;
}
.page_pagination li a.active,
.page_pagination li a:hover {
  color:#fff;
  background: #f2704E;
}








/* ==============================
   5. Our Services styles
   ============================== */
.our-services {
  padding: 70px 0 15px;
}
.our-services.style-2 {
}

.our-services .separet {
  position: relative;
  display: block;
  width: 100%;
  border-bottom: 1px solid #f1f1f1;
  margin-bottom: 40px;
  top: -10px;
}

.our-services .caption-box-wrapper {
  border-top: 1px solid #F4F4F4;
  margin-top: 35px;
}
.our-services .single-our-service {
  margin-bottom: 56px;
}

.our-services .single-our-service h4 {
  margin-top: 17px;
  margin-bottom: 11px;
  color: #222;
  font-size: 20px;
  font-weight: 400;
  text-transform: capitalize;
  transition: .5s ease;
}
.our-services .single-our-service:hover h4 {
  color: #f2704E;
  transition: .5s ease;

}
.our-services .single-our-service .img-box {
  position: relative;
  max-width: 370px;
}
.our-services .single-our-service .text-box {
  border: 1px solid #f1f1f1;
  border-top: 0px;
  padding: 5px 20px 30px;
}

.our-services .single-our-service-caption-box {
  padding-top: 40px;
}
.our-services .single-our-service-caption-box .img-box,
.our-services .single-our-service-caption-box .content-box {
  display: table-cell;
  vertical-align: middle;
}
.our-services .single-our-service-caption-box .img-box {
  width: 120px;
}
.our-services .single-our-service-caption-box .content-box {
  padding-left: 20px;
}
.our-services.service-page .has-divider {
  border-top: 1px solid #F4F4F4;
  margin-top: 60px;
  padding-top: 60px;
}
.our-services .single-our-service .img-box {
  overflow: hidden;
}
.our-services .single-our-service .img-box img {
  transform: scale(1);
  transition: .5s ease-in-out;
}
.our-services .single-our-service:hover .img-box img {
  transform: scale(1.1);
  transition: .5s ease-in-out;
}

.our-services .single-our-service .img-box a {
  position: relative;
  display: block;
}

.our-services .single-our-service .img-box a:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background: rgba(72, 199, 236, 0.9);
    left: 0;
    top: 0;
    transform: scale(0,1);
    transition: .5s ease;
}

.our-services .single-our-service:hover .img-box a:after {
    transform: scale(1,1);
    transition: .5s ease;
}

.our-services .single-our-service .thm-btn {
  background: transparent;
  color: #f2704E;
  border-color: #848484;
}
.our-services .single-our-service .thm-btn:hover {
  background: #f2704E;
  color: #fff;
  border-color: #f2704E;
}




/*** 
=============================================
    two-column style
=============================================
***/



.two-column {
  position: relative;
}
.two-column .img-box {
  position: relative;
  padding-right: 24px;
  margin-bottom: 20px;
  float: left;
}
.two-column .content {
  position: relative;
  float: left;
}

.two-column .content h4 {
  font-size: 20px;
}
.two-column .content .date {
  color: #f2704E;
  margin-bottom: 18px;
  margin-top: 5px;
}


.two-column .owl-controls {
    text-align: center;
    display: block;
    position: relative;
    bottom: 0px;
    margin-top: 14px;
}


.two-column .owl-dots .owl-dot {
    display: inline-block;
}
.two-column .owl-dots .owl-dot span {
    background: none repeat scroll 0 0 #ff9a00;
    border-radius: 50%;
    display: block;
    height: 10px;
    background: #f4f4f4;
    width: 10px;
    margin: 5px 7px;
    transition: .5s ease;
}
.two-column .owl-dots .owl-dot.active span,
.two-column .owl-dots .owl-dot:hover span {
    background-color: #f2704E;
    transition: .5s ease;
}





/* ==============================
   5. Our Services styles
   ============================== */
.three-column.bg {
  background: #f7f7f7;
  padding-bottom: 14px;
}


.three-column .separet {
  position: relative;
  display: block;
  width: 100%;
  border-bottom: 1px solid #f1f1f1;
  margin-bottom: 40px;
  top: -10px;
}

.three-column .caption-box-wrapper {
  border-top: 1px solid #F4F4F4;
  margin-top: 35px;
}
.three-column .single-our-service {
  margin-bottom: 56px;
}

.three-column .single-our-service h4 {
  margin-top: 17px;
  margin-bottom: 11px;
  color: #222;
  font-size: 20px;
  font-weight: 400;
  text-transform: capitalize;
  transition: .5s ease;
}
.three-column .single-our-service:hover h4 {
  color: #f2704E;
  transition: .5s ease;

}
.three-column .single-our-service .img-box {
  position: relative;
  max-width: 370px;
}
.three-column .single-our-service .img-box .count {
  background: #f2704E;
  color: #fff;
  font-size: 18px;
  font-family: "Poppins", sans-serif;
  padding: 5px 24px;
  position: absolute;
  left: 0;
  bottom: -16px;
}
.three-column .single-our-service .text-box {
  border-top: 0px;
  padding: 20px 0px 0px;
}

.three-column .single-our-service-caption-box {
  padding-top: 40px;
}
.three-column .single-our-service-caption-box .img-box,
.three-column .single-our-service-caption-box .content-box {
  display: table-cell;
  vertical-align: middle;
}
.three-column .single-our-service-caption-box .img-box {
  width: 120px;
}
.three-column .single-our-service-caption-box .content-box {
  padding-left: 20px;
}
.three-column.service-page .has-divider {
  border-top: 1px solid #F4F4F4;
  margin-top: 60px;
  padding-top: 60px;
}
.three-column .single-our-service .img-box {
}
.three-column .single-our-service .img-box img {
  transition: .5s ease-in-out;
}
.three-column .single-our-service:hover .img-box img {
  transition: .5s ease-in-out;
}

.three-column .single-our-service .img-box a {
  position: relative;
  display: block;
}


.three-column .single-our-service .thm-btn {
  background: transparent;
  color: #f2704E;
  border-color: #848484;
}
.three-column .single-our-service .thm-btn:hover {
  background: #f2704E;
  color: #fff;
  border-color: #f2704E;
}


/*form*/

.default-form-area.style-3 {
  border: 1px solid #eee;
  padding: 29px;
}

.default-form-area.style-3 .thm-btn {
  width: 100%;
}

.default-form-area.style-3 textarea {
  height: 200px;
}






/*** 

====================================================================
  Featured Services style
====================================================================

***/

.feature-service {
  position: relative;
}

.feature-service .column {
  position: relative;
  margin-bottom: 50px;
}

.feature-service .style-1 .text {
}
.feature-service .style-1 .thm-btn-tr {
  color: #f2704E;
  border-color: #f4f4f4;
}
.feature-service .style-1 .thm-btn-tr:hover {
  color: #fff;
}



.feature-service .style-1 .text {
  margin: 21px 0 19px;
}




.feature-service .style-2 .content {
  position: relative;
  border: 1px solid #f4f4f4;
  padding: 25px 20px 0px;
}

.feature-service .style-2 .content:after {
  position: absolute;
  content: "";
  height: 3px;
  border: 1px solid #f4f4f4;
  border-top: 0px;
  width: 80%;
  left: 10%;
  bottom: -4px;
}
.feature-service .style-2 .content:before {
  position: absolute;
  content: "";
  height: 3px;
  border: 1px solid #f4f4f4;
  border-top: 0px;
  width: 60%;
  left: 20%;
  bottom: -7px;
}

.feature-service .style-2 .list{
  margin-top: 10px;
}
.feature-service .style-2 .list li {

}
.feature-service .style-2 .list li a {
  font-size: 16px;
  color: #848484;
  border-bottom: 1px solid #f4f4f4;
  padding: 10px 0;
  display: block;
  
}

.feature-service .style-2 .list li:last-child a {
  border-bottom: 0px;
}


.feature-service .style-2 .list li a i {
  color: #f2704E;
  font-size: 15px;
  padding-right: 15px;
}







.feature-service .style-3 .graph {
  padding: 14px;
  border: 1px solid #f4f4f4;
}

.feature-service .style-3 .select-box {
  margin-top: 20px;
  margin-bottom: 5px;
}


.feature-service .select-box .btn:before {
  position: absolute;
  content: "\f009";
  font-family: FontAwesome;
  font-size: 16px;
  color: #f2704E;
  left: 12px;
  top: 7px;
}

.feature-service .default-form .g-select {
  width: 170px;
}

.feature-service .default-form .bootstrap-select.btn-group .dropdown-toggle .filter-option {
    padding: 6px 15px;
    border: 1px solid #f4f4f4;
    text-align: center;
}

.feature-service .default-form .dropdown-menu {
  top: 33px;
}

/* ==============================
   call-out styles
   ============================== */
.call-out {
  background: #f2704E;
  padding: 25px 0 25px;
  color: #ffffff !important;
}

.call-out h3{
  margin-bottom: 1.4em;
  text-indent: 15px;
}

.call-out label{
  font-size: 1.2em;
}

.call-out input::placeholder{
  color:#fff;
}

.call-out input[type="text"],
.call-out select
{
  color: #fff;
  border: 2px  #fff solid;
  background-color: #f2704E !important;
  line-height: 41px;
  font-size: 1em;
  height: 44px;
  padding: 0px 21px;
  font-weight: 600;
  text-transform: uppercase;
}
.call-out .thm-btn-tr {
  color: #fff;
  background: transparent;
  border-color: #fff;
  margin-top: 20px;
  line-height: 41px;
  padding: 0 21px;
}

.call-out .thm-btn-tr:hover ,.call-out .thm-btn-tr:focus {
  color: #f2704E;
  background: #fff;
  border-color: #fff;
}


/* ==============================
   parallax styles
   ============================== */
.parallax {
  position: relative;
  padding: 100px 0;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
}

.parallax:before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background: rgba(16, 37, 46, 0.92);
  z-index: 0;
}

.parallax h1 {
  position: relative;
  font-size: 40px;
  color: #fff;
  margin-bottom: 30px;
}
.parallax p {
  position: relative;
  font-size: 18px;
  color: #fff;
  margin-bottom: 28px;
}

/* ==============================
   default-section styles
   ============================== */


.default-section {
  position: relative;
}

.default-section h4 {
  color: #f2704E;
  line-height: 32px;
  margin: 14px 0px;
}

.default-section p {
  margin: 14px 0px;
}


.default-section .default-list {
  position: relative;
  margin: 15px 0;
}

.mb-2{
  margin-bottom: 20px;
}
/*contacnt page*/





/*tab*/
.tabs-section{
  position:relative;
  padding:130px 0px 100px;
}

.tabs-section .column{
  margin-bottom:30px; 
}

.tabs-section .image-box img{
  width:100%;
  display:block;
}

.tabs-style-one .tab-buttons{
  position:relative;
}

.tabs-style-one .tab-buttons .tab-btn{
  position:relative;
  display:inline-block;
  padding: 13px 30px;
  margin:0px 0px 0px;
  cursor:pointer;
  font-size: 18px;
  text-align:center;
  color: #848484;
  border-top: 2px solid transparent;
  background-color:#f9f9f9;
  text-transform:capitalize;
  transition:all 300ms ease;
}

.tabs-style-one .tab-buttons .tab-btn.active-btn{
  color: #f2704E;
  background: #ffffff;
  border-color: #f2704E;
}

.tabs-style-one .tabs-content{
  position:relative;
  padding: 34px;
  padding-left: 0;
}

.tabs-style-one .tab .text-content{
  position:relative;
}

.tabs-style-one .tab .text-content .text{
  font-size:16px;
  line-height:1.8em;
  margin-bottom:15px;
  color:#777777;
  font-weight:300;
  font-family: 'Roboto', sans-serif;
}

.tabs-style-one.tabs-box{
  padding-right:15px;
}

.tabs-box .tabs-content .tab{
    position: relative;
    display: none;
}

.tabs-box .tabs-content .active-tab {
    display: block;
}






.contact_details {
  
}
.contact_details .item {
  border: 1px solid #f4f4f4;
  padding: 40px;
  transition: .5s ease;
  margin-bottom: 50px;
}
.contact_details .item:hover {
  border-color: #f2704E;
  transition: .5s ease; 
}
.contact_details .item span {color: #f2704E;font-size: 55px;}

.contact_details .item h4 {
  position: relative;
  font-size: 20px;
  margin: 25px 0 30px;
}

.contact_details .item h4:after {
    content: "";
    position: absolute;
    top: 35px;
    left: 50%;
    margin-left: -30px;
    width: 60px;
    height: 1px;
    background: #eee;
}

/* ==============================
    Modal Styles
   ============================== */

.modal .thm-btn {
  padding: 0px 24px !important;
}

#service_err{
  margin-left:10px;
}