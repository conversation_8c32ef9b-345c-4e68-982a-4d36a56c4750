@charset "utf-8";
/* CSS Document */


/* ---------------------------------------------------------------------- */
/* "Polyglot" Language Switcher
/* ----------------------------------------------------------------------
Version: 1.4
Author: Ixtendo
Author URI: http://www.ixtendo.com
License: MIT License
License URI: http://www.opensource.org/licenses/mit-license.php
------------------------------------------------------------------------- */


/* ---------------------------------------------------------------------- */
/* TABLE OF CONTENTS
/* ----------------------------------------------------------------------
-Generic
-JS-created Code
------------------------------------------------------------------------- */


/* ---------------------------------------------------------------------- */
/* Generic
/* ---------------------------------------------------------------------- */
#polyglotLanguageSwitcher, #polyglotLanguageSwitcher * {
	margin: 0;
	padding: 0; 
	outline: none;
}

#polyglotLanguageSwitcher ul {
	list-style: none;	
}

#polyglotLanguageSwitcher {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 12px;
	color: #444;
	line-height: normal;
	position: relative; /* sets the initial position for the drop-down menu */	
	z-index: 100;
}

#polyglotLanguageSwitcher form {
	display: none;	
}

/* ---------------------------------------------------------------------- */
/* JS-created Code
/* ---------------------------------------------------------------------- */
#polyglotLanguageSwitcher a {
	text-decoration: none;
	display: block;
	padding: 7px 0 8px 54px; /* 6px 6px 6px 28px */
	color: #444;
	width: 140px; /* 84px; total width: 120px */
	background-repeat: no-repeat;
	background-position: 20px center;
}

#polyglotLanguageSwitcher a:hover {
}

#polyglotLanguageSwitcher a.current:link, #polyglotLanguageSwitcher a.current:visited, #polyglotLanguageSwitcher a.current:active {
	position: relative; /* sets the initial position for the trigger arrow */
	background-color: #FFF;
	border: 1px solid #E5E5E5;
	border-radius: 3px;
	height: 1.25em; /* 15px */
}

#polyglotLanguageSwitcher a.current:hover, 
#polyglotLanguageSwitcher ul.dropdown li:hover {
	/* background-color: #F7F7F7; */
}

#polyglotLanguageSwitcher a.active { /* This style is applied as long as the drop-down menu is visible. */
	border-bottom: none !important;
	border-radius: 3px 3px 0 0 !important;
}

#polyglotLanguageSwitcher span.trigger {
	display: block;
	position: absolute;
	top: 11px;
	right: 15px;
	height: 12px;
	width: 20px;
	overflow: hidden;
}



#polyglotLanguageSwitcher a.current:hover span.trigger, #polyglotLanguageSwitcher a.current:active span.trigger, #polyglotLanguageSwitcher a.active span.trigger {
	background-position: left bottom !important;
}

/* Drop-Down Menu */

#polyglotLanguageSwitcher ul.dropdown {
	display: none;
	position: absolute;
	top: 2.333em; /* 28px */
	left: 0;
	background-color: #10252e;
	border-top: none !important;
	border-radius: 0 0 3px 3px;
}

#polyglotLanguageSwitcher ul.dropdown li {
	border-top: 1px dashed #D4D4D4;
}

#polyglotLanguageSwitcher ul.dropdown li:last-child { 
	border-radius: 0 0 3px 3px;
}

/* Flags */

#en { 
	background-image: url(../images/icons/gb.png);
}

#fr {
	background-image: url(../images/icons/fr.png);
}

#de { 
	background-image: url(../images/icons/de.png);	
}

#it { 
	background-image: url(../images/icons/it.png);	
}

#es { 
	background-image: url(../images/icons/es.png);	
}