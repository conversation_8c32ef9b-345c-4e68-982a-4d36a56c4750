<div class="call-out" id="qgen"> <div class="container"> <div class="row"> <div class="col-md-12 col-sm-12"> <h3>Use the price calculator to determine how much your document will cost.</h3> <form id="" name="" class="" method="post" onsubmit="event.preventDefault()" > <div class="col-md-6 col-sm-6 wow slideInLeft"> <label for="words_count">Enter Word Count</label> <input id="words_count" type="text" name="words_count" class="form-control" placeholder="eg : 1000"> </div><div class="col-md-6 col-sm-6 wow slideInRight"> <label>Select Service</label> <select id="service_type" class="form-control"> <option value="">Select Service</option> <option value="ce">Copy Editing</option> <option value="se">Standard Editing</option> <option value="ae">Advanced Editing</option> </select> </div><div class="col-sm-12 wow slideInLeft"> <button id="qgenerate" class="thm-btn-tr">Request Quote</a></button><b id="service_err"></b> </div></form> </div></div></div></div></div><footer class="main-footer"> <div class="widgets-section"> <div class="container"> <div class="row wow bounceIn"> <div class="big-column col-md-6 col-sm-12 col-xs-12"> <div class="row clearfix"> <div class="footer-column col-md-6 col-sm-6 col-xs-12"> <div class="footer-widget about-widget"> <h3 class="footer-title">About Us</h3> <div class="widget-content"> <div class="text"><p>Expert consulting with over 15 years of experience that ensures you always get the best guidance and services. We serve clients at every level of their academic journey, in whatever capacity we can be most useful by customizing our services to suit their needs and meet their expectations.</p></div><div class="link"> <a href="<?if($page=='about'){echo '#';}else{echo 'about.php';}?>" class="default_link">More About us <i class="fa fa-angle-right"></i></a> </div></div></div></div><div class="footer-column col-md-6 col-sm-6 col-xs-12"> <div class="footer-widget links-widget"> <h3 class="footer-title">Our Services</h3> <div class="widget-content"> <ul class="list"> <li><a href="service.php">Copy Editing</a></li><li><a href="service.php">Standard Editing</a></li><li><a href="service.php">Advanced Editing</a></li></ul> </div></div></div></div></div><div class="big-column col-md-6 col-sm-12 col-xs-12"> <div class="row clearfix"> <div class="footer-column col-md-6 col-sm-6 col-xs-12"> <div class="footer-widget links-widget"> <h3 class="footer-title">Our Sample Edits</h3> <div class="widget-content"> <ul class=" sample-info "> <li><a target="_blank" href="docs/Sample_copyediting.jpg"><span class="fa fa-file-word-o"></span>Copy Editing</a></li><li><a target="_blank" href="docs/Sample_edit_Standard-page.jpg"><span class="fa fa-file-word-o"></span>Standard Editing</a></li><li><a target="_blank" href="docs/Sample_edit_Advanced.jpg"><span class="fa fa-file-word-o"></span>Advanced Editing</a></li></ul> </div></div></div><div class="footer-column col-md-6 col-sm-6 col-xs-12"> <div class="footer-widget contact-widget"> <h3 class="footer-title">Contact us</h3> <div class="widget-content"> <ul class="contact-info"> <li><span class="icon-signs"></span>G-2, Sai Anand Apartment, Pada No 3, Lokmanya nagar, Thane - 400606, IN</li><li><span class="icon-phone-call"></span> Phone: +91 8454850534</li><li><span class="icon-e-mail-envelope"></span><EMAIL></li></ul> </div><ul class="social"> <li><a href="#"><i class="fa fa-facebook"></i></a></li><li><a href="#"><i class="fa fa-twitter"></i></a></li><li><a href="#"><i class="fa fa-google-plus"></i></a></li><li><a href="#"><i class="fa fa-linkedin"></i></a></li></ul> </div></div></div></div></div></div></div><section class="footer-bottom"> <div class="container"> <div class="pull-left copy-text"> <p>Copyrights © 2018 All Rights Reserved. Powered by <a href="index.php"> Editink.</a></p></div><div class="pull-right get-text"><ul><li>Designed And Developed By | <a href="https://t.me/pratik_ui_developer">Pratik Jain</a></li></ul></div></div></section> </footer><button class="scroll-top tran3s color2_bg"> <span class="fa fa-angle-up"></span></button><div class="preloader"></div><div class="modal fade" role="dialog" id="qgenmodal" style="margin-top: 90px;"> <div class="modal-dialog modal-md" role="document"> <div class="modal-content"> <div class="modal-header"> <button type="button" class="close mdl-close " data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button> <h4 class="modal-title">Final Quote</h4> </div><div class="modal-body"> <div class="form-content"> <h4 class="h2 ">Please fill in the details to get the Quote </h4> <form id="enq-form" name="enq_form" class="default-form" action="inc/sendenq.php" method="post"> <div class="row clearfix"> <div class="col-md-6 col-sm-6 col-xs-12"> <div class="form-group"> <input id="nam" type="text" name="enq_name" class="form-control" minlength="3" placeholder="Your Name *" tab-index="1" required> </div></div><div class="col-md-6 col-sm-6 col-xs-12"> <div class="form-group"> <input id="mail" type="text" name="enq_email" class="form-control required email" placeholder="Your Mail *" tab-index="2" required> </div></div><div class="col-md-12 col-sm-12 col-xs-12"> <div class="form-group"> <input id="enq_botcheck" name="enq_botcheck" class="form-control" type="hidden" value=""> <input id="wrd_c" name="wrd_c" class="form-control" type="hidden"> <input id="ser_type" name="ser_type" class="form-control" type="hidden"> <button class=" btn-block thm-btn2" tab-index="3" type="submit" id="genq_btn" data-loading-text="Please wait...">Generate Quote</button> </div></div></div></form> </div><div class="quote-content"> </div></div><div class="modal-footer"> <a role="button" class="thm-btn pull-left mdl-close" data-dismiss="modal" tab-index="5">Close</a> <a href="<?if($page=='contact'){echo '#';}else{echo 'contact.php';}?>" role="button" class="pull-right thm-btn" tab-index="4">Contact us</a> </div></div></div></div><script src="js/libs.js"></script><script src="js/validation.js"></script><script>$('.mdl-close').click(function(){$(".quote-content").html(""); $(".form-content").show(200);}); if($("#enq-form").length){$("#enq-form").validate({submitHandler: function(form){var form_btn=$(form).find($('#genq_btn')); var form_result_div='#form-result'; $(form_result_div).remove(); form_btn.before('<div id="form-result" class="alert alert-success" role="alert" style="display: none;"></div>'); var form_btn_old_msg=form_btn.html(); form_btn.html(form_btn.prop('disabled', true).data("loading-text")); var name=$("[name='enq_name']").val(); var mail=$("[name='enq_email']").val(); var wc=$("[name='wrd_c']").val($('#words_count').val()); var st=$("[name='ser_type']").val($('#service_type').val());$(form).ajaxSubmit({dataType: 'json', success: function(data){if( data.status=='true' ){$(form).find('.form-control').val('');}form_btn.prop('disabled', false).html(form_btn_old_msg); var e=document.querySelector("#service_type").value, i=document.querySelector("#words_count").value; a=[{min: "1", max: "1000", ce: "12", se: "21", ae: "35"},{min: "1001", max: "1500", ce: "15", se: "28", ae: "46"},{min: "1501", max: "2000", ce: "21", se: "37", ae: "62"},{min: "2001", max: "2500", ce: "23", se: "42", ae: "69"},{min: "2501", max: "3000", ce: "28", se: "50", ae: "83"},{min: "3001", max: "3500", ce: "32", se: "58", ae: "97"},{min: "3501", max: "4000", ce: "37", se: "66", ae: "117"},{min: "4001", max: "4500", ce: "39", se: "71", ae: "118"},{min: "4501", max: "5000", ce: "43", se: "78", ae: "129"}]; if(i > 5000){$(".form-content").hide(200,function(){$("[name='enq_name']").val(name); $("[name='enq_email']").val(mail); $(".quote-content").html("WordsCount is greater than 5000, for best price contact us");return;});}n=a.find(t=> i >=parseInt(t.min) && i <=parseInt(t.max))[e]; $(".form-content").hide(200,function(){$("[name='enq_name']").val(name); $("[name='enq_email']").val(mail); $(".quote-content").html(data.message).fadeIn('slow'); document.querySelector(".modal-body").querySelector("h6").innerHTML="$ " + `${n}`;});}});}});}</script>