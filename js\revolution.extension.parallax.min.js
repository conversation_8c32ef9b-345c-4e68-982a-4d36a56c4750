/********************************************
 * REVOLUTION 5.1.4 EXTENSION - PARALLAX
 * @version: 1.1.1 (25.11.2015)
 * @requires jquery.themepunch.revolution.js
 * <AUTHOR>
*********************************************/
!function(e){var r=jQuery.fn.revolution,a=r.is_mobile();jQuery.extend(!0,r,{checkForParallax:function(e,t){var o=t.parallax;return a&&"on"==o.disable_onmobile?!1:(("3D"==o.type||"3d"==o.type)&&(punchgs.TweenLite.set(t.c,{overflow:o.ddd_overflow}),punchgs.TweenLite.set(t.ul,{overflow:o.ddd_overflow}),"carousel"!=t.sliderType&&"on"==o.ddd_shadow&&(t.c.prepend('<div class="dddwrappershadow"></div>'),punchgs.TweenLite.set(t.c.find(".dddwrappershadow"),{force3D:"auto",transformPerspective:1600,transformOrigin:"50% 50%",width:"100%",height:"100%",position:"absolute",top:0,left:0,zIndex:0}))),t.li.each(function(){var e=jQuery(this);if("3D"==o.type||"3d"==o.type){e.find(".slotholder").wrapAll('<div class="dddwrapper" style="width:100%;height:100%;position:absolute;top:0px;left:0px;overflow:hidden"></div>'),e.find(".tp-parallax-wrap").wrapAll('<div class="dddwrapper-layer" style="width:100%;height:100%;position:absolute;top:0px;left:0px;z-index:5;overflow:'+o.ddd_layer_overflow+';"></div>'),e.find(".rs-parallaxlevel-tobggroup").closest(".tp-parallax-wrap").wrapAll('<div class="dddwrapper-layertobggroup" style="position:absolute;top:0px;left:0px;z-index:50;width:100%;height:100%"></div>');var r=e.find(".dddwrapper"),a=e.find(".dddwrapper-layer"),l=e.find(".dddwrapper-layertobggroup");l.appendTo(r),"carousel"==t.sliderType&&("on"==o.ddd_shadow&&r.addClass("dddwrappershadow"),punchgs.TweenLite.set(r,{borderRadius:t.carousel.border_radius})),punchgs.TweenLite.set(e,{overflow:"visible",transformStyle:"preserve-3d",perspective:1600}),punchgs.TweenLite.set(r,{force3D:"auto",transformOrigin:"50% 50%"}),punchgs.TweenLite.set(a,{force3D:"auto",transformOrigin:"50% 50%",zIndex:5}),punchgs.TweenLite.set(t.ul,{transformStyle:"preserve-3d",transformPerspective:1600})}for(var s=1;s<=o.levels.length;s++)e.find(".rs-parallaxlevel-"+s).each(function(){var e=jQuery(this),r=e.closest(".tp-parallax-wrap");r.data("parallaxlevel",o.levels[s-1]),r.addClass("tp-parallax-container")})}),("mouse"==o.type||"scroll+mouse"==o.type||"mouse+scroll"==o.type||"3D"==o.type||"3d"==o.type)&&(e.mouseenter(function(r){var a=e.find(".active-revslide"),t=e.offset().top,o=e.offset().left,l=r.pageX-o,s=r.pageY-t;a.data("enterx",l),a.data("entery",s)}),e.on("mousemove.hoverdir, mouseleave.hoverdir, trigger3dpath",function(r,a){var l=a&&a.li?a.li:e.find(".active-revslide");if("enterpoint"==o.origo){var s=e.offset().top,i=e.offset().left;void 0==l.data("enterx")&&l.data("enterx",r.pageX-i),void 0==l.data("entery")&&l.data("entery",r.pageY-s);var d=l.data("enterx")||r.pageX-i,n=l.data("entery")||r.pageY-s,p=d-(r.pageX-i),c=n-(r.pageY-s),u=o.speed/1e3||.4}else var s=e.offset().top,i=e.offset().left,p=t.conw/2-(r.pageX-i),c=t.conh/2-(r.pageY-s),u=o.speed/1e3||3;if("mouseleave"==r.type&&(p=o.ddd_lasth||0,c=o.ddd_lastv||0,u=1.5),l.find(".tp-parallax-container").each(function(){var e=jQuery(this),r=parseInt(e.data("parallaxlevel"),0),a="3D"==o.type||"3d"==o.type?r/200:r/100,t=p*a,l=c*a;"scroll+mouse"==o.type||"mouse+scroll"==o.type?punchgs.TweenLite.to(e,u,{force3D:"auto",x:t,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,u,{force3D:"auto",x:t,y:l,ease:punchgs.Power3.easeOut,overwrite:"all"})}),"3D"==o.type||"3d"==o.type){var h=".tp-revslider-slidesli .dddwrapper, .dddwrappershadow, .tp-revslider-slidesli .dddwrapper-layer";"carousel"===t.sliderType&&(h=".tp-revslider-slidesli .dddwrapper, .tp-revslider-slidesli .dddwrapper-layer"),t.c.find(h).each(function(){var e=jQuery(this),a=o.levels[o.levels.length-1]/200,l=p*a,s=c*a,i=0==t.conw?0:Math.round(p/t.conw*a*100)||0,d=0==t.conh?0:Math.round(c/t.conh*a*100)||0,n=e.closest("li"),h=0,w=!1;e.hasClass("dddwrapper-layer")&&(h=o.ddd_z_correction||65,w=!0),e.hasClass("dddwrapper-layer")&&(l=0,s=0),n.hasClass("active-revslide")||"carousel"!=t.sliderType?"on"!=o.ddd_bgfreeze||w?punchgs.TweenLite.to(e,u,{rotationX:d,rotationY:-i,x:l,z:h,y:s,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,z:0,x:0,y:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}),"mouseleave"==r.type&&punchgs.TweenLite.to(jQuery(this),3.8,{z:0,ease:punchgs.Power3.easeOut})})}}),a&&(window.ondeviceorientation=function(r){var a=Math.round(r.beta||0)-70,l=Math.round(r.gamma||0),s=e.find(".active-revslide");if(jQuery(window).width()>jQuery(window).height()){var i=l;l=a,a=i}var d=e.width(),n=e.height(),p=360/d*l,c=180/n*a,u=o.speed/1e3||3;if(s.find(".tp-parallax-container").each(function(){var e=jQuery(this),r=parseInt(e.data("parallaxlevel"),0),a=r/100,t=p*a*2,o=c*a*4;punchgs.TweenLite.to(e,u,{force3D:"auto",x:t,y:o,ease:punchgs.Power3.easeOut,overwrite:"all"})}),"3D"==o.type||"3d"==o.type){var h=".tp-revslider-slidesli .dddwrapper, .dddwrappershadow, .tp-revslider-slidesli .dddwrapper-layer";"carousel"===t.sliderType&&(h=".tp-revslider-slidesli .dddwrapper, .tp-revslider-slidesli .dddwrapper-layer"),t.c.find(h).each(function(){var e=jQuery(this),a=o.levels[o.levels.length-1]/200;offsh=p*a,offsv=c*a*3,offrv=0==t.conw?0:Math.round(p/t.conw*a*500)||0,offrh=0==t.conh?0:Math.round(c/t.conh*a*700)||0,li=e.closest("li"),zz=0,itslayer=!1,e.hasClass("dddwrapper-layer")&&(zz=o.ddd_z_correction||65,itslayer=!0),e.hasClass("dddwrapper-layer")&&(offsh=0,offsv=0),li.hasClass("active-revslide")||"carousel"!=t.sliderType?"on"!=o.ddd_bgfreeze||itslayer?punchgs.TweenLite.to(e,u,{rotationX:offrh,rotationY:-offrv,x:offsh,z:zz,y:offsv,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,z:0,x:0,y:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}),"mouseleave"==r.type&&punchgs.TweenLite.to(jQuery(this),3.8,{z:0,ease:punchgs.Power3.easeOut})})}})),void r.scrollTicker(t,e))},scrollTicker:function(e,a){1!=e.scrollTicker&&(e.scrollTicker=!0,punchgs.TweenLite.ticker.fps(150),punchgs.TweenLite.ticker.addEventListener("tick",function(){r.scrollHandling(e)},a,!1,1))},scrollHandling:function(e){function t(e,r){e.lastscrolltop=r}e.lastwindowheight=e.lastwindowheight||jQuery(window).height();var o=e.c.offset().top,l=jQuery(window).scrollTop(),s=new Object,i=e.viewPort,d=e.parallax;if(e.lastscrolltop==l&&!e.duringslidechange)return!1;punchgs.TweenLite.delayedCall(.2,t,[e,l]),s.top=o-l,s.h=0==e.conh?e.c.height():e.conh,s.bottom=o-l+s.h;var n=s.top<0?s.top/s.h:s.bottom>e.lastwindowheight?(s.bottom-e.lastwindowheight)/s.h:0;e.scrollproc=n,r.callBackHandling&&r.callBackHandling(e,"parallax","start");var p=1-Math.abs(n);if(p=0>p?0:p,i.enable&&(1-i.visible_area<=p?e.inviewport||(e.inviewport=!0,r.enterInViewPort(e)):e.inviewport&&(e.inviewport=!1,r.leaveViewPort(e))),a&&"on"==e.parallax.disable_onmobile)return!1;var c=new punchgs.TimelineLite;c.pause(),"3d"!=d.type&&"3D"!=d.type&&(("scroll"==d.type||"scroll+mouse"==d.type||"mouse+scroll"==d.type)&&e.c.find(".tp-parallax-container").each(function(r){var a=jQuery(this),t=parseInt(a.data("parallaxlevel"),0)/100,o=n*-(t*e.conh);a.data("parallaxoffset",o),c.add(punchgs.TweenLite.set(a,{force3D:"auto",y:o}),0)}),e.c.find(".tp-revslider-slidesli .slotholder, .tp-revslider-slidesli .rs-background-video-layer").each(function(){var r=jQuery(this),a=r.data("bgparallax")||e.parallax.bgparallax;if(a="on"==a?1:a,void 0!==a||"off"!==a){var t=e.parallax.levels[parseInt(a,0)-1]/100,o=n*-(t*e.conh);jQuery.isNumeric(o)&&c.add(punchgs.TweenLite.set(r,{position:"absolute",top:"0px",left:"0px",backfaceVisibility:"hidden",force3D:"true",y:o+"px",overwrite:"auto"}),0)}})),r.callBackHandling&&r.callBackHandling(e,"parallax","end"),c.play(0)}})}(jQuery);