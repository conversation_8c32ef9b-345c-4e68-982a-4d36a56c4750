<?php

require_once('phpmailer/class.phpmailer.php');
require_once('phpmailer/class.smtp.php');

$mail = new PHPMailer();

//$mail->SMTPDebug = 3;                               // Enable verbose debug output
$mail->isSMTP();  
//$mail->Host = 'mail.editink.in';  // Specify main and backup SMTP servers
$mail->SMTPAuth = true;                                             // Enable SMTP authentication
$mail->Username = '<EMAIL>';                 // SMTP username
$mail->Password = '[*EditInk*]';             // SMTP password
$mail->addAddress('<EMAIL>');
$mail->addAddress('<EMAIL>');
$message = "";
$status = "false";
$mail->Host = 'localhost';
//$mail->SMTPAuth = false;
$mail->SMTPAutoTLS = false; 
$mail->Port = 25; 

if( $_SERVER['REQUEST_METHOD'] == 'POST' ) {
    if( $_POST['enq_name'] != '' AND $_POST['enq_email'] != '') {

        $name = $_POST['enq_name'];
        $email = $_POST['enq_email'];
        $wc =  $_POST['wrd_c'];
        $st =  $_POST['ser_type'];
          
        $subject = 'New quotation request';

        $botcheck = $_POST['enq_botcheck'];

        $toemail = '<EMAIL>'; // Your Email Address
        $toname = 'Editink'; // Your Name

        if( $botcheck == '' ) {

            $mail->SetFrom( $toemail , $name );
            $mail->AddReplyTo( $email , $name );
            $mail->AddAddress( $toemail , $toname );
            $mail->Subject = $subject;

            $name = isset($name) ? "Name: $name<br><br>" : '';
            $email = isset($email) ? "Email: $email<br><br>" : '';
            $wc = "Total Words Entered : $wc<br><br>";
            $st = "Service Selected : $st<br><br>";
        

            $referrer = $_SERVER['HTTP_REFERER'] ? '<br><br><br>This Form was submitted from: ' . $_SERVER['HTTP_REFERER'] : '';
            
            $body = "$name $email $wc $st $referrer";

            $mail->MsgHTML( $body );
            $sendEmail = $mail->Send();

            if( $sendEmail == true ):
                $message = '<h4 class="text-center" id="apprx_price">Approximate Price Would be </h4>
            <h6 class="text-center h1" id="actual_quote">$ 0 </h6>';
                $status = "true";
            else:
                $message = 'Email <strong>could not</strong> be sent due to some Unexpected Error. Please Try Again later.<br /><br /><strong>Reason:</strong><br />' . $mail->ErrorInfo . '';
                $status = "false";
            endif;
        } else {
            $message = 'Bot <strong>Detected</strong>.! Clean yourself Botster.!';
            $status = "false";
        }
    } else {
        $message = 'Please <strong>Fill up</strong> all the Fields and Try Again.';
        $status = "false";
    }
} else {
    $message = 'An <strong>unexpected error</strong> occured. Please Try Again later.';
    $status = "false";
}

$status_array = array( 'message' => $message, 'status' => $status);
echo json_encode($status_array);
?>