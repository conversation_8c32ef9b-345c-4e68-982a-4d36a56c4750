/********************************************
 * REVOLUTION 5.0 EXTENSION - LAYER ANIMATION
 * @version: 1.3 (26.11.2015)
 * @requires jquery.themepunch.revolution.js
 * <AUTHOR>
*********************************************/

!function(a){function e(a,e,t,i,n,o,r){var d=a.find(e);d.css("borderWidth",o+"px"),d.css(t,0-o+"px"),d.css(i,"0px solid transparent"),d.css(n,r)}var t=jQuery.fn.revolution;t.is_mobile();jQuery.extend(!0,t,{animcompleted:function(a,e){var i=a.data("videotype"),n=a.data("autoplay"),o=a.data("autoplayonlyfirsttime");void 0!=i&&"none"!=i&&(1==n||"true"==n||"on"==n||"1sttime"==n||o?(t.playVideo(a,e),t.toggleState(a.data("videotoggledby")),(o||"1sttime"==n)&&(a.data("autoplayonlyfirsttime",!1),a.data("autoplay","off"))):("no1sttime"==n&&a.data("autoplay","on"),t.unToggleState(a.data("videotoggledby"))))},handleStaticLayers:function(a,e){var t=parseInt(a.data("startslide"),0),i=parseInt(a.data("endslide"),0);0>t&&(t=0),0>i&&(i=e.slideamount),0===t&&i===e.slideamount-1&&(i=e.slideamount+1),a.data("startslide",t),a.data("endslide",i)},animateTheCaptions:function(a,e,i,n){var o="carousel"===e.sliderType?0:e.width/2-e.gridwidth[e.curWinRange]*e.bw/2,r=0,d=a.data("index");e.layers=e.layers||new Object,e.layers[d]=e.layers[d]||a.find(".tp-caption"),e.layers["static"]=e.layers["static"]||e.c.find(".tp-static-layers").find(".tp-caption");var s=new Array;if(e.conh=e.c.height(),e.conw=e.c.width(),e.ulw=e.ul.width(),e.ulh=e.ul.height(),e.debugMode){a.addClass("indebugmode"),a.find(".helpgrid").remove(),e.c.find(".hglayerinfo").remove(),a.append('<div class="helpgrid" style="width:'+e.gridwidth[e.curWinRange]*e.bw+"px;height:"+e.gridheight[e.curWinRange]*e.bw+'px;"></div>');var l=a.find(".helpgrid");l.append('<div class="hginfo">Zoom:'+Math.round(100*e.bw)+"% &nbsp;&nbsp;&nbsp; Device Level:"+e.curWinRange+"&nbsp;&nbsp;&nbsp; Grid Preset:"+e.gridwidth[e.curWinRange]+"x"+e.gridheight[e.curWinRange]+"</div>"),e.c.append('<div class="hglayerinfo"></div>'),l.append('<div class="tlhg"></div>')}s&&jQuery.each(s,function(a){var e=jQuery(this);punchgs.TweenLite.set(e.find(".tp-videoposter"),{autoAlpha:1}),punchgs.TweenLite.set(e.find("iframe"),{autoAlpha:0})}),e.layers[d]&&jQuery.each(e.layers[d],function(a,e){s.push(e)}),e.layers["static"]&&jQuery.each(e.layers["static"],function(a,e){s.push(e)}),s&&jQuery.each(s,function(a){t.animateSingleCaption(jQuery(this),e,o,r,a,i)});var p=jQuery("body").find("#"+e.c.attr("id")).find(".tp-bannertimer");p.data("opt",e),void 0!=n&&setTimeout(function(){n.resume()},30)},animateSingleCaption:function(a,n,r,u,w,y,b){var x=y,T=h(a,n,"in",!0),L=a.data("_pw")||a.closest(".tp-parallax-wrap"),W=a.data("_lw")||a.closest(".tp-loop-wrap"),C=a.data("_mw")||a.closest(".tp-mask-wrap"),j=a.data("responsive")||"on",I=a.data("responsive_offset")||"on",R=a.data("basealign")||"grid",k="grid"===R?n.width:n.ulw,_="grid"===R?n.height:n.ulh,S=jQuery("body").hasClass("rtl");if(a.data("_pw")||(a.data("_pw",L),a.data("_lw",W),a.data("_mw",C)),"fullscreen"==n.sliderLayout&&(u=_/2-n.gridheight[n.curWinRange]*n.bh/2),("on"==n.autoHeight||void 0!=n.minHeight&&n.minHeight>0)&&(u=n.conh/2-n.gridheight[n.curWinRange]*n.bh/2),0>u&&(u=0),n.debugMode){a.closest("li").find(".helpgrid").css({top:u+"px",left:r+"px"});var z=n.c.find(".hglayerinfo");a.on("hover, mouseenter",function(){var e="";a.data()&&jQuery.each(a.data(),function(a,t){"object"!=typeof t&&(e=e+'<span style="white-space:nowrap"><span style="color:#27ae60">'+a+":</span>"+t+"</span>&nbsp; &nbsp; ")}),z.html(e)})}var Q=p(a.data("visibility"),n)[n.forcedWinRange]||p(a.data("visibility"),n)||"on";if("off"==Q||k<n.hideCaptionAtLimit&&"on"==a.data("captionhidden")||k<n.hideAllCaptionAtLimit?a.addClass("tp-hidden-caption"):a.removeClass("tp-hidden-caption"),a.data("layertype","html"),0>r&&(r=0),void 0!=a.data("thumbimage")&&void 0==a.data("videoposter")&&a.data("videoposter",a.data("thumbimage")),a.find("img").length>0){var M=a.find("img");a.data("layertype","image"),0==M.width()&&M.css({width:"auto"}),0==M.height()&&M.css({height:"auto"}),void 0==M.data("ww")&&M.width()>0&&M.data("ww",M.width()),void 0==M.data("hh")&&M.height()>0&&M.data("hh",M.height());var O=M.data("ww"),H=M.data("hh"),B="slide"==R?n.ulw:n.gridwidth[n.curWinRange],A="slide"==R?n.ulh:n.gridheight[n.curWinRange],O=p(M.data("ww"),n)[n.curWinRange]||p(M.data("ww"),n)||"auto",H=p(M.data("hh"),n)[n.curWinRange]||p(M.data("hh"),n)||"auto",F="full"===O||"full-proportional"===O,D="full"===H||"full-proportional"===H;if("full-proportional"===O){var P=M.data("owidth"),X=M.data("oheight");X/A>P/B?(O=B,H=X*(B/P)):(H=A,O=P*(A/X))}else O=F?B:parseFloat(O),H=D?A:parseFloat(H);void 0==O&&(O=0),void 0==H&&(H=0),"off"!==j?("grid"!=R&&F?M.width(O):M.width(O*n.bw),"grid"!=R&&D?M.height(H):M.height(H*n.bh)):(M.width(O),M.height(H))}if("slide"===R&&(r=0,u=0),a.hasClass("tp-videolayer")||a.find("iframe").length>0||a.find("video").length>0){if(a.data("layertype","video"),t.manageVideoLayer(a,n,y,x),!y&&!x){a.data("videotype");t.resetVideo(a,n)}var Y=a.data("aspectratio");void 0!=Y&&Y.split(":").length>1&&t.prepareCoveredVideo(Y,n,a);var M=a.find("iframe")?a.find("iframe"):M=a.find("video"),V=a.find("iframe")?!1:!0,N=a.hasClass("coverscreenvideo");M.css({display:"block"}),void 0==a.data("videowidth")&&(a.data("videowidth",M.width()),a.data("videoheight",M.height()));var Z,O=p(a.data("videowidth"),n)[n.curWinRange]||p(a.data("videowidth"),n)||"auto",H=p(a.data("videoheight"),n)[n.curWinRange]||p(a.data("videoheight"),n)||"auto";O=parseFloat(O),H=parseFloat(H),void 0===a.data("cssobj")&&(Z=g(a,0),a.data("cssobj",Z));var $=m(a.data("cssobj"),n);if("auto"==$.lineHeight&&($.lineHeight=$.fontSize+4),a.hasClass("fullscreenvideo")||N){r=0,u=0,a.data("x",0),a.data("y",0);var G=_;"on"==n.autoHeight&&(G=n.conh),a.css({width:k,height:G})}else punchgs.TweenLite.set(a,{paddingTop:Math.round($.paddingTop*n.bh)+"px",paddingBottom:Math.round($.paddingBottom*n.bh)+"px",paddingLeft:Math.round($.paddingLeft*n.bw)+"px",paddingRight:Math.round($.paddingRight*n.bw)+"px",marginTop:$.marginTop*n.bh+"px",marginBottom:$.marginBottom*n.bh+"px",marginLeft:$.marginLeft*n.bw+"px",marginRight:$.marginRight*n.bw+"px",borderTopWidth:Math.round($.borderTopWidth*n.bh)+"px",borderBottomWidth:Math.round($.borderBottomWidth*n.bh)+"px",borderLeftWidth:Math.round($.borderLeftWidth*n.bw)+"px",borderRightWidth:Math.round($.borderRightWidth*n.bw)+"px",width:O*n.bw+"px",height:H*n.bh+"px"});(0==V&&!N||1!=a.data("forcecover")&&!a.hasClass("fullscreenvideo")&&!N)&&(M.width(O*n.bw),M.height(H*n.bh))}a.find(".tp-resizeme, .tp-resizeme *").each(function(){v(jQuery(this),n,"rekursive",j)}),a.hasClass("tp-resizeme")&&a.find("*").each(function(){v(jQuery(this),n,"rekursive",j)}),v(a,n,0,j);var U=a.outerHeight(),q=a.css("backgroundColor");e(a,".frontcorner","left","borderRight","borderTopColor",U,q),e(a,".frontcornertop","left","borderRight","borderBottomColor",U,q),e(a,".backcorner","right","borderLeft","borderBottomColor",U,q),e(a,".backcornertop","right","borderLeft","borderTopColor",U,q),"on"==n.fullScreenAlignForce&&(r=0,u=0);var E=a.data("arrobj");if(void 0===E){var E=new Object;E.voa=p(a.data("voffset"),n)[n.curWinRange]||p(a.data("voffset"),n)[0],E.hoa=p(a.data("hoffset"),n)[n.curWinRange]||p(a.data("hoffset"),n)[0],E.elx=p(a.data("x"),n)[n.curWinRange]||p(a.data("x"),n)[0],E.ely=p(a.data("y"),n)[n.curWinRange]||p(a.data("y"),n)[0]}var J=0==E.voa.length?0:E.voa,K=0==E.hoa.length?0:E.hoa,aa=0==E.elx.length?0:E.elx,ea=0==E.ely.length?0:E.ely,ta=a.outerWidth(!0),ia=a.outerHeight(!0);0==ta&&0==ia&&(ta=n.ulw,ia=n.ulh);var na="off"!==I?parseInt(J,0)*n.bw:parseInt(J,0),oa="off"!==I?parseInt(K,0)*n.bw:parseInt(K,0),ra="grid"===R?n.gridwidth[n.curWinRange]*n.bw:k,da="grid"===R?n.gridheight[n.curWinRange]*n.bw:_;"on"==n.fullScreenAlignForce&&(ra=n.ulw,da=n.ulh),aa="center"===aa||"middle"===aa?ra/2-ta/2+oa:"left"===aa?oa:"right"===aa?ra-ta-oa:"off"!==I?aa*n.bw:aa,ea="center"==ea||"middle"==ea?da/2-ia/2+na:"top"==ea?na:"bottom"==ea?da-ia-na:"off"!==I?ea*n.bw:ea,S&&(aa+=ta);var sa=a.data("lasttriggerstate"),la=a.data("triggerstate"),pa=a.data("start")||100,ha=a.data("end"),ca=b?0:"bytrigger"===pa||"sliderenter"===pa?0:parseFloat(pa)/1e3,ga=aa+r,ma=ea+u,ua=a.css("z-Index");b||("reset"==sa&&"bytrigger"!=pa?(a.data("triggerstate","on"),a.data("animdirection","in"),la="on"):"reset"==sa&&"bytrigger"==pa&&(a.data("triggerstate","off"),a.data("animdirection","out"),la="off")),punchgs.TweenLite.set(L,{zIndex:ua,top:ma,left:ga,overwrite:"auto"}),0==T&&(x=!0),void 0==a.data("timeline")||x||(2!=T&&a.data("timeline").gotoAndPlay(0),x=!0),!y&&a.data("timeline_out")&&2!=T&&0!=T&&(a.data("timeline_out").kill(),a.data("outstarted",0)),b&&void 0!=a.data("timeline")&&(a.removeData("$anims"),a.data("timeline").pause(0),a.data("timeline").kill(),void 0!=a.data("newhoveranim")&&(a.data("newhoveranim").progress(0),a.data("newhoveranim").kill()),a.removeData("timeline"),punchgs.TweenLite.killTweensOf(a),a.unbind("hover"),a.removeClass("rs-hover-ready"),a.removeData("newhoveranim"));var va=a.data("timeline")?a.data("timeline").time():0,fa=void 0!==a.data("timeline")?a.data("timeline").progress():0,wa=a.data("timeline")||new punchgs.TimelineLite({smoothChildTiming:!0});if(fa=jQuery.isNumeric(fa)?fa:0,wa.pause(),1>fa&&1!=a.data("outstarted")||2==T||b){var ya=a;if(void 0!=a.data("mySplitText")&&a.data("mySplitText").revert(),void 0!=a.data("splitin")&&a.data("splitin").match(/chars|words|lines/g)||void 0!=a.data("splitout")&&a.data("splitout").match(/chars|words|lines/g)){var ba=a.find("a").length>0?a.find("a"):a;a.data("mySplitText",new punchgs.SplitText(ba,{type:"lines,words,chars",charsClass:"tp-splitted",wordsClass:"tp-splitted",linesClass:"tp-splitted"})),a.addClass("splitted")}void 0!==a.data("mySplitText")&&a.data("splitin")&&a.data("splitin").match(/chars|words|lines/g)&&(ya=a.data("mySplitText")[a.data("splitin")]);var xa=new Object,Ta=void 0!=a.data("transform_in")?a.data("transform_in").match(/\(R\)/gi):!1;if(!a.data("$anims")||b||Ta){var La=i(),Wa=i(),Ca=o(),ja=void 0!==a.data("transform_hover")||void 0!==a.data("style_hover");Wa=s(Wa,a.data("transform_idle")),La=s(Wa,a.data("transform_in"),1==n.sdir),ja&&(Ca=s(Ca,a.data("transform_hover")),Ca=c(Ca,a.data("style_hover")),a.data("hover",Ca)),La.elemdelay=void 0==a.data("elementdelay")?0:a.data("elementdelay"),Wa.anim.ease=La.anim.ease=La.anim.ease||punchgs.Power1.easeInOut,ja&&!a.hasClass("rs-hover-ready")&&(a.addClass("rs-hover-ready"),a.hover(function(a){var e=jQuery(a.currentTarget),t=e.data("hover"),i=e.data("timeline");i&&1==i.progress()&&(void 0===e.data("newhoveranim")||"none"===e.data("newhoveranim")?e.data("newhoveranim",punchgs.TweenLite.to(e,t.speed,t.anim)):(e.data("newhoveranim").progress(0),e.data("newhoveranim").play()))},function(a){var e=jQuery(a.currentTarget),t=e.data("timeline");t&&1==t.progress()&&void 0!=e.data("newhoveranim")&&e.data("newhoveranim").reverse()})),xa=new Object,xa.f=La,xa.r=Wa,a.data("$anims")}else xa=a.data("$anims");var Ia=l(a.data("mask_in")),Ra=new punchgs.TimelineLite;if(xa.f.anim.x=xa.f.anim.x*n.bw||d(xa.f.anim.x,n,ta,ia,ma,ga,"horizontal"),xa.f.anim.y=xa.f.anim.y*n.bw||d(xa.f.anim.y,n,ta,ia,ma,ga,"vertical"),2!=T||b){if(ya!=a){var ka=xa.r.anim.ease;wa.add(punchgs.TweenLite.set(a,xa.r.anim)),xa.r=i(),xa.r.anim.ease=ka}if(xa.f.anim.visibility="hidden",Ra.eventCallback("onStart",function(){punchgs.TweenLite.set(a,{visibility:"visible"}),a.data("iframes")&&a.find("iframe").each(function(){punchgs.TweenLite.set(jQuery(this),{autoAlpha:1})}),punchgs.TweenLite.set(L,{visibility:"visible"});var e={};e.layer=a,e.eventtype="enterstage",e.layertype=a.data("layertype"),e.layersettings=a.data(),n.c.trigger("revolution.layeraction",e)}),Ra.eventCallback("onComplete",function(){var e={};e.layer=a,e.eventtype="enteredstage",e.layertype=a.data("layertype"),e.layersettings=a.data(),n.c.trigger("revolution.layeraction",e),t.animcompleted(a,n)}),"sliderenter"==pa&&n.overcontainer&&(ca=.6),wa.add(Ra.staggerFromTo(ya,xa.f.speed,xa.f.anim,xa.r.anim,xa.f.elemdelay),ca),Ia){var _a=new Object;_a.ease=xa.r.anim.ease,_a.overflow=Ia.anim.overflow="hidden",_a.x=_a.y=0,Ia.anim.x=Ia.anim.x*n.bw||d(Ia.anim.x,n,ta,ia,ma,ga,"horizontal"),Ia.anim.y=Ia.anim.y*n.bw||d(Ia.anim.y,n,ta,ia,ma,ga,"vertical"),wa.add(punchgs.TweenLite.fromTo(C,xa.f.speed,Ia.anim,_a,La.elemdelay),ca)}else wa.add(punchgs.TweenLite.set(C,{overflow:"visible"},La.elemdelay),0)}a.data("timeline",wa),n.sliderscrope=void 0===n.sliderscrope?Math.round(99999*Math.random()):n.sliderscrope,T=h(a,n,"in"),0!==fa&&2!=T||"bytrigger"===ha||b||"sliderleave"==ha||(void 0==ha||-1!=T&&2!=T||"bytriger"===ha?punchgs.TweenLite.delayedCall(999999,t.endMoveCaption,[a,C,L,n],n.sliderscrope):punchgs.TweenLite.delayedCall(parseInt(a.data("end"),0)/1e3,t.endMoveCaption,[a,C,L,n],n.sliderscrope)),wa=a.data("timeline"),"on"==a.data("loopanimation")&&f(W,n.bw),("sliderenter"!=pa||"sliderenter"==pa&&n.overcontainer)&&(-1==T||1==T||b||0==T&&1>fa&&a.hasClass("rev-static-visbile"))&&(1>fa&&fa>0||0==fa&&"bytrigger"!=pa&&"keep"!=sa||0==fa&&"bytrigger"!=pa&&"keep"==sa&&"on"==la||"bytrigger"==pa&&"keep"==sa&&"on"==la)&&(wa.resume(va),t.toggleState(a.data("layertoggledby")))}"on"==a.data("loopanimation")&&punchgs.TweenLite.set(W,{minWidth:ta,minHeight:ia}),0==a.data("slidelink")||1!=a.data("slidelink")&&!a.hasClass("slidelink")?(punchgs.TweenLite.set(C,{width:"auto",height:"auto"}),a.data("slidelink",0)):(punchgs.TweenLite.set(C,{width:"100%",height:"100%"}),a.data("slidelink",1))},endMoveCaption:function(a,e,t,o){if(e=e||a.data("_mw"),t=t||a.data("_pw"),a.data("outstarted",1),a.data("timeline"))a.data("timeline").pause();else if(void 0===a.data("_pw"))return;var r=new punchgs.TimelineLite,p=new punchgs.TimelineLite,h=new punchgs.TimelineLite,c=s(i(),a.data("transform_in"),1==o.sdir),g=a.data("transform_out")?s(n(),a.data("transform_out"),1==o.sdir):s(n(),a.data("transform_in"),1==o.sdir),m=a.data("splitout")&&a.data("splitout").match(/words|chars|lines/g)?a.data("mySplitText")[a.data("splitout")]:a,u=void 0==a.data("endelementdelay")?0:a.data("endelementdelay"),v=a.innerWidth(),f=a.innerHeight(),w=t.position();a.data("transform_out")&&a.data("transform_out").match(/auto:auto/g)&&(c.speed=g.speed,c.anim.ease=g.anim.ease,g=c);var y=l(a.data("mask_out"));g.anim.x=g.anim.x*o.bw||d(g.anim.x,o,v,f,w.top,w.left,"horizontal"),g.anim.y=g.anim.y*o.bw||d(g.anim.y,o,v,f,w.top,w.left,"vertical"),p.eventCallback("onStart",function(){var e={};e.layer=a,e.eventtype="leavestage",e.layertype=a.data("layertype"),e.layersettings=a.data(),o.c.trigger("revolution.layeraction",e)}),p.eventCallback("onComplete",function(){punchgs.TweenLite.set(a,{visibility:"hidden"}),punchgs.TweenLite.set(t,{visibility:"hidden"});var e={};e.layer=a,e.eventtype="leftstage",e.layertype=a.data("layertype"),e.layersettings=a.data(),o.c.trigger("revolution.layeraction",e)}),r.add(p.staggerTo(m,g.speed,g.anim,u),0),y?(y.anim.ease=g.anim.ease,y.anim.overflow="hidden",y.anim.x=y.anim.x*o.bw||d(y.anim.x,o,v,f,w.top,w.left,"horizontal"),y.anim.y=y.anim.y*o.bw||d(y.anim.y,o,v,f,w.top,w.left,"vertical"),r.add(h.to(e,g.speed,y.anim,u),0)):r.add(h.set(e,{overflow:"visible",overwrite:"auto"},u),0),a.data("timeline_out",r)},removeTheCaptions:function(a,e){var i=a.data("index"),n=new Array;e.layers[i]&&jQuery.each(e.layers[i],function(a,e){n.push(e)}),e.layers["static"]&&jQuery.each(e.layers["static"],function(a,e){n.push(e)}),punchgs.TweenLite.killDelayedCallsTo(t.endMoveCaption,!1,e.sliderscrope),n&&jQuery.each(n,function(a){var i=jQuery(this),n=h(i,e,"out");0!=n&&(w(i),clearTimeout(i.data("videoplaywait")),t.stopVideo&&t.stopVideo(i,e),t.endMoveCaption(i,null,null,e),e.playingvideos=[],e.lastplayedvideos=[])})}});var i=function(){var a=new Object;return a.anim=new Object,a.anim.x=0,a.anim.y=0,a.anim.z=0,a.anim.rotationX=0,a.anim.rotationY=0,a.anim.rotationZ=0,a.anim.scaleX=1,a.anim.scaleY=1,a.anim.skewX=0,a.anim.skewY=0,a.anim.opacity=1,a.anim.transformOrigin="50% 50%",a.anim.transformPerspective=600,a.anim.rotation=0,a.anim.ease=punchgs.Power3.easeOut,a.anim.force3D="auto",a.speed=.3,a.anim.autoAlpha=1,a.anim.visibility="visible",a.anim.overwrite="all",a},n=function(){var a=new Object;return a.anim=new Object,a.anim.x=0,a.anim.y=0,a.anim.z=0,a},o=function(){var a=new Object;return a.anim=new Object,a.speed=.2,a},r=function(a,e){if(jQuery.isNumeric(parseFloat(a)))return parseFloat(a);if(void 0===a||"inherit"===a)return e;if(a.split("{").length>1){var t=a.split(","),i=parseFloat(t[1].split("}")[0]);t=parseFloat(t[0].split("{")[1]),a=Math.random()*(i-t)+t}return a},d=function(a,e,t,i,n,o,r){return!jQuery.isNumeric(a)&&a.match(/%]/g)?(a=a.split("[")[1].split("]")[0],"horizontal"==r?a=(t+2)*parseInt(a,0)/100:"vertical"==r&&(a=(i+2)*parseInt(a,0)/100)):(a="layer_left"===a?0-t:"layer_right"===a?t:a,a="layer_top"===a?0-i:"layer_bottom"===a?i:a,a="left"===a||"stage_left"===a?0-t-o:"right"===a||"stage_right"===a?e.conw-o:"center"===a||"stage_center"===a?e.conw/2-t/2-o:a,a="top"===a||"stage_top"===a?0-i-n:"bottom"===a||"stage_bottom"===a?e.conh-n:"middle"===a||"stage_middle"===a?e.conh/2-i/2-n:a),a},s=function(a,e,t){var i=new Object;if(i=jQuery.extend(!0,{},i,a),void 0===e)return i;var n=e.split(";");return n&&jQuery.each(n,function(a,e){var n=e.split(":"),o=n[0],d=n[1];t&&void 0!=d&&d.length>0&&d.match(/\(R\)/)&&(d=d.replace("(R)",""),d="right"===d?"left":"left"===d?"right":"top"===d?"bottom":"bottom"===d?"top":d,"["===d[0]&&"-"===d[1]?d=d.replace("[-","["):"["===d[0]&&"-"!==d[1]?d=d.replace("[","[-"):"-"===d[0]?d=d.replace("-",""):d[0].match(/[1-9]/)&&(d="-"+d)),void 0!=d&&(d=d.replace(/\(R\)/,""),("rotationX"==o||"rX"==o)&&(i.anim.rotationX=r(d,i.anim.rotationX)+"deg"),("rotationY"==o||"rY"==o)&&(i.anim.rotationY=r(d,i.anim.rotationY)+"deg"),("rotationZ"==o||"rZ"==o)&&(i.anim.rotation=r(d,i.anim.rotationZ)+"deg"),("scaleX"==o||"sX"==o)&&(i.anim.scaleX=r(d,i.anim.scaleX)),("scaleY"==o||"sY"==o)&&(i.anim.scaleY=r(d,i.anim.scaleY)),("opacity"==o||"o"==o)&&(i.anim.opacity=r(d,i.anim.opacity)),("skewX"==o||"skX"==o)&&(i.anim.skewX=r(d,i.anim.skewX)),("skewY"==o||"skY"==o)&&(i.anim.skewY=r(d,i.anim.skewY)),"x"==o&&(i.anim.x=r(d,i.anim.x)),"y"==o&&(i.anim.y=r(d,i.anim.y)),"z"==o&&(i.anim.z=r(d,i.anim.z)),("transformOrigin"==o||"tO"==o)&&(i.anim.transformOrigin=d.toString()),("transformPerspective"==o||"tP"==o)&&(i.anim.transformPerspective=parseInt(d,0)),("speed"==o||"s"==o)&&(i.speed=parseFloat(d)/1e3),("ease"==o||"e"==o)&&(i.anim.ease=d))}),i},l=function(a){if(void 0===a)return!1;var e=new Object;e.anim=new Object;var t=a.split(";");return t&&jQuery.each(t,function(a,t){t=t.split(":");var i=t[0],n=t[1];"x"==i&&(e.anim.x=n),"y"==i&&(e.anim.y=n),"s"==i&&(e.speed=parseFloat(n)/1e3),("e"==i||"ease"==i)&&(e.anim.ease=n)}),e},p=function(a,e,t){if(void 0==a&&(a=0),!jQuery.isArray(a)&&"string"===jQuery.type(a)&&(a.split(",").length>1||a.split("[").length>1)){a=a.replace("[",""),a=a.replace("]","");var i=a.match(/'/g)?a.split("',"):a.split(",");a=new Array,i&&jQuery.each(i,function(e,t){t=t.replace("'",""),t=t.replace("'",""),a.push(t)})}else{var n=a;jQuery.isArray(a)||(a=new Array,a.push(n))}var n=a[a.length-1];if(a.length<e.rle)for(var o=1;o<=e.curWinRange;o++)a.push(n);return a},h=function(a,e,t,i){var n=-1;if(a.hasClass("tp-static-layer")){var o=parseInt(a.data("startslide"),0),r=parseInt(a.data("endslide"),0),d=e.c.find(".processing-revslide").index(),s=-1!=d?d:e.c.find(".active-revslide").index();s=-1==s?0:s,"in"===t?a.hasClass("rev-static-visbile")?n=r==s||o>s||s>r?2:0:s>=o&&r>=s||o==s||r==s?(i||(a.addClass("rev-static-visbile"),a.removeClass("rev-static-hidden")),n=1):n=0:a.hasClass("rev-static-visbile")?o>s||s>r?(n=2,i||(a.removeClass("rev-static-visbile"),a.addClass("rev-static-hidden"))):n=0:n=2}return n},c=function(a,e){if(void 0===e)return a;e=e.replace("c:","color:"),e=e.replace("bg:","background-color:"),e=e.replace("bw:","border-width:"),e=e.replace("bc:","border-color:"),e=e.replace("br:","borderRadius:"),e=e.replace("bs:","border-style:"),e=e.replace("td:","text-decoration:");var t=e.split(";");return t&&jQuery.each(t,function(e,t){var i=t.split(":");i[0].length>0&&(a.anim[i[0]]=i[1])}),a},g=function(a,e){var t,i=new Object,n=!1;if("rekursive"==e&&(t=a.closest(".tp-caption"),t&&a.css("fontSize")===t.css("fontSize")&&(n=!0)),i.basealign=a.data("basealign")||"grid",i.fontSize=n?void 0===t.data("fontsize")?parseInt(t.css("fontSize"),0)||0:t.data("fontsize"):void 0===a.data("fontsize")?parseInt(a.css("fontSize"),0)||0:a.data("fontsize"),i.fontWeight=n?void 0===t.data("fontweight")?parseInt(t.css("fontWeight"),0)||0:t.data("fontweight"):void 0===a.data("fontweight")?parseInt(a.css("fontWeight"),0)||0:a.data("fontweight"),i.whiteSpace=n?void 0===t.data("whitespace")?t.css("whitespace")||"normal":t.data("whitespace"):void 0===a.data("whitespace")?a.css("whitespace")||"normal":a.data("whitespace"),i.lineHeight=n?void 0===t.data("lineheight")?parseInt(t.css("lineHeight"),0)||0:t.data("lineheight"):void 0===a.data("lineheight")?parseInt(a.css("lineHeight"),0)||0:a.data("lineheight"),i.letterSpacing=n?void 0===t.data("letterspacing")?parseFloat(t.css("letterSpacing"),0)||0:t.data("letterspacing"):void 0===a.data("letterspacing")?parseFloat(a.css("letterSpacing"))||0:a.data("letterspacing"),i.paddingTop=void 0===a.data("paddingtop")?parseInt(a.css("paddingTop"),0)||0:a.data("paddingtop"),i.paddingBottom=void 0===a.data("paddingbottom")?parseInt(a.css("paddingBottom"),0)||0:a.data("paddingbottom"),i.paddingLeft=void 0===a.data("paddingleft")?parseInt(a.css("paddingLeft"),0)||0:a.data("paddingleft"),i.paddingRight=void 0===a.data("paddingright")?parseInt(a.css("paddingRight"),0)||0:a.data("paddingright"),i.marginTop=void 0===a.data("margintop")?parseInt(a.css("marginTop"),0)||0:a.data("margintop"),i.marginBottom=void 0===a.data("marginbottom")?parseInt(a.css("marginBottom"),0)||0:a.data("marginbottom"),i.marginLeft=void 0===a.data("marginleft")?parseInt(a.css("marginLeft"),0)||0:a.data("marginleft"),i.marginRight=void 0===a.data("marginright")?parseInt(a.css("marginRight"),0)||0:a.data("marginright"),i.borderTopWidth=void 0===a.data("bordertopwidth")?parseInt(a.css("borderTopWidth"),0)||0:a.data("bordertopwidth"),i.borderBottomWidth=void 0===a.data("borderbottomwidth")?parseInt(a.css("borderBottomWidth"),0)||0:a.data("borderbottomwidth"),i.borderLeftWidth=void 0===a.data("borderleftwidth")?parseInt(a.css("borderLeftWidth"),0)||0:a.data("borderleftwidth"),i.borderRightWidth=void 0===a.data("borderrightwidth")?parseInt(a.css("borderRightWidth"),0)||0:a.data("borderrightwidth"),"rekursive"!=e){if(i.color=void 0===a.data("color")?"nopredefinedcolor":a.data("color"),i.whiteSpace=n?void 0===t.data("whitespace")?t.css("whiteSpace")||"nowrap":t.data("whitespace"):void 0===a.data("whitespace")?a.css("whiteSpace")||"nowrap":a.data("whitespace"),i.minWidth=void 0===a.data("width")?parseInt(a.css("minWidth"),0)||0:a.data("width"),i.minHeight=void 0===a.data("height")?parseInt(a.css("minHeight"),0)||0:a.data("height"),void 0!=a.data("videowidth")&&void 0!=a.data("videoheight")){var o=a.data("videowidth"),r=a.data("videoheight");o="100%"===o?"none":o,r="100%"===r?"none":r,a.data("width",o),a.data("height",r)}i.maxWidth=void 0===a.data("width")?parseInt(a.css("maxWidth"),0)||"none":a.data("width"),i.maxHeight=void 0===a.data("height")?parseInt(a.css("maxHeight"),0)||"none":a.data("height"),i.wan=void 0===a.data("wan")?parseInt(a.css("-webkit-transition"),0)||"none":a.data("wan"),i.moan=void 0===a.data("moan")?parseInt(a.css("-moz-animation-transition"),0)||"none":a.data("moan"),i.man=void 0===a.data("man")?parseInt(a.css("-ms-animation-transition"),0)||"none":a.data("man"),i.ani=void 0===a.data("ani")?parseInt(a.css("transition"),0)||"none":a.data("ani")}return i.styleProps=a.css(["background-color","border-top-color","border-bottom-color","border-right-color","border-left-color","border-top-style","border-bottom-style","border-left-style","border-right-style","border-left-width","border-right-width","border-bottom-width","border-top-width","color","text-decoration","font-style","border-radius"]),i},m=function(a,e){var t=new Object;return a&&jQuery.each(a,function(i,n){t[i]=p(n,e)[e.curWinRange]||a[i]}),t},u=function(a,e,t,i){return a=jQuery.isNumeric(a)?a*e+"px":a,a="full"===a?i:"auto"===a||"none"===a?t:a},v=function(a,e,t,i){var n;void 0===a.data("cssobj")?(n=g(a,t),a.data("cssobj",n)):n=a.data("cssobj");var o=m(n,e),r=e.bw,d=e.bh;if("off"===i&&(r=1,d=1),"auto"==o.lineHeight&&(o.lineHeight=o.fontSize+4),!a.hasClass("tp-splitted")){a.css("-webkit-transition","none"),a.css("-moz-transition","none"),a.css("-ms-transition","none"),a.css("transition","none");var s=void 0!==a.data("transform_hover")||void 0!==a.data("style_hover");if(s&&punchgs.TweenLite.set(a,o.styleProps),punchgs.TweenLite.set(a,{fontSize:Math.round(o.fontSize*r)+"px",fontWeight:o.fontWeight,letterSpacing:Math.floor(o.letterSpacing*r)+"px",paddingTop:Math.round(o.paddingTop*d)+"px",paddingBottom:Math.round(o.paddingBottom*d)+"px",paddingLeft:Math.round(o.paddingLeft*r)+"px",paddingRight:Math.round(o.paddingRight*r)+"px",marginTop:o.marginTop*d+"px",marginBottom:o.marginBottom*d+"px",marginLeft:o.marginLeft*r+"px",marginRight:o.marginRight*r+"px",borderTopWidth:Math.round(o.borderTopWidth*d)+"px",borderBottomWidth:Math.round(o.borderBottomWidth*d)+"px",borderLeftWidth:Math.round(o.borderLeftWidth*r)+"px",borderRightWidth:Math.round(o.borderRightWidth*r)+"px",lineHeight:Math.round(o.lineHeight*d)+"px",overwrite:"auto"}),"rekursive"!=t){var l="slide"==o.basealign?e.ulw:e.gridwidth[e.curWinRange],p="slide"==o.basealign?e.ulh:e.gridheight[e.curWinRange],h=u(o.maxWidth,r,"none",l),c=u(o.maxHeight,d,"none",p),v=u(o.minWidth,r,"0px",l),f=u(o.minHeight,d,"0px",p);punchgs.TweenLite.set(a,{maxWidth:h,maxHeight:c,minWidth:v,minHeight:f,whiteSpace:o.whiteSpace,overwrite:"auto"}),"nopredefinedcolor"!=o.color&&punchgs.TweenLite.set(a,{color:o.color,overwrite:"auto"})}setTimeout(function(){a.css("-webkit-transition",a.data("wan")),a.css("-moz-transition",a.data("moan")),a.css("-ms-transition",a.data("man")),a.css("transition",a.data("ani"))},30)}},f=function(a,e){if(a.hasClass("rs-pendulum")&&void 0==a.data("loop-timeline")){a.data("loop-timeline",new punchgs.TimelineLite);var t=void 0==a.data("startdeg")?-20:a.data("startdeg"),i=void 0==a.data("enddeg")?20:a.data("enddeg"),n=void 0==a.data("speed")?2:a.data("speed"),o=void 0==a.data("origin")?"50% 50%":a.data("origin"),r=void 0==a.data("easing")?punchgs.Power2.easeInOut:a.data("ease");t*=e,i*=e,a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",rotation:t,transformOrigin:o},{rotation:i,ease:r})),a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",rotation:i,transformOrigin:o},{rotation:t,ease:r,onComplete:function(){a.data("loop-timeline").restart()}}))}if(a.hasClass("rs-rotate")&&void 0==a.data("loop-timeline")){a.data("loop-timeline",new punchgs.TimelineLite);var t=void 0==a.data("startdeg")?0:a.data("startdeg"),i=void 0==a.data("enddeg")?360:a.data("enddeg");n=void 0==a.data("speed")?2:a.data("speed"),o=void 0==a.data("origin")?"50% 50%":a.data("origin"),r=void 0==a.data("easing")?punchgs.Power2.easeInOut:a.data("easing"),t*=e,i*=e,a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",rotation:t,transformOrigin:o},{rotation:i,ease:r,onComplete:function(){a.data("loop-timeline").restart()}}))}if(a.hasClass("rs-slideloop")&&void 0==a.data("loop-timeline")){a.data("loop-timeline",new punchgs.TimelineLite);var d=void 0==a.data("xs")?0:a.data("xs"),s=void 0==a.data("ys")?0:a.data("ys"),l=void 0==a.data("xe")?0:a.data("xe"),p=void 0==a.data("ye")?0:a.data("ye"),n=void 0==a.data("speed")?2:a.data("speed"),r=void 0==a.data("easing")?punchgs.Power2.easeInOut:a.data("easing");d*=e,s*=e,l*=e,p*=e,a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",x:d,y:s},{x:l,y:p,ease:r})),a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",x:l,y:p},{x:d,y:s,onComplete:function(){a.data("loop-timeline").restart()}}))}if(a.hasClass("rs-pulse")&&void 0==a.data("loop-timeline")){a.data("loop-timeline",new punchgs.TimelineLite);var h=void 0==a.data("zoomstart")?0:a.data("zoomstart"),c=void 0==a.data("zoomend")?0:a.data("zoomend"),n=void 0==a.data("speed")?2:a.data("speed"),r=void 0==a.data("easing")?punchgs.Power2.easeInOut:a.data("easing");a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",scale:h},{scale:c,ease:r})),a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(a,n,{force3D:"auto",scale:c},{scale:h,onComplete:function(){a.data("loop-timeline").restart()}}))}if(a.hasClass("rs-wave")&&void 0==a.data("loop-timeline")){a.data("loop-timeline",new punchgs.TimelineLite);var g=void 0==a.data("angle")?10:parseInt(a.data("angle"),0),m=void 0==a.data("radius")?10:parseInt(a.data("radius"),0),n=void 0==a.data("speed")?-20:a.data("speed"),o=void 0==a.data("origin")?"50% 50%":a.data("origin"),u=o.split(" "),v=new Object;u.length>=1?(v.x=u[0],v.y=u[1]):(v.x="50%",v.y="50%"),g*=e,m*=e;var f=0-a.height()/2+m*(-1+parseInt(v.y,0)/100),w=a.width()*(-.5+parseInt(v.x,0)/100),y={a:0,ang:g,element:a,unit:m,xoffset:w,yoffset:f};a.data("loop-timeline").append(new punchgs.TweenLite.fromTo(y,n,{a:360},{a:0,force3D:"auto",ease:punchgs.Linear.easeNone,onUpdate:function(){var a=y.a*(Math.PI/180);punchgs.TweenLite.to(y.element,.1,{force3D:"auto",x:y.xoffset+Math.cos(a)*y.unit,y:y.yoffset+y.unit*(1-Math.sin(a))})},onComplete:function(){a.data("loop-timeline").restart()}}))}},w=function(a){a.find(".rs-pendulum, .rs-slideloop, .rs-pulse, .rs-wave").each(function(){var a=jQuery(this);void 0!=a.data("loop-timeline")&&(a.data("loop-timeline").pause(),a.data("loop-timeline",null))})}}(jQuery);