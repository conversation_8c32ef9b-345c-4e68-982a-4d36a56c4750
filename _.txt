Microcontroller/Microprocessors

A microprocessor is a silicon chip representing a central processing unit.

A microprocessor is a dependent unit and it requires the combination of other
hardware like memory, timer unit, and interrupt controller, etc. for proper
functioning. Ex. 8085 microprocessor

A microcontroller is a highly integrated chip that contains a CPU, scratch pad RAM,
special and general purpose register arrays,on chip ROM/FLASH memory for
program storage , timer and interrupt control units and dedicated I/O ports.

Microcontrollers are designed for either general purpose application requirement
or domain specific application requirement. Ex. 8051 microcontroller

The Intel 8051 microcontroller is one of the most popular general purpose
microcontrollers in use today. The success of the Intel 8051 spawned a number of
clones (also called derivatives) which are collectively referred to as the MCS-51
family of microcontrollers, which includes chips from vendors such as Atmel,
Philips, Infineon, and Texas Instruments.

8051 Variants

The 8051 has the widest range of variants of any embedded controller in the
market.

The smallest device is the Atmel 89c1051, a 20 pin flash variant with 2 timers,
UART, 20mA.

The fastest parts are from Dallas, with performance close to 10 MIPS.

The most powerful chip is the Siemens 80C517A, with 32-bit ALU, 2 UARTs, 2K
RAM, PLCC84 package, 8×16 bit PWMs and other features.


Intel 8051

ROM [ bytes] 4K
RAM [bytes] 128
Timers 2
I/O pins 32
Serial port 1
Interrupt sources 6



Basic features of 8051

The 8051 is the original member of the MCS-51 family, and is the core for all MCS-51
devices. The features of the 8051 core are-
• 8-bit CPU optimized for control applications
• Sixteen bit program counter (PC) and data pointer (DPTR).
• Eight bit program status word (PSW).
• Eight bit stack pointer (SP).
• Extensive Boolean processing (single-bit logic) capabilities
• 64K program memory address space
• 64K data memory address space
• 4K bytes of on-chip program memory
• 128 bytes of on-chip data RAM

 Four register banks, each containing eight registers.
 Sixteen bytes, of bit addressable memory locations.Eighty bytes of general-purpose data memory.

• 32 bidirectional and individually addressable I/O lines(4 groups of eight)
• Two 16 bit timers/counters
• Full duplex 8-bit serial data transmitter/receiver (UART)
• Control registers: TCON, TMOD, SCON, PCON, IP and IE among SFRs
• Thirty two I/O pins arranged as four 8-bit ports P0, P1,P2 and P3 addressed as
PX.0-PX.7.
• Two external and three internal interrupt sources.
• 6- source/5-vector interrupt structure with two priority levels
• On-chip clock oscillator clock circuits


Resistors: (Color code and types)


 A resistor is a passive two-terminal electrical component that
implements electrical resistance as a circuit element. Resistors act to
reduce current flow, and, at the same time, act to lower voltage levels
within circuits. In electronic circuits resistors are used to limit current
flow, to adjust signal levels, bias active elements, terminate transmission
lines among other uses.


 Resistors are common elements of electrical networks and electronic
circuitsand are ubiquitous in electronic equipment. Practical resistors as
discrete components can be composed of various compounds and forms.
Resistors are also implemented within integrated circuits.


 The electrical function of a resistor is specified by its resistance: common
commercial resistors are manufactured over a range of more than
nine orders of magnitude. The nominal value of the resistance will fall
within a manufacturing tolerance.




Capacitors:

 A capacitor (originally known as a condenser) is a passive two-
terminal electrical component used to store energy electrostatically in
an electric field. The forms of practical capacitors vary widely, but all contain
at least two electrical conductors (plates) separated by
adielectric (i.e. insulator).

 When there is a potential difference across the conductors (e.g., when a
capacitor is attached across a battery), an electric field develops across the
dielectric, causing positive charge +Q to collect on one plate and negative
charge −Q to collect on the other plate.

 Capacitors are widely used in electronic circuits for blocking direct
current while allowing alternating current to pass. In analog filter networks,
they smooth the output of power supplies. In resonant circuits they
tune radios to particular frequencies. In electric power transmission systems,
they stabilize voltage and power flow.





Inductors:

 An inductor, also called a coil or reactor, is a passive two-terminal electrical
component which resists changes in electric current passing through it. It
consists of a conductor such as a wire, usually wound into a coil. When a
current flows through it, energy is stored temporarily in a magnetic field in
the coil.
 When the current flowing through an inductor changes, the time-varying
magnetic field induces a voltage in the conductor, according to Faraday’s law
of electromagnetic induction, which opposes the change in current that
created it.




LEDs/Displays

 A light-emitting diode (LED) is a two-lead semiconductorlight source. It is a basic
pn-junction diode, which emits light when activated.[7] When a suitable voltage is
applied to the leads, electrons are able to recombine with electron holes within
the device, releasing energy in the form of photons. This effect is called
electroluminescence, and the color of the light (corresponding to the energy of
the photon) is determined by the energy band gap of the semiconductor.

 An LED is often small in area (less than 1 mm2) and integrated optical
components may be used to shape its radiation pattern.

Alphanumeric displays are available in seven-segment, starburst and dot-matrix
format. Seven-segment displays handle all numbers and a limited set of letters.
Starburst displays can display all letters. Dot-matrix displays typically use 5x7 pixels per
character



Diodes

In electronics, a diode is a two-terminal electronic component with asymmetric
conductance; it has low (ideally zero) resistance to current in one direction, and
high (ideally infinite) resistance in the other. A semiconductor diode, the most
common type today, is a crystalline piece of semiconductor material with a p–n
junction connected to two electrical terminals.

The most common function of a diode is to allow an electric current to pass in
one direction (called the diode's forward direction), while blocking current in the
opposite direction (the reverse direction). Thus, the diode can be viewed as an
electronic version of a check valve. This unidirectional behavior is
called rectification, and is used to convert alternating current to direct current,
including extraction of modulation from radio signals in radio receivers—these
diodes are forms of rectifiers.



Transistors

 A transistor is a semiconductor device used
to amplify and switch electronic signals and electrical power.

 It is composed of semiconductor material with at least three terminals for
connection to an external circuit.

 A voltage or current applied to one pair of the transistor's terminals changes the
current through another pair of terminals. Because the controlled
(output) power can be higher than the controlling (input) power, a transistor
can amplify a signal.





Sensors

A sensor is a device that detects events or changes in quantities and
provides a corresponding output, generally as an electrical or optical signal;
for example, a thermocouple converts temperature to an output voltage.

A sensor's sensitivity indicates how much the sensor's output changes when
the input quantity being measured changes.

The sensitivity is then defined as the ratio between output signal and
measured property. For example, if a sensor measures temperature and has
a voltage output, the sensitivity is a constant with the unit [V/K]; this sensor
is linear because the ratio is constant at all points of measurement.




Actuators

An actuator is a type of motor that is responsible for moving or controlling a
mechanism or system.

It is operated by a source of energy, typically electric current, hydraulic
fluid pressure, or pneumatic pressure, and converts that energy into motion.
An actuator is the mechanism by which a control system acts upon an 
environment. The control system can be simple (a fixed mechanical or
electronic system), software-based (e.g. a printer driver, robot control
system), a human, or any other input. 



Crystal oscillator

 A crystal oscillator is an electronic oscillator circuit that uses the mechanical
resonance of a vibrating crystal of piezoelectric material to create an
electrical signal with a very precise frequency.

 The most common type of piezoelectric resonator used is the quartz crystal,
so oscillator circuits incorporating them became known as crystal
oscillators, but other piezoelectric materials including polycrystalline
ceramics are used in similar circuits.

 A crystal is a solid in which the constituent atoms, molecules, or ions are
packed in a regularly ordered, repeating pattern extending in all three spatial
dimensions.




Operational Amplifiers:

An operational amplifier (op-amp) is a DC-coupled high-gain electronic
voltage amplifier with a differential input and, usually, a single-ended output.

In this configuration, an op-amp produces an output potential (relative to circuit
ground) that is typically hundreds of thousands of times larger than the
potential difference between its input terminals.




ADC, DAC

An analog-to-digital converter (ADC) is a device that converts a continuous
physical quantity (usually voltage) to a digital number that represents the
quantity's amplitude.


An ADC is defined by its bandwidth and its signal to noise. The actual bandwidth
of an ADC is characterized primarily by its sampling rate, and to a lesser extent
by how it handles errors such as aliasing. The dynamic range of an ADC is
influenced by many factors, including the resolution, linearity and accuracy
and jitter . The dynamic range of an ADC is often summarized in terms of
its effective number of bits (ENOB), the number of bits of each measure it
returns that are on average not noise.

In electronics, a digital-to-analog converter (DAC) is a function that converts
digital data into an analog signal.DACs are commonly used in music players to
convert digital data streams into analog audio signals. They are also used in
televisions and mobile phones to convert digital video data into analog video
signals which connect to the screen drivers to display monochrome or color
images. These two applications use DACs at opposite ends of the
speed/resolution trade-off. The audio DAC is a low speed high resolution type
while the video DAC is a high speed low to medium resolution type.
